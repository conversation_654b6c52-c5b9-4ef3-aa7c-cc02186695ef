#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试根据角色权限key获取客服用户列表功能
"""

import asyncio
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from config.database import async_engine
from module_admin.dao.role_dao import RoleD<PERSON>
from module_admin.service.role_service import RoleService
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.do.user_do import SysUser, SysUserRole
from sqlalchemy import select


class TestCustomerSupportRoleKey:
    """
    测试客服角色权限key功能
    """

    @pytest.mark.asyncio
    async def test_get_users_by_role_key_dao(self):
        """
        测试根据角色权限key获取用户列表的DAO方法
        """
        async with async_engine.begin() as conn:
            async_session = AsyncSession(conn)
            
            # 测试获取customer-support权限的用户
            users = await RoleDao.get_users_by_role_key_dao(async_session, 'customer-support')
            
            # 验证返回结果
            assert isinstance(users, list)
            print(f"找到 {len(users)} 个客服用户")
            
            # 如果有用户，验证用户数据结构
            if users:
                user = users[0]
                assert 'user_id' in user
                assert 'user_name' in user
                assert 'nick_name' in user
                print(f"第一个客服用户: {user['user_name']} ({user['nick_name']})")

    @pytest.mark.asyncio
    async def test_get_users_by_role_key_service(self):
        """
        测试根据角色权限key获取用户列表的Service方法
        """
        async with async_engine.begin() as conn:
            async_session = AsyncSession(conn)
            
            # 测试获取customer-support权限的用户
            users = await RoleService.get_users_by_role_key_services(async_session, 'customer-support')
            
            # 验证返回结果
            assert isinstance(users, list)
            print(f"Service层找到 {len(users)} 个客服用户")
            
            # 如果有用户，验证用户数据结构（应该是驼峰命名）
            if users:
                user = users[0]
                # 验证驼峰命名转换
                assert hasattr(user, 'userId')
                assert hasattr(user, 'userName')
                assert hasattr(user, 'nickName')
                print(f"第一个客服用户: {user.userName} ({user.nickName})")

    @pytest.mark.asyncio
    async def test_check_customer_support_role_exists(self):
        """
        检查数据库中是否存在customer-support角色
        """
        async with async_engine.begin() as conn:
            async_session = AsyncSession(conn)
            
            # 查询customer-support角色
            result = await async_session.execute(
                select(SysRole).where(
                    SysRole.role_key == 'customer-support',
                    SysRole.status == '0',
                    SysRole.del_flag == '0'
                )
            )
            role = result.scalar_one_or_none()
            
            if role:
                print(f"找到客服角色: {role.role_name} (权限key: {role.role_key})")
                
                # 查询该角色下的用户数量
                user_count_result = await async_session.execute(
                    select(SysUser)
                    .join(SysUserRole, SysUser.user_id == SysUserRole.user_id)
                    .where(
                        SysUserRole.role_id == role.role_id,
                        SysUser.status == '0',
                        SysUser.del_flag == '0'
                    )
                )
                users = user_count_result.scalars().all()
                print(f"该角色下有 {len(users)} 个用户")
            else:
                print("警告: 数据库中未找到customer-support角色")
                print("请确保已创建具有customer-support权限key的角色")


if __name__ == '__main__':
    # 运行测试
    asyncio.run(TestCustomerSupportRoleKey().test_check_customer_support_role_exists())
    asyncio.run(TestCustomerSupportRoleKey().test_get_users_by_role_key_dao())
    asyncio.run(TestCustomerSupportRoleKey().test_get_users_by_role_key_service())
    print("所有测试完成")