"""
用户模块测试
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import SysUser
from app.utils.pwd_util import PwdUtil


@pytest.mark.asyncio
async def test_get_user_profile(client: TestClient, db: AsyncSession):
    """
    测试获取用户信息

    :param client: 测试客户端
    :param db: 测试数据库会话
    :return: None
    """
    # 创建测试用户
    test_user = SysUser(
        user_name="testuser",
        nick_name="Test User",
        password=PwdUtil.get_password_hash("password123"),
        status="0",
        del_flag="0",
    )
    db.add(test_user)
    await db.commit()

    # 登录获取token
    response = client.post(
        "/api/v1/auth/token",
        data={"username": "testuser", "password": "password123"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]

    # 测试获取用户信息
    response = client.get(
        "/api/v1/user/profile",
        headers={"Authorization": f"Bearer {token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "data" in data
    assert "user_basic_info" in data["data"]
    assert data["data"]["user_basic_info"]["user_name"] == "testuser"


@pytest.mark.asyncio
async def test_get_user_list(client: TestClient, db: AsyncSession):
    """
    测试获取用户列表

    :param client: 测试客户端
    :param db: 测试数据库会话
    :return: None
    """
    # 创建测试用户
    test_user = SysUser(
        user_name="testuser",
        nick_name="Test User",
        password=PwdUtil.get_password_hash("password123"),
        status="0",
        del_flag="0",
    )
    db.add(test_user)
    await db.commit()

    # 登录获取token
    response = client.post(
        "/api/v1/auth/token",
        data={"username": "testuser", "password": "password123"},
    )
    assert response.status_code == 200
    token = response.json()["access_token"]

    # 测试获取用户列表
    response = client.get(
        "/api/v1/user/list",
        headers={"Authorization": f"Bearer {token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "data" in data
    assert "rows" in data["data"]
    assert len(data["data"]["rows"]) > 0
