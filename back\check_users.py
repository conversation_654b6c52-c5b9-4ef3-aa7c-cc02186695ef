#!/usr/bin/env python3
"""
检查数据库中存在的用户ID
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.database import AsyncSessionLocal

async def check_existing_users():
    """检查现有用户"""
    async with AsyncSessionLocal() as db:
        try:
            # 获取前10个用户
            result = await db.execute(
                text("SELECT user_id, user_name, nick_name FROM sys_user WHERE del_flag = '0' LIMIT 10")
            )
            users = result.fetchall()
            
            print("数据库中存在的用户:")
            for user in users:
                print(f"ID: {user.user_id}, 用户名: {user.user_name}, 昵称: {user.nick_name}")
                
            return users
            
        except Exception as e:
            print(f"查询用户失败: {e}")
            return []

if __name__ == "__main__":
    asyncio.run(check_existing_users())