"""
设备管理模块新功能单元测试
测试校准内容和附件功能
"""
import pytest
import asyncio
import os
import tempfile
from datetime import datetime, date
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# 模拟FastAPI的UploadFile
class MockUploadFile:
    def __init__(self, filename: str, content: bytes, content_type: str = "application/pdf"):
        self.filename = filename
        self.content = content
        self.content_type = content_type
        self.size = len(content)
    
    async def read(self):
        return self.content


class TestEquipmentManagementNewFeatures:
    """设备管理新功能测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.attachments_dir = self.test_dir / "attachedFiles" / "equipment"
        self.attachments_dir.mkdir(parents=True, exist_ok=True)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_file_manager_generate_unique_filename(self):
        """测试唯一文件名生成"""
        from utils.file_utils import FileManager
        
        file_manager = FileManager(str(self.test_dir))
        
        # 测试PDF文件
        filename1 = file_manager.generate_unique_filename("test.pdf")
        filename2 = file_manager.generate_unique_filename("test.pdf")
        
        assert filename1 != filename2
        assert filename1.startswith("ZJQS_")
        assert filename1.endswith(".pdf")
        assert len(filename1.split("_")) == 3  # ZJQS_timestamp_random.pdf
        
        # 测试无扩展名文件
        filename3 = file_manager.generate_unique_filename("test")
        assert filename3.startswith("ZJQS_")
        assert not filename3.endswith(".")
        
        print(f"✅ 唯一文件名生成测试通过: {filename1}, {filename2}, {filename3}")
    
    @pytest.mark.asyncio
    async def test_file_manager_save_file(self):
        """测试文件保存功能"""
        from utils.file_utils import FileManager
        
        file_manager = FileManager(str(self.test_dir))
        
        # 创建模拟文件
        test_content = b"This is a test PDF content"
        mock_file = MockUploadFile("test_document.pdf", test_content)
        
        # 保存文件
        file_info = await file_manager.save_file(mock_file, "equipment")
        
        # 验证返回信息
        assert file_info["file_name"] == "test_document.pdf"
        assert file_info["file_size"] == len(test_content)
        assert file_info["unique_id"].startswith("ZJQS_")
        assert file_info["unique_id"].endswith(".pdf")
        assert "equipment/" in file_info["file_path"]
        assert isinstance(file_info["upload_time"], datetime)
        
        # 验证文件实际保存
        saved_file_path = self.test_dir / file_info["file_path"]
        assert saved_file_path.exists()
        
        with open(saved_file_path, "rb") as f:
            saved_content = f.read()
        assert saved_content == test_content
        
        print(f"✅ 文件保存测试通过: {file_info}")
    
    @pytest.mark.asyncio
    async def test_file_manager_save_multiple_files(self):
        """测试批量文件保存"""
        from utils.file_utils import FileManager
        
        file_manager = FileManager(str(self.test_dir))
        
        # 创建多个模拟文件
        files = [
            MockUploadFile("doc1.pdf", b"PDF content 1"),
            MockUploadFile("doc2.docx", b"Word content 2", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
            MockUploadFile("image.jpg", b"Image content 3", "image/jpeg")
        ]
        
        # 批量保存
        file_infos = await file_manager.save_multiple_files(files, "equipment")
        
        # 验证结果
        assert len(file_infos) == 3
        
        for i, file_info in enumerate(file_infos):
            assert file_info["file_name"] == files[i].filename
            assert file_info["file_size"] == files[i].size
            
            # 验证文件存在
            saved_file_path = self.test_dir / file_info["file_path"]
            assert saved_file_path.exists()
        
        print(f"✅ 批量文件保存测试通过: {len(file_infos)} 个文件")
    
    def test_file_manager_delete_file(self):
        """测试文件删除功能"""
        from utils.file_utils import FileManager
        
        file_manager = FileManager(str(self.test_dir))
        
        # 创建测试文件
        test_file_path = self.attachments_dir / "test_file.txt"
        test_file_path.write_text("test content")
        
        # 删除文件
        relative_path = "equipment/test_file.txt"
        result = file_manager.delete_file(relative_path)
        
        assert result is True
        assert not test_file_path.exists()
        
        # 测试删除不存在的文件
        result2 = file_manager.delete_file("equipment/nonexistent.txt")
        assert result2 is False
        
        print("✅ 文件删除测试通过")
    
    def test_file_manager_file_operations(self):
        """测试文件操作功能"""
        from utils.file_utils import FileManager
        
        file_manager = FileManager(str(self.test_dir))
        
        # 创建测试文件
        test_file_path = self.attachments_dir / "test_operations.txt"
        test_content = "test operations content"
        test_file_path.write_text(test_content)
        
        relative_path = "equipment/test_operations.txt"
        
        # 测试文件存在检查
        assert file_manager.file_exists(relative_path) is True
        assert file_manager.file_exists("equipment/nonexistent.txt") is False
        
        # 测试获取文件路径
        full_path = file_manager.get_file_path(relative_path)
        assert full_path == test_file_path
        
        # 测试获取文件信息
        file_info = file_manager.get_file_info(relative_path)
        assert file_info is not None
        assert file_info["file_name"] == "test_operations.txt"
        assert file_info["file_path"] == relative_path
        assert file_info["file_size"] == len(test_content)
        assert isinstance(file_info["create_time"], datetime)
        assert isinstance(file_info["modify_time"], datetime)
        
        print("✅ 文件操作测试通过")
    
    def test_attachment_model_validation(self):
        """测试附件模型验证"""
        from module_equipment_management.entity.vo.equipment_management_vo import AttachmentModel
        from pydantic import ValidationError
        
        # 测试有效数据
        valid_attachment = AttachmentModel(
            file_name="test.pdf",
            file_path="equipment/ZJQS_1234567890_1234.pdf",
            file_size=1024,
            upload_time=datetime.now(),
            unique_id="ZJQS_1234567890_1234.pdf"
        )
        
        assert valid_attachment.file_name == "test.pdf"
        assert valid_attachment.file_size == 1024
        
        # 测试必填字段验证
        with pytest.raises(ValidationError):
            AttachmentModel(
                file_path="equipment/test.pdf",
                unique_id="ZJQS_1234567890_1234.pdf"
                # 缺少 file_name
            )
        
        print("✅ 附件模型验证测试通过")
    
    def test_equipment_model_with_new_fields(self):
        """测试设备模型新字段"""
        from module_equipment_management.entity.vo.equipment_management_vo import (
            AddEquipmentManagementModel, 
            AttachmentModel
        )
        
        # 创建附件
        attachment = AttachmentModel(
            file_name="calibration_cert.pdf",
            file_path="equipment/ZJQS_1234567890_1234.pdf",
            file_size=2048,
            upload_time=datetime.now(),
            unique_id="ZJQS_1234567890_1234.pdf"
        )
        
        # 创建设备模型
        equipment = AddEquipmentManagementModel(
            equipment_number="EQ001",
            equipment_name="测试设备",
            calibration_content="校准内容：检查设备精度和稳定性",
            attachments=[attachment]
        )
        
        assert equipment.equipment_number == "EQ001"
        assert equipment.calibration_content == "校准内容：检查设备精度和稳定性"
        assert len(equipment.attachments) == 1
        assert equipment.attachments[0].file_name == "calibration_cert.pdf"
        
        print("✅ 设备模型新字段测试通过")
    
    def test_unique_filename_format(self):
        """测试唯一文件名格式"""
        from utils.file_utils import FileManager
        import re
        
        file_manager = FileManager(str(self.test_dir))
        
        # 测试多个文件名
        test_files = [
            "document.pdf",
            "image.jpg", 
            "spreadsheet.xlsx",
            "presentation.pptx",
            "archive.zip"
        ]
        
        pattern = r"^ZJQS_\d{10}_\d{4}\.(pdf|jpg|xlsx|pptx|zip)$"
        
        for original_filename in test_files:
            unique_filename = file_manager.generate_unique_filename(original_filename)
            
            # 验证格式
            assert re.match(pattern, unique_filename), f"文件名格式不正确: {unique_filename}"
            
            # 验证组成部分
            parts = unique_filename.split("_")
            assert parts[0] == "ZJQS"
            assert len(parts[1]) == 10  # 时间戳
            assert len(parts[2].split(".")[0]) == 4  # 随机数字
        
        print("✅ 唯一文件名格式测试通过")


def run_tests():
    """运行所有测试"""
    print("🚀 开始运行设备管理新功能单元测试...")
    
    test_instance = TestEquipmentManagementNewFeatures()
    
    try:
        # 运行同步测试
        test_instance.setup_method()
        test_instance.test_file_manager_generate_unique_filename()
        test_instance.test_file_manager_delete_file()
        test_instance.test_file_manager_file_operations()
        test_instance.test_attachment_model_validation()
        test_instance.test_equipment_model_with_new_fields()
        test_instance.test_unique_filename_format()
        test_instance.teardown_method()
        
        # 运行异步测试
        async def run_async_tests():
            test_instance.setup_method()
            await test_instance.test_file_manager_save_file()
            await test_instance.test_file_manager_save_multiple_files()
            test_instance.teardown_method()
        
        asyncio.run(run_async_tests())
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_tests()
