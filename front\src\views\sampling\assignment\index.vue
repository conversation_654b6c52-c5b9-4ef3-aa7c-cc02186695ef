<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" size="small" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Refresh"
          size="small"
          @click="getList"
        >刷新</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 已审批项目报价列表 -->
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span class="card-title">已审批项目报价列表</span>
          <span class="card-subtitle">点击"采样分配"按钮为项目报价分配采样任务</span>
        </div>
      </template>
      
      <el-table v-loading="loading" :data="quotationList">
        <el-table-column label="项目编号" align="center" prop="projectCode" width="120" />
        <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
        <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
        <el-table-column label="客户联系人" align="center" prop="customerContact" width="100" />
        <el-table-column label="客户电话" align="center" prop="customerPhone" width="120" />
        <el-table-column label="委托日期" align="center" prop="commissionDate" width="100">
          <template #default="scope">
            <span>{{ getFormattedCommissionDate(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目经理" align="center" prop="projectManager" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ getFormattedCreateTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="link"
              icon="Operation"
              @click="handleAssignment(scope.row)"
              v-hasPermi="['sampling:assignment:assign']"
            >采样分配</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 采样任务分配弹窗 -->
    <el-dialog title="采样任务分配" v-model="assignmentOpen" width="900px" append-to-body>
      <div v-if="selectedQuotation">
        <div class="assignment-header">
          <h3>{{ selectedQuotation.projectName }} ({{ selectedQuotation.projectCode }})</h3>
          <p class="text-muted">客户：{{ selectedQuotation.customerName }}</p>
        </div>

        <el-form :model="assignmentForm" ref="assignmentForm" :rules="assignmentRules" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="任务名称" prop="taskName">
                <el-input v-model="assignmentForm.taskName" placeholder="请输入任务名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分配用户" prop="assignedUserId">
                <el-select v-model="assignmentForm.assignedUserId" placeholder="请选择分配用户" clearable style="width: 100%">
                  <el-option
                    v-for="user in userList"
                    :key="user.userId"
                    :label="user.nickName"
                    :value="user.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="任务描述" prop="taskDescription">
            <el-input
              v-model="assignmentForm.taskDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入任务描述"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划开始时间">
                <el-date-picker
                  v-model="assignmentForm.plannedStartTime"
                  type="datetime"
                  placeholder="选择开始时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划结束时间">
                <el-date-picker
                  v-model="assignmentForm.plannedEndTime"
                  type="datetime"
                  placeholder="选择结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 检测项目选择区域 -->
        <div class="detection-items-section">
          <h4>检测项目选择</h4>
          <p class="text-muted">说明：周期必须按顺序选择，一个采样任务可关联多个检测项目的不同周期</p>
          
          <div v-for="item in cycleItems" :key="item.itemId" class="detection-item-card">
            <div class="item-header">
              <el-checkbox
                :value="isItemSelected(item)"
                @change="handleItemSelectAll(item, $event)"
                :indeterminate="isItemIndeterminate(item)"
              >
                <strong>{{ item.category }} - {{ item.parameter }}</strong>
              </el-checkbox>
              <span class="item-info">检测方法：{{ item.method }} | 周期类型：{{ item.cycleType }} | 总周期数：{{ item.cycleCount }}</span>
            </div>
            
            <div class="cycle-selection">
              <span class="cycle-label">可选周期：</span>
              <el-checkbox-group v-model="selectedCycleItemIds">
                <el-checkbox
                  v-for="cycle in item.cycleItems"
                  :key="cycle.id"
                  :label="cycle.id"
                  :disabled="!cycle.isSelectable"
                  @change="handleCycleChange(item, cycle, $event)"
                >
                  第{{ cycle.cycleNumber }}周期
                  <el-tag v-if="cycle.status === 1" type="warning" size="small">已分配</el-tag>
                  <el-tag v-else-if="cycle.status === 2" type="success" size="small">已完成</el-tag>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignmentOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitAssignment" :loading="assignmentLoading">生成采样任务</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getApprovedQuotationsPage, getQuotationCycleItems, createSamplingTask } from "@/api/sampling/assignment";
import { listUser } from "@/api/system/user";


export default {
  name: "SamplingAssignment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目报价列表
      quotationList: [],
      // 用户列表
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        projectCode: null,
        customerName: null
      },
      // 分配弹窗
      assignmentOpen: false,
      assignmentLoading: false,
      selectedQuotation: null,
      cycleItems: [],
      selectedCycleItemIds: [],
      // 分配表单
      assignmentForm: {
        taskName: '',
        taskDescription: '',
        assignedUserId: null,
        plannedStartTime: null,
        plannedEndTime: null
      },
      // 表单校验
      assignmentRules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getUserList();
  },
  methods: {
    /** 查询项目报价列表 */
    getList(pagination) {
      // 如果有分页参数，更新查询参数
      if (pagination) {
        this.queryParams.pageNum = pagination.page;
        this.queryParams.pageSize = pagination.limit;
      }
      
      this.loading = true;
      this.quotationList = []; // Clear the list before fetching new data
      getApprovedQuotationsPage(this.queryParams).then(response => {
        let rows = [];
        let total = 0;

        if (response && response.data) {
          rows = response.data.rows || [];
          total = response.data.total || 0;
        }

        this.quotationList = rows.filter(item => item != null && typeof item === 'object');
        this.total = total;
        this.loading = false;
      }).catch(error => {
        console.error('获取项目报价列表失败:', error);
        this.quotationList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 获取用户列表 */
    getUserList() {
      listUser({ pageSize: 100000 }).then(response => {
        this.userList = response.rows || [];
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.userList = [];
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryRef");
      this.handleQuery();
    },
    /** 采样分配按钮操作 */
    handleAssignment(row) {
      if (!row) {
        console.error("handleAssignment received an undefined row.");
        return;
      }
      this.selectedQuotation = row;
      this.assignmentForm.taskName = `${row.projectName}-采样任务`;
      this.selectedCycleItemIds = [];
      this.assignmentOpen = true;
      
      // 获取检测周期条目
      getQuotationCycleItems(row.id).then(response => {
        this.cycleItems = response.data;
      });
    },
    /** 判断检测项目是否全选 */
    isItemSelected(item) {
      const selectableCycles = item.cycleItems.filter(cycle => cycle.isSelectable);
      if (selectableCycles.length === 0) return false;
      return selectableCycles.every(cycle => this.selectedCycleItemIds.includes(cycle.id));
    },
    /** 判断检测项目是否半选 */
    isItemIndeterminate(item) {
      const selectableCycles = item.cycleItems.filter(cycle => cycle.isSelectable);
      if (selectableCycles.length === 0) return false;
      const selectedCount = selectableCycles.filter(cycle => this.selectedCycleItemIds.includes(cycle.id)).length;
      return selectedCount > 0 && selectedCount < selectableCycles.length;
    },
    /** 检测项目全选/取消全选 */
    handleItemSelectAll(item, checked) {
      const selectableCycles = item.cycleItems.filter(cycle => cycle.isSelectable);
      if (checked) {
        // 全选：按顺序选择所有可选周期
        const sortedCycles = selectableCycles.sort((a, b) => a.cycleNumber - b.cycleNumber);
        for (const cycle of sortedCycles) {
          if (!this.selectedCycleItemIds.includes(cycle.id)) {
            this.selectedCycleItemIds.push(cycle.id);
          }
        }
      } else {
        // 取消全选
        selectableCycles.forEach(cycle => {
          const index = this.selectedCycleItemIds.indexOf(cycle.id);
          if (index > -1) {
            this.selectedCycleItemIds.splice(index, 1);
          }
        });
      }
    },
    /** 周期选择变更 */
    handleCycleChange(item, cycle, checked) {
      if (checked) {
        // 检查是否可以选择（前面的周期是否都已选择）
        const itemCycles = item.cycleItems.sort((a, b) => a.cycleNumber - b.cycleNumber);
        const currentIndex = itemCycles.findIndex(c => c.id === cycle.id);
        
        for (let i = 0; i < currentIndex; i++) {
          const prevCycle = itemCycles[i];
          if (prevCycle.status === 0 && !this.selectedCycleItemIds.includes(prevCycle.id)) {
            this.$modal.msgWarning(`请先选择第${prevCycle.cycleNumber}周期`);
            // 取消当前选择
            const index = this.selectedCycleItemIds.indexOf(cycle.id);
            if (index > -1) {
              this.selectedCycleItemIds.splice(index, 1);
            }
            return;
          }
        }
      }
    },
    /** 提交分配 */
    submitAssignment() {
      this.$refs["assignmentForm"].validate(valid => {
        if (valid) {
          if (this.selectedCycleItemIds.length === 0) {
            this.$modal.msgWarning("请至少选择一个检测周期");
            return;
          }
          
          this.assignmentLoading = true;
          const assignmentData = {
            projectQuotationId: this.selectedQuotation.id,
            taskName: this.assignmentForm.taskName,
            taskDescription: this.assignmentForm.taskDescription,
            selectedCycleItemIds: this.selectedCycleItemIds,
            plannedStartTime: this.assignmentForm.plannedStartTime,
            plannedEndTime: this.assignmentForm.plannedEndTime,
            assignedUserId: this.assignmentForm.assignedUserId
          };
          
          createSamplingTask(assignmentData).then(response => {
            this.$modal.msgSuccess(response.msg);
            this.assignmentOpen = false;
            this.getList(); // 刷新列表
          }).finally(() => {
            this.assignmentLoading = false;
          });
        }
      });
    },
    /** 安全获取格式化委托日期 */
    getFormattedCommissionDate(row) {
      return (row && row.commissionDate) ? this.parseTime(row.commissionDate, '{y}-{m}-{d}') : '-';
    },
    /** 安全获取格式化创建时间 */
    getFormattedCreateTime(row) {
      return (row && row.createTime) ? this.parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.assignment-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.assignment-header h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-weight: 600;
}

.text-muted {
  color: #909399;
  font-size: 14px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: 15px;
}

.box-card {
  margin-top: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-button--link {
  color: #409eff;
  font-weight: 500;
}

.el-button--link:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.detection-items-section {
  margin-top: 20px;
}

.detection-items-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-weight: 600;
}

.detection-item-card {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.detection-item-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-header {
  margin-bottom: 12px;
}

.item-info {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.cycle-selection {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 8px;
}

.cycle-label {
  font-size: 12px;
  color: #606266;
  margin-right: 10px;
  min-width: 60px;
  font-weight: 500;
}

.el-checkbox {
  margin-right: 15px;
  margin-bottom: 5px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.mb8 {
  margin-bottom: 8px;
}

/* 搜索表单样式 */
.search-form {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-input, .el-select {
  width: 100%;
}

/* 搜索表单样式 */
.el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

/* 操作按钮区域 */
.el-row.mb8 {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

/* 按钮组样式优化 */
.el-row.mb8 .el-button {
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-row.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片头部样式增强 */
.box-card .el-card__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #e9ecef;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f0f9ff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 12px;
  }
  
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .el-form-item .el-input,
  .el-form-item .el-select {
    width: 100% !important;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .card-subtitle {
    display: none;
  }
  
  .el-table-column {
    min-width: 80px;
  }
}
</style>