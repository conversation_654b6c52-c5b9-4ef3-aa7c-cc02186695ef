"""
运行数据迁移脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入迁移脚本
from migrations.price_list_test_code_migration import main as price_list_migration

async def run_migrations():
    """
    运行所有迁移脚本
    """
    print("开始运行数据迁移...")
    
    # 运行价目表与技术手册关联迁移
    print("\n运行价目表与技术手册关联迁移...")
    await price_list_migration()
    
    print("\n所有迁移完成！")

if __name__ == "__main__":
    asyncio.run(run_migrations())
