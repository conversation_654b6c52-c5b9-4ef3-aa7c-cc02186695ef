from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from sqlalchemy.orm import selectinload
from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember
# from module_system.entity.do.sys_user_do import SysUser  # 暂时注释掉，避免模块导入问题


class SamplingTaskMemberDAO:
    """采样任务组员数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_member(self, member: SamplingTaskMember) -> SamplingTaskMember:
        """创建任务组员"""
        self.db.add(member)
        await self.db.flush()
        await self.db.refresh(member)
        return member
    
    async def get_members_by_task_id(self, task_id: int) -> List[SamplingTaskMember]:
        """根据任务ID获取所有组员"""
        stmt = (
            select(SamplingTaskMember)
            .where(SamplingTaskMember.sampling_task_id == task_id)
            .order_by(SamplingTaskMember.create_time)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_member_by_task_and_user(self, task_id: int, user_id: int) -> Optional[SamplingTaskMember]:
        """根据任务ID和用户ID获取组员记录"""
        stmt = select(SamplingTaskMember).where(
            SamplingTaskMember.sampling_task_id == task_id,
            SamplingTaskMember.user_id == user_id
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def delete_members_by_task_id(self, task_id: int) -> int:
        """删除任务的所有组员"""
        stmt = delete(SamplingTaskMember).where(SamplingTaskMember.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        return result.rowcount
    
    async def delete_member_by_task_and_user(self, task_id: int, user_id: int) -> int:
        """删除指定任务的指定组员"""
        stmt = delete(SamplingTaskMember).where(
            SamplingTaskMember.sampling_task_id == task_id,
            SamplingTaskMember.user_id == user_id
        )
        result = await self.db.execute(stmt)
        return result.rowcount
    
    async def get_tasks_by_member_user_id(self, user_id: int) -> List[SamplingTaskMember]:
        """根据用户ID获取该用户参与的所有任务"""
        stmt = (
            select(SamplingTaskMember)
            .where(SamplingTaskMember.user_id == user_id)
            .order_by(SamplingTaskMember.create_time.desc())
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def batch_create_members(self, members: List[SamplingTaskMember]) -> List[SamplingTaskMember]:
        """批量创建任务组员"""
        self.db.add_all(members)
        await self.db.flush()
        for member in members:
            await self.db.refresh(member)
        return members
