"""
测试技术手册类目API接口
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import httpx
import json
from config.env import AppConfig

# API基础URL
BASE_URL = f"http://{AppConfig.app_host}:{AppConfig.app_port}"

async def test_technical_manual_category_apis():
    """
    测试技术手册类目API接口
    """
    print("=== 技术手册类目API接口测试 ===")
    
    async with httpx.AsyncClient() as client:
        
        # 1. 测试获取类目列表
        print("\n1. 测试获取类目列表:")
        try:
            response = await client.get(f"{BASE_URL}/basedata/technical-manual-category/list")
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 获取类目列表成功，数量: {len(data.get('data', []))}")
                if data.get('data'):
                    print(f"  示例数据: {data['data'][0]}")
            else:
                print(f"  ❌ 获取类目列表失败: {response.text}")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
        
        # 2. 测试获取分页列表
        print("\n2. 测试获取分页列表:")
        try:
            params = {"pageNum": 1, "pageSize": 10}
            response = await client.get(f"{BASE_URL}/basedata/technical-manual-category/page", params=params)
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                page_data = data.get('data', {})
                print(f"  ✅ 获取分页列表成功")
                print(f"  总数: {page_data.get('total', 0)}")
                print(f"  当前页数据量: {len(page_data.get('rows', []))}")
            else:
                print(f"  ❌ 获取分页列表失败: {response.text}")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
        
        # 3. 测试获取分类选项
        print("\n3. 测试获取分类选项:")
        try:
            response = await client.get(f"{BASE_URL}/basedata/technical-manual-category/options/classifications")
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                classifications = data.get('data', [])
                print(f"  ✅ 获取分类选项成功，数量: {len(classifications)}")
                print(f"  分类列表: {classifications[:5]}...")  # 显示前5个
            else:
                print(f"  ❌ 获取分类选项失败: {response.text}")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
        
        # 4. 测试获取检测类别选项
        print("\n4. 测试获取检测类别选项:")
        try:
            response = await client.get(f"{BASE_URL}/basedata/technical-manual-category/options/categories")
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                categories = data.get('data', [])
                print(f"  ✅ 获取检测类别选项成功，数量: {len(categories)}")
                print(f"  检测类别列表: {categories[:5]}...")  # 显示前5个
            else:
                print(f"  ❌ 获取检测类别选项失败: {response.text}")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
        
        # 5. 测试获取类目树
        print("\n5. 测试获取类目树:")
        try:
            response = await client.get(f"{BASE_URL}/basedata/technical-manual-category/tree")
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                tree_data = data.get('data', [])
                print(f"  ✅ 获取类目树成功，根节点数量: {len(tree_data)}")
                if tree_data:
                    first_node = tree_data[0]
                    print(f"  示例根节点: {first_node.get('label')}")
                    print(f"  子节点数量: {len(first_node.get('children', []))}")
            else:
                print(f"  ❌ 获取类目树失败: {response.text}")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
        
        # 6. 测试新增类目（需要认证，可能会失败）
        print("\n6. 测试新增类目:")
        try:
            new_category = {
                "classification": "测试分类",
                "category": "测试检测类别",
                "status": "0",
                "remark": "API测试创建的类目"
            }
            response = await client.post(
                f"{BASE_URL}/basedata/technical-manual-category",
                json=new_category
            )
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 新增类目成功: {data}")
                # 保存ID用于后续测试
                created_id = data.get('data', {}).get('id')
                if created_id:
                    print(f"  创建的类目ID: {created_id}")
            else:
                print(f"  ⚠️  新增类目失败（可能需要认证）: {response.text}")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
        
        # 7. 测试URL路径
        print("\n7. 测试URL路径:")
        test_urls = [
            "/basedata/technical-manual-category/list",
            "/basedata/technical-manual-category/page",
            "/basedata/technical-manual-category/options/classifications",
            "/basedata/technical-manual-category/options/categories",
            "/basedata/technical-manual-category/tree"
        ]
        
        for url in test_urls:
            try:
                response = await client.get(f"{BASE_URL}{url}")
                status = "✅" if response.status_code == 200 else "❌"
                print(f"  {status} {url} - 状态码: {response.status_code}")
            except Exception as e:
                print(f"  ❌ {url} - 异常: {str(e)}")
        
        # 8. 测试错误处理
        print("\n8. 测试错误处理:")
        try:
            # 测试不存在的ID
            response = await client.get(f"{BASE_URL}/basedata/technical-manual-category/99999")
            print(f"  不存在ID查询 - 状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"  ✅ 正确处理了不存在的ID")
            else:
                print(f"  ⚠️  未正确处理不存在的ID")
        except Exception as e:
            print(f"  ❌ 错误处理测试异常: {str(e)}")
        
        print("\n=== 测试完成 ===")


def test_frontend_api_urls():
    """
    测试前端API URL配置
    """
    print("\n=== 前端API URL配置测试 ===")
    
    # 检查前端API文件
    api_file_path = "../front/src/api/basedata/technicalManualCategory.js"
    if os.path.exists(api_file_path):
        print("✅ 前端API文件存在")
        
        with open(api_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查URL配置
        urls_to_check = [
            "/basedata/technical-manual-category/list",
            "/basedata/technical-manual-category/page",
            "/basedata/technical-manual-category/",
            "/basedata/technical-manual-category/options/classifications",
            "/basedata/technical-manual-category/options/categories",
            "/basedata/technical-manual-category/tree"
        ]
        
        for url in urls_to_check:
            if url in content:
                print(f"✅ URL配置正确: {url}")
            else:
                print(f"❌ URL配置缺失: {url}")
                
        # 检查是否有双斜杠
        if "//" in content and "http://" not in content and "https://" not in content:
            print("⚠️  可能存在双斜杠问题")
        else:
            print("✅ 无双斜杠问题")
            
    else:
        print("❌ 前端API文件不存在")


if __name__ == "__main__":
    print("开始测试技术手册类目功能...")
    
    # 测试前端API配置
    test_frontend_api_urls()
    
    # 测试后端API接口
    try:
        asyncio.run(test_technical_manual_category_apis())
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    
    print("\n✅ 所有测试完成！")
