"""
瓶组管理服务
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from module_basedata.entity.vo.technical_manual_vo import TechnicalManualQueryModel
from module_basedata.service.technical_manual_service import TechnicalManualService
from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance
from module_bottle_maintenance.entity.do.bottle_maintenance_technical_manual_do import BottleMaintenanceTechnicalManual
from module_bottle_maintenance.entity.do.bottle_code_sequence_do import BottleCodeSequence
from module_bottle_maintenance.entity.vo.bottle_maintenance_vo import (
    BottleMaintenanceModel,
    AddBottleMaintenanceModel,
    EditBottleMaintenanceModel,
    BottleMaintenanceQueryModel,
    BottleMaintenanceResponseModel,
    BottleMaintenanceImportModel,
    BottleMaintenanceImportResultModel,
    BottleMaintenanceImportErrorModel,
    BottleMaintenancePageQueryModel,
)
from utils.common_util import CamelCaseUtil
from utils.log_util import logger
from utils.page_util import PageUtil


class BottleMaintenanceService(BaseService[BottleMaintenance]):
    """瓶组管理服务"""

    def __init__(self, db: AsyncSession):
        super().__init__(BottleMaintenance, db)
        self.db = db
        self.technical_manual_service = TechnicalManualService(db)

    async def generate_bottle_code(self) -> str:
        """生成瓶组编码"""
        try:
            # 获取当前序列值并自增
            stmt = select(BottleCodeSequence).limit(1)
            result = await self.db.execute(stmt)
            sequence = result.scalar_one_or_none()

            if not sequence:
                # 如果序列不存在，创建一个
                sequence = BottleCodeSequence(current_value=1, update_time=datetime.now())
                self.db.add(sequence)
                await self.db.flush()
                current_value = 1
            else:
                # 自增序列值
                current_value = sequence.current_value + 1
                sequence.current_value = current_value
                sequence.update_time = datetime.now()
                await self.db.flush()

            # 生成编码：BOL + 6位数字
            bottle_code = f"BOL{current_value:06d}"
            return bottle_code

        except Exception as e:
            raise ServiceException(f"生成瓶组编码失败: {str(e)}")

    async def get_bottle_maintenance_page(self, query_model: BottleMaintenancePageQueryModel):
        """获取瓶组管理,分页"""
        # 构建基础查询
        stmt = select(BottleMaintenance)

        # 添加瓶组管理字段查询条件
        if query_model.bottle_code:
            stmt = stmt.where(BottleMaintenance.bottle_code.like(f"%{query_model.bottle_code}%"))
        if query_model.bottle_type:
            stmt = stmt.where(BottleMaintenance.bottle_type.like(f"%{query_model.bottle_type}%"))
        if query_model.bottle_volume:
            stmt = stmt.where(BottleMaintenance.bottle_volume.like(f"%{query_model.bottle_volume}%"))
        if query_model.storage_styles:
            stmt = stmt.where(BottleMaintenance.storage_styles.like(f"%{query_model.storage_styles}%"))
        if query_model.fix_styles:
            stmt = stmt.where(BottleMaintenance.fix_styles.like(f"%{query_model.fix_styles}%"))
        if query_model.sample_age_unit:
            stmt = stmt.where(BottleMaintenance.sample_age_unit.like(f"%{query_model.sample_age_unit}%"))

        # 如果有技术手册相关查询条件，需要先查询技术手册获取ID列表
        if query_model.parameter or query_model.method or query_model.category:
            technical_manual_ids = await self._find_technical_manuals(
                query_model.parameter, query_model.method, query_model.category
            )

            if technical_manual_ids:
                # 通过关联表查询瓶组管理ID
                bottle_ids_stmt = (
                    select(BottleMaintenanceTechnicalManual.bottle_maintenance_id)
                    .where(BottleMaintenanceTechnicalManual.technical_manual_id.in_(technical_manual_ids))
                    .distinct()
                )
                bottle_ids_result = await self.db.execute(bottle_ids_stmt)
                bottle_ids = [row[0] for row in bottle_ids_result.fetchall()]

                if bottle_ids:
                    # 在瓶组管理表中进行in查询
                    stmt = stmt.where(BottleMaintenance.id.in_(bottle_ids))
                else:
                    # 如果没有找到关联的瓶组管理，返回空结果
                    return [], 0
            else:
                # 如果没有找到匹配的技术手册，返回空结果
                return [], 0

        # 分页查询
        page_result = await PageUtil.paginate(self.db, stmt, query_model.page_num, query_model.page_size, True)
        for bottle_dict in page_result.rows:
            # 获取关联的技术手册（手动查询）
            bottle_dict["technicalManuals"] = await self._get_technical_manuals_by_bottle_id(bottle_dict.get("id"))
        return page_result

    async def get_bottle_maintenance_by_id(self, bottle_maintenance_id: int) -> BottleMaintenanceResponseModel:
        """根据ID获取瓶组管理详情"""
        stmt = select(BottleMaintenance).where(BottleMaintenance.id == bottle_maintenance_id)
        result = await self.db.execute(stmt)
        bottle_maintenance = result.scalar_one_or_none()

        if not bottle_maintenance:
            raise ServiceException("瓶组管理记录不存在")

        # 获取关联的技术手册
        technical_manuals = await self._get_technical_manuals_by_bottle_id(bottle_maintenance_id)

        return BottleMaintenanceResponseModel(
            id=bottle_maintenance.id,
            bottleCode=bottle_maintenance.bottle_code,
            bottleType=bottle_maintenance.bottle_type,
            bottleVolume=bottle_maintenance.bottle_volume,
            storageStyles=bottle_maintenance.storage_styles,
            fixStyles=bottle_maintenance.fix_styles,
            sampleAge=bottle_maintenance.sample_age,
            sampleAgeUnit=bottle_maintenance.sample_age_unit,
            remark=bottle_maintenance.remark,
            technicalManuals=technical_manuals,
        )

    async def add_bottle_maintenance(
        self, add_model: AddBottleMaintenanceModel, current_user: CurrentUserModel
    ) -> BottleMaintenanceResponseModel:
        """添加瓶组管理"""
        try:
            # 生成瓶组编码
            bottle_code = await self.generate_bottle_code()

            # 创建瓶组管理对象
            bottle_maintenance = BottleMaintenance(
                bottle_code=bottle_code,
                bottle_type=add_model.bottle_type,
                bottle_volume=add_model.bottle_volume,
                storage_styles=add_model.storage_styles,
                fix_styles=add_model.fix_styles,
                sample_age=add_model.sample_age,
                sample_age_unit=add_model.sample_age_unit,
                remark=add_model.remark,
                create_by=current_user.user.user_name,
                create_time=datetime.now(),
                update_by=current_user.user.user_name,
                update_time=datetime.now(),
            )

            self.db.add(bottle_maintenance)
            await self.db.flush()

            # 添加技术手册关联
            if add_model.technical_manual_ids:
                for technical_manual_id in add_model.technical_manual_ids:
                    relation = BottleMaintenanceTechnicalManual(
                        bottle_maintenance_id=bottle_maintenance.id,
                        technical_manual_id=technical_manual_id,
                        create_by=current_user.user.user_name,
                        create_time=datetime.now(),
                    )
                    self.db.add(relation)

            await self.db.commit()

            # 返回创建的记录
            return await self.get_bottle_maintenance_by_id(bottle_maintenance.id)

        except Exception as e:
            await self.db.rollback()
            raise ServiceException(f"添加瓶组管理失败: {str(e)}")

    async def edit_bottle_maintenance(
        self, edit_model: EditBottleMaintenanceModel, current_user: CurrentUserModel
    ) -> BottleMaintenanceResponseModel:
        """编辑瓶组管理"""
        try:
            # 查询现有记录
            stmt = select(BottleMaintenance).where(BottleMaintenance.id == edit_model.id)
            result = await self.db.execute(stmt)
            bottle_maintenance = result.scalar_one_or_none()

            if not bottle_maintenance:
                raise ServiceException("瓶组管理记录不存在")

            # 更新基础信息
            bottle_maintenance.bottle_type = edit_model.bottle_type
            bottle_maintenance.bottle_volume = edit_model.bottle_volume
            bottle_maintenance.storage_styles = edit_model.storage_styles
            bottle_maintenance.fix_styles = edit_model.fix_styles
            bottle_maintenance.sample_age = edit_model.sample_age
            bottle_maintenance.sample_age_unit = edit_model.sample_age_unit
            bottle_maintenance.remark = edit_model.remark
            bottle_maintenance.update_by = current_user.user.user_name
            bottle_maintenance.update_time = datetime.now()

            # 删除原有的技术手册关联
            delete_stmt = delete(BottleMaintenanceTechnicalManual).where(
                BottleMaintenanceTechnicalManual.bottle_maintenance_id == edit_model.id
            )
            await self.db.execute(delete_stmt)

            # 添加新的技术手册关联
            if edit_model.technical_manual_ids:
                for technical_manual_id in edit_model.technical_manual_ids:
                    relation = BottleMaintenanceTechnicalManual(
                        bottle_maintenance_id=edit_model.id,
                        technical_manual_id=technical_manual_id,
                        create_by=current_user.user.user_name,
                        create_time=datetime.now(),
                    )
                    self.db.add(relation)

            await self.db.commit()

            # 返回更新后的记录
            return await self.get_bottle_maintenance_by_id(edit_model.id)

        except Exception as e:
            await self.db.rollback()
            raise ServiceException(f"编辑瓶组管理失败: {str(e)}")

    async def delete_bottle_maintenance(self, bottle_maintenance_id: int, current_user: CurrentUserModel) -> None:
        """删除瓶组管理（物理删除）"""
        try:
            # 查询现有记录
            stmt = select(BottleMaintenance).where(BottleMaintenance.id == bottle_maintenance_id)
            result = await self.db.execute(stmt)
            bottle_maintenance = result.scalar_one_or_none()

            if not bottle_maintenance:
                raise ServiceException("瓶组管理记录不存在")

            # 先删除关联的技术手册关系
            delete_relations_stmt = delete(BottleMaintenanceTechnicalManual).where(
                BottleMaintenanceTechnicalManual.bottle_maintenance_id == bottle_maintenance_id
            )
            await self.db.execute(delete_relations_stmt)

            # 物理删除瓶组管理记录
            delete_stmt = delete(BottleMaintenance).where(BottleMaintenance.id == bottle_maintenance_id)
            await self.db.execute(delete_stmt)

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            raise ServiceException(f"删除瓶组管理失败: {str(e)}")

    async def batch_import_bottle_maintenance(
        self, import_data: List[BottleMaintenanceImportModel], current_user: CurrentUserModel
    ) -> BottleMaintenanceImportResultModel:
        """批量导入瓶组管理"""
        try:
            success_count = 0
            error_count = 0
            errors = []
            success_data = []

            for row_index, import_model in enumerate(import_data, start=1):
                try:
                    # 验证必填字段
                    if not import_model.bottle_type:
                        errors.append(
                            BottleMaintenanceImportErrorModel(
                                row=row_index, field="bottle_type", message="容器类型不能为空"
                            )
                        )
                        error_count += 1
                        continue

                    if not import_model.bottle_volume:
                        errors.append(
                            BottleMaintenanceImportErrorModel(
                                row=row_index, field="bottle_volume", message="容器容量不能为空"
                            )
                        )
                        error_count += 1
                        continue

                    # 处理存储方式和固定方式（逗号分隔转为数组）
                    storage_styles = None
                    if import_model.storage_styles:
                        storage_styles = [s.strip() for s in import_model.storage_styles.split(",") if s.strip()]

                    fix_styles = None
                    if import_model.fix_styles:
                        fix_styles = [s.strip() for s in import_model.fix_styles.split(",") if s.strip()]

                    # 查找关联的技术手册
                    technical_manual_ids = []
                    if import_model.parameter or import_model.method or import_model.category:
                        technical_manual_ids = await self._find_technical_manuals(
                            import_model.parameter, import_model.method, import_model.category
                        )
                        if not technical_manual_ids:
                            raise Exception(
                                f"类别:{import_model.category}、参数:{import_model.parameter}、"
                                f"方法:{import_model.method}，不存在"
                            )

                    # 创建添加模型
                    add_model = AddBottleMaintenanceModel.parse_obj(
                        CamelCaseUtil.transform_result(
                            dict(
                                bottle_type=import_model.bottle_type,
                                bottle_volume=import_model.bottle_volume,
                                storage_styles=storage_styles,
                                fix_styles=fix_styles,
                                sample_age=import_model.sample_age,
                                sample_age_unit=import_model.sample_age_unit,
                                remark=import_model.remark,
                                technical_manual_ids=technical_manual_ids,
                            )
                        )
                    )

                    # 添加记录
                    result = await self.add_bottle_maintenance(add_model, current_user)
                    success_data.append(result)
                    success_count += 1

                except Exception as e:
                    errors.append(BottleMaintenanceImportErrorModel(row=row_index, field="general", message=str(e)))
                    error_count += 1
                    logger.info("import from excel error: %s", str(e))

            return BottleMaintenanceImportResultModel(
                success_count=success_count, error_count=error_count, errors=errors, data=success_data
            )

        except Exception as e:
            logger.error("batch_import_bottle_maintenance error: %s", str(e))
            raise ServiceException(f"批量导入瓶组管理失败: {str(e)}")

    async def _find_technical_manuals(
        self, parameter: Optional[str], method: Optional[str], category: Optional[str]
    ) -> List[int]:
        """根据参数、方法、类别查找技术手册ID（用于导入功能）"""
        try:
            # 在实际使用时，需要实现完整的技术手册查询逻辑
            technical_manual_list = await self.technical_manual_service.get_technical_manual_list(
                TechnicalManualQueryModel(parameter=parameter, method=method, category=category)
            )
            if not technical_manual_list:
                return []
            return [manual.get("id") for manual in technical_manual_list]
        except Exception as e:
            logger.error("_find_technical_manuals error: %s", str(e))
            # 如果查询失败，返回空列表而不是抛出异常
            return []

    async def _get_technical_manuals_by_bottle_id(self, bottle_maintenance_id: int) -> List[dict]:
        """根据瓶组管理ID获取关联的技术手册"""
        try:
            # 查询关联的技术手册ID
            relation_stmt = select(BottleMaintenanceTechnicalManual.technical_manual_id).where(
                BottleMaintenanceTechnicalManual.bottle_maintenance_id == bottle_maintenance_id
            )
            relation_result = await self.db.execute(relation_stmt)
            technical_manual_ids = [int(row[0]) for row in relation_result.fetchall()]
            if not technical_manual_ids:
                return []
            # 在实际使用时，需要查询技术手册表获取详细信息
            return await self.technical_manual_service.get_technical_manual_detail(technical_manual_ids)
        except Exception as e:
            logger.error("_get_technical_manuals_by_bottle_id error: %s", str(e))
            # 如果查询失败，返回空列表而不是抛出异常
            return []
