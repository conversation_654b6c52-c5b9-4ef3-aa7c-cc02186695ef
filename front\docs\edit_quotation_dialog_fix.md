# 项目报价编辑页面修复文档

## 修复概述

对 `EditQuotationDialog.vue` 进行了全面修复，参考 `ViewQuotationDialog.vue` 的设计，实现了以下功能：

1. **费用项1：检测项目费用明细** - 完全参考ViewQuotationDialog的设计
2. **项目的其他费用表** - 修复了显示和编辑功能

## 主要修复内容

### 1. 费用项1：检测项目费用明细

#### 修复前问题：
- 只有简单的编辑表格
- 缺少详细的费用计算显示
- 没有费用汇总信息

#### 修复后功能：
- **查看模式**：显示详细的费用明细表格，包含：
  - 样品类别、点位名称、参数（合并显示）
  - 参数个数、检测点位、检测周期、检测频率、样品数
  - 采样单价、采样费用、检测首项单价、检测增项单价、检测封顶单价
  - 检测单价、检测总费用、前处理单价、前处理费、总价、备注
  
- **编辑模式**：保留原有的简化编辑表格
  
- **费用汇总**：
  - 查看模式：显示采样费用合计、检测费用合计、前处理费用合计、检测项目费用合计
  - 编辑模式：显示检测项目费用合计、折扣率设置、折后费用合计

- **新增功能**：
  - 同步基础价目表按钮
  - 自动获取费用计算详情

### 2. 项目其他费用表

#### 修复前问题：
- 只有一个表格，查看和编辑混合
- 显示效果不够清晰

#### 修复后功能：
- **查看模式**：只读表格，显示费用名称、数量、单价、总价、备注
- **编辑模式**：可编辑表格，支持行级编辑和保存
- **统一样式**：参考ViewQuotationDialog的表格样式

### 3. 新增数据和方法

#### 新增数据：
```javascript
// 费用计算详情数据
const feeCalculationData = ref({
  samplingFee: 0,
  testingFee: 0,
  pretreatmentFee: 0,
  totalFee: 0,
  calculationDetails: {
    details: [],
    summary: {}
  }
})

// 费用明细表格数据
const feeDetailTableData = ref([])
```

#### 新增方法：
```javascript
// 格式化货币
function formatCurrency(value)

// 同步基础价目表
function handleSyncBasedataPrices()

// 获取费用计算详情
function getFeeCalculationDetail(id)

// 处理费用明细数据
function processFeeDetailData(details)
```

#### 新增API导入：
```javascript
import { 
  getProjectQuotationFeeCalculation, 
  syncBasedataPrices 
} from "@/api/quotation/projectQuotation"
```

### 4. 样式优化

#### 新增样式类：
- `.fee-summary-section` - 费用汇总区域样式
- `.fee-summary-item` - 费用汇总项样式
- `.fee-label` / `.fee-value` - 费用标签和数值样式
- `.total-amount` - 总金额样式
- `.total-price` - 总价样式
- `.cell-content` - 单元格内容样式
- `.negative-value` - 负值样式
- `.final-amount` - 最终金额样式

## 功能特性

### 1. 双模式显示
- **查看模式**：详细的费用明细表格，完全参考ViewQuotationDialog
- **编辑模式**：简化的编辑表格，便于快速修改

### 2. 智能数据处理
- 自动获取费用计算详情
- 按类别-方法-点位分组显示
- 参数合并显示（用"、"分隔）
- 自动计算参数个数

### 3. 费用汇总
- 分类显示：采样费用、检测费用、前处理费用
- 总计显示：检测项目费用合计
- 实时计算：折扣率和折后费用

### 4. 操作功能
- 同步基础价目表数据
- 行级编辑其他费用
- 实时费用计算

## 技术实现

### 1. 条件渲染
```vue
<!-- 详细费用明细表格 -->
<el-table :data="feeDetailTableData" v-if="!isEditable">
  <!-- 详细列定义 -->
</el-table>

<!-- 编辑模式下的简化表格 -->
<el-table :data="quotationForm.items" v-if="isEditable">
  <!-- 简化列定义 -->
</el-table>
```

### 2. 数据分组处理
```javascript
// 按类别-方法-点位名称分组
const groupedData = {}
details.forEach(item => {
  const key = `${item.category}_${item.method}_${item.pointName || '未指定点位'}`
  // 分组逻辑
})
```

### 3. 费用计算
```javascript
// 累加各类费用
groupedData[key].samplingFee += item.samplingFee || 0
groupedData[key].testingFee += item.testingFee || 0
groupedData[key].pretreatmentFee += item.pretreatmentFee || 0
```

## 用户体验改进

### 1. 视觉一致性
- 与ViewQuotationDialog保持一致的表格样式
- 统一的费用汇总显示格式
- 一致的颜色和字体样式

### 2. 操作便利性
- 编辑模式下的简化表格，便于快速修改
- 查看模式下的详细信息，便于核对
- 一键同步基础价目表数据

### 3. 信息完整性
- 显示所有费用计算细节
- 提供完整的费用汇总信息
- 支持参数合并显示

## 测试建议

### 1. 功能测试
- [ ] 查看模式下费用明细表格显示正确
- [ ] 编辑模式下可以正常编辑项目明细
- [ ] 费用汇总计算正确
- [ ] 同步基础价目表功能正常
- [ ] 其他费用表的查看和编辑功能正常

### 2. 样式测试
- [ ] 表格样式与ViewQuotationDialog一致
- [ ] 费用汇总区域显示美观
- [ ] 响应式布局正常

### 3. 数据测试
- [ ] 费用计算详情获取正确
- [ ] 数据分组和合并显示正确
- [ ] 货币格式化显示正确

## 总结

通过这次修复，EditQuotationDialog.vue 现在具备了：

1. **完整的费用明细显示** - 参考ViewQuotationDialog的设计
2. **优化的其他费用表** - 分离查看和编辑模式
3. **增强的用户体验** - 统一的样式和操作逻辑
4. **完善的功能支持** - 同步基础价目表、费用计算等

现在用户可以在编辑页面中获得与查看页面一致的详细费用信息，同时保持编辑功能的便利性。
