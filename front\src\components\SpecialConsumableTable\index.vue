<template>
  <div class="special-consumable-table">
    <el-card class="box-card" style="border: 1px solid #dcdfe6;">
      <template #header>
        <div class="card-header">
          <span>费用项2：特殊耗材费用明细</span>
        </div>
      </template>
      
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :show-header="true"
        size="small"
      >
        <el-table-column prop="parameter" label="参数" width="150" />
        <el-table-column prop="method" label="方法" width="150" />
        <el-table-column prop="unitPrice" label="特殊耗材单价（元）" width="140" align="right">
          <template #default="scope">
            {{ formatPrice(scope.row.unitPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" align="center">
          <template #default="scope">
            <el-input-number
              v-if="!readonly"
              v-model="scope.row.quantity"
              :min="1"
              :max="999"
              size="small"
              controls-position="right"
              @change="handleQuantityChange(scope.row)"
            />
            <span v-else>{{ scope.row.quantity }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="总价（元）" width="120" align="right">
          <template #default="scope">
            {{ formatPrice(scope.row.totalPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150">
          <template #default="scope">
            <el-input
              v-if="!readonly"
              v-model="scope.row.remark"
              placeholder="请输入备注"
              size="small"
              @blur="handleRemarkChange(scope.row)"
            />
            <span v-else>{{ scope.row.remark || '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 费用汇总 -->
      <div class="fee-summary" style="margin-top: 10px; text-align: right;">
        <span style="font-weight: bold;">特殊耗材费用小计：{{ formatPrice(totalFee) }} 元</span>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:data', 'change'])

// 响应式数据
const tableData = ref([])

// 计算属性
const totalFee = computed(() => {
  return tableData.value.reduce((sum, item) => {
    return sum + parseFloat(item.totalPrice || 0)
  }, 0)
})

// 监听props变化
watch(() => props.data, (newData) => {
  tableData.value = newData ? [...newData] : []
}, { immediate: true, deep: true })

// 监听tableData变化，向父组件发送更新
watch(tableData, (newData) => {
  emit('update:data', newData)
  emit('change', {
    data: newData,
    totalFee: totalFee.value
  })
}, { deep: true })

// 方法
const formatPrice = (price) => {
  if (!price && price !== 0) return '0.00'
  return parseFloat(price).toFixed(2)
}

const handleQuantityChange = (row) => {
  // 重新计算总价
  const unitPrice = parseFloat(row.unitPrice || 0)
  const quantity = parseInt(row.quantity || 1)
  row.totalPrice = (unitPrice * quantity).toFixed(2)
  
  // 触发保存（可以在这里调用API保存单行数据）
  saveRowData(row)
}

const handleRemarkChange = (row) => {
  // 触发保存
  saveRowData(row)
}

const saveRowData = async (row) => {
  try {
    // 这里可以调用API保存单行数据
    // 由于按照需求，所有更新都在项目报价的主接口中处理
    // 所以这里只是触发父组件的change事件
    console.log('保存特殊耗材数据:', row)
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

// 暴露给父组件的方法
defineExpose({
  getTotalFee: () => totalFee.value,
  getData: () => tableData.value
})
</script>

<style scoped>
.special-consumable-table {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.fee-summary {
  padding: 10px 0;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  margin: 10px -20px -20px -20px;
  padding: 10px 20px;
}

:deep(.el-input-number) {
  width: 80px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}
</style>
