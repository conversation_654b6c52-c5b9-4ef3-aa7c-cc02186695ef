"""
项目报价特殊耗材费用明细 VO
"""
from typing import Optional
from pydantic import BaseModel, Field
from decimal import Decimal


class ProjectQuotationSpecialConsumableModel(BaseModel):
    """项目报价特殊耗材费用明细模型"""
    
    id: Optional[int] = Field(None, description="主键ID")
    project_quotation_id: Optional[int] = Field(None, description="项目报价ID")
    parameter: str = Field(..., description="参数")
    method: str = Field(..., description="方法")
    unit_price: Decimal = Field(default=Decimal('0.00'), description="特殊耗材单价")
    quantity: int = Field(default=1, description="数量")
    total_price: Decimal = Field(default=Decimal('0.00'), description="总价")
    remark: Optional[str] = Field(None, description="备注")
    
    class Config:
        from_attributes = True


class AddProjectQuotationSpecialConsumableModel(BaseModel):
    """添加项目报价特殊耗材费用明细模型"""
    
    parameter: str = Field(..., description="参数")
    method: str = Field(..., description="方法")
    quantity: int = Field(default=1, description="数量")
    remark: Optional[str] = Field(None, description="备注")
    
    class Config:
        from_attributes = True


class EditProjectQuotationSpecialConsumableModel(BaseModel):
    """编辑项目报价特殊耗材费用明细模型"""
    
    id: int = Field(..., description="主键ID")
    quantity: int = Field(default=1, description="数量")
    remark: Optional[str] = Field(None, description="备注")
    
    class Config:
        from_attributes = True


class ProjectQuotationSpecialConsumableResponseModel(BaseModel):
    """项目报价特殊耗材费用明细响应模型"""
    
    id: int = Field(..., description="主键ID")
    projectQuotationId: int = Field(..., description="项目报价ID")
    parameter: str = Field(..., description="参数")
    method: str = Field(..., description="方法")
    unitPrice: str = Field(..., description="特殊耗材单价")
    quantity: int = Field(..., description="数量")
    totalPrice: str = Field(..., description="总价")
    remark: Optional[str] = Field(None, description="备注")
    
    class Config:
        from_attributes = True
