-- 创建项目报价特殊耗材费用明细表
CREATE TABLE IF NOT EXISTS `project_quotation_special_consumable` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_quotation_id` int NOT NULL COMMENT '项目报价ID',
  `parameter` varchar(200) NOT NULL COMMENT '参数',
  `method` varchar(200) NOT NULL COMMENT '方法',
  `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '特殊耗材单价',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `total_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总价',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_quotation_id` (`project_quotation_id`),
  KEY `idx_parameter_method` (`parameter`, `method`),
  CONSTRAINT `fk_special_consumable_quotation` FOREIGN KEY (`project_quotation_id`) REFERENCES `project_quotation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目报价特殊耗材费用明细表';

-- 为项目报价总费用表添加特殊耗材总费用字段
ALTER TABLE `project_quotation_total_fee` 
ADD COLUMN `special_consumables_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '特殊耗材总费用' 
AFTER `discounted_testing_fee`;

-- 更新项目报价总费用表的注释
ALTER TABLE `project_quotation_total_fee` 
MODIFY COLUMN `total_fee_before_discount` decimal(10,2) NOT NULL DEFAULT '0' COMMENT '优惠前总费用=检测折后费用+特殊耗材费用+其他费用';
