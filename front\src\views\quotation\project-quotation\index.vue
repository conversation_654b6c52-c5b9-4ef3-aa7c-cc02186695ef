<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类别" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          placeholder="请选择业务类别"
          clearable
          style="width: 200px"
        >
          <el-option label="一般采样" value="sampling" />
          <el-option label="送样" value="sample" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="项目状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['quotation:project-quotation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quotation:project-quotation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quotation:project-quotation:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['quotation:project-quotation:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectQuotationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectCode" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="业务类别" align="center" prop="businessType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.businessType === 'sampling' ? 'primary' : 'success'">
            {{ scope.row.businessType === 'sampling' ? '一般采样' : '送样' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="合同编号" align="center" prop="contractCode" width="120" />
      <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
      <el-table-column label="项目负责人" align="center" prop="projectManager" width="100" />

      <el-table-column label="项目状态" align="center" prop="status" width="140">
        <template #default="scope">
          <el-tag
            :type="getStatusTagType(scope.row.detailedStatus || scope.row.status)"
            size="small"
          >
            {{ scope.row.detailedStatusLabel || getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleViewQuotation(scope.row)"
          >查看报价</el-button>
          <el-dropdown @command="(format) => handleExportDetail(scope.row, format)" v-hasPermi="['quotation:project-quotation:export']">
            <el-button type="text" icon="Download">
              导出报价单<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
                <!-- <el-dropdown-item command="word">导出Word</el-dropdown-item>
                <el-dropdown-item command="pdf">导出PDF</el-dropdown-item> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            v-if="canEdit(scope.row)"
            type="text"
            icon="Edit"
            @click="handleEditQuotation(scope.row)"
            v-hasPermi="['quotation:project-quotation:edit']"
          >编辑报价</el-button>
          <el-button
            v-if="canEdit(scope.row)"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quotation:project-quotation:edit']"
          >修改项目</el-button>
          <el-button
            v-if="scope.row.status !== '0'"
            type="text"
            icon="Document"
            @click="handleViewApprovalFlow(scope.row)"
            v-hasPermi="['quotation:project-quotation:query']"
          >审批流程</el-button>
          <el-button
            v-if="canSubmitApproval(scope.row)"
            type="text"
            icon="Check"
            @click="handleSubmitApproval(scope.row)"
            v-hasPermi="['quotation:project-quotation:edit']"
            style="color: #67C23A"
          >提交审批</el-button>
          <el-button
            v-if="canWithdrawApproval(scope.row)"
            type="text"
            icon="RefreshLeft"
            @click="handleWithdrawApproval(scope.row)"
            v-hasPermi="['quotation:project-quotation:edit']"
            style="color: #E6A23C"
          >撤回审批</el-button>
          <el-button
            v-if="canDelete(scope.row)"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quotation:project-quotation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看报价弹框 -->
    <edit-quotation-dialog
      :visible="editQuotationOpen"
      @update:visible="editQuotationOpen = $event"
      :quotation-id="currentQuotationId"
      @refresh="getList"
    />

    <!-- 查看项目报价弹框 -->
    <view-quotation-dialog
      :visible="viewQuotationOpen"
      @update:visible="viewQuotationOpen = $event"
      :quotation-id="currentQuotationId"
      @refresh="getList"
    />

    <!-- 修改项目信息弹框 -->
    <edit-project-info-dialog
      :visible="editProjectInfoOpen"
      @update:visible="editProjectInfoOpen = $event"
      :quotation-id="currentQuotationId"
      @refresh="getList"
    />

    <!-- 审批流程查看弹框 -->
    <approval-flow-dialog
      :visible="approvalFlowOpen"
      @update:visible="approvalFlowOpen = $event"
      :quotation-id="currentQuotationId"
    />

    <!-- 新增项目报价弹框 -->
    <add-project-quotation-dialog
      :visible="addQuotationOpen"
      @update:visible="addQuotationOpen = $event"
      @refresh="getList"
    />


  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import {
  pageProjectQuotation,
  delProjectQuotation,
  exportProjectQuotation,
  exportProjectQuotationDetail,
  submitProjectQuotationApproval,
  withdrawProjectQuotationApproval,
  checkProjectQuotationEditPermission
} from "@/api/quotation/projectQuotation";
import EditQuotationDialog from './components/EditQuotationDialog.vue';
import ViewQuotationDialog from './components/ViewQuotationDialog.vue';
import EditProjectInfoDialog from './components/EditProjectInfoDialog.vue';
import AddProjectQuotationDialog from './components/AddProjectQuotationDialog.vue';
import ApprovalFlowDialog from './components/ApprovalFlowDialog.vue';
import { useRouter } from 'vue-router'
import useUserStore from '@/store/modules/user'

const router = useRouter()
const { proxy } = getCurrentInstance()
const userStore = useUserStore()

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 项目报价表格数据
const projectQuotationList = ref([])
// 日期范围
const dateRange = ref([])
// 状态数据字典
const statusOptions = ref([
  { value: '0', label: '草稿' },
  { value: '1', label: '待审核' },
  { value: '2', label: '审核完成' },
  { value: '3', label: '已撤回' },
  { value: '4', label: '已拒绝' }
])

// 权限缓存
const editPermissionCache = ref(new Map())

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectName: undefined,
  projectCode: undefined,
  customerName: undefined,
  businessType: undefined,
  status: undefined,
  beginTime: undefined,
  endTime: undefined
})

/** 查询项目报价列表 */
function getList() {
  loading.value = true
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.beginTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  } else {
    queryParams.value.beginTime = undefined
    queryParams.value.endTime = undefined
  }
  pageProjectQuotation(queryParams.value).then(async response => {
    projectQuotationList.value = response.data.rows
    total.value = response.data.total
    
    // 批量检查编辑权限
    await checkEditPermissions(response.data.rows)
    
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  // 打开新增项目报价弹框
  addQuotationOpen.value = true
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const id = row.id || ids.value[0]
  // 弹出修改项目信息弹框
  currentQuotationId.value = id
  editProjectInfoOpen.value = true
  // 不再跳转到新页面
  // router.push({ path: `/quotation/project-quotation/edit/${id}` })
}


/** 删除按钮操作 */
function handleDelete(row) {
  const projectIds = row.id || ids.value
  ElMessageBox.confirm('是否确认删除项目报价编号为"' + projectIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delProjectQuotation(projectIds)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  const queryParams = {
    projectName: queryParams.value.projectName,
    projectCode: queryParams.value.projectCode,
    customerName: queryParams.value.customerName,
    status: queryParams.value.status,
    beginTime: queryParams.value.beginTime,
    endTime: queryParams.value.endTime
  }
  ElMessageBox.confirm('是否确认导出所有项目报价数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return exportProjectQuotation(queryParams)
  }).then(response => {
    proxy.download(response)
  }).catch(() => {})
}



// 查看报价对话框
const editQuotationOpen = ref(false)
// 查看项目报价对话框
const viewQuotationOpen = ref(false)
// 修改项目信息对话框
const editProjectInfoOpen = ref(false)
// 新增项目报价对话框
const addQuotationOpen = ref(false)
// 审批流程查看对话框
const approvalFlowOpen = ref(false)
// 当前查看的报价ID
const currentQuotationId = ref(null)

/** 查看报价按钮操作 */
function handleViewQuotation(row) {
  const id = row.id || ids.value[0]
  currentQuotationId.value = id
  viewQuotationOpen.value = true
}

/** 编辑报价按钮操作 */
function handleEditQuotation(row) {
  const id = row.id || ids.value[0]
  currentQuotationId.value = id
  editQuotationOpen.value = true
}



/** 查看审批流程按钮操作 */
function handleViewApprovalFlow(row) {
  const id = row.id || ids.value[0]
  currentQuotationId.value = id
  approvalFlowOpen.value = true
}

/** 检查是否可以编辑 */
function canEdit(row) {
  // 审核中(1)和审核完成(2)状态的项目不能修改（兼容字符串和数字）
  if (String(row.status) === '1' || String(row.status) === '2') {
    return false
  }
  
  // 优先使用权限缓存
  if (editPermissionCache.value.has(row.id)) {
    return editPermissionCache.value.get(row.id)
  }
  
  // 如果缓存中没有，使用原有逻辑作为后备
  // 管理员可以编辑所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以编辑
  return row.createBy === userStore.name
}

/** 检查是否可以删除 */
function canDelete(row) {
  // 管理员可以删除所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以删除
  return row.createBy === userStore.name
}

/** 检查是否可以提交审批 */
function canSubmitApproval(row) {
  // 只有草稿状态的项目才能提交审批（兼容字符串和数字）
  if (String(row.status) !== '0') {
    return false
  }
  // 管理员可以提交所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以提交审批
  return row.createBy === userStore.name
}

/** 检查是否可以撤回审批 */
function canWithdrawApproval(row) {
  // 只有待审核状态的项目才能撤回审批（兼容字符串和数字）
  if (String(row.status) !== '1') {
    return false
  }
  // 管理员可以撤回所有项目
  if (userStore.roles.includes('admin')) {
    return true
  }
  // 只有创建人可以撤回审批
  return row.createBy === userStore.name
}

/** 提交审批按钮操作 */
function handleSubmitApproval(row) {
  const id = row.id
  const projectName = row.projectName
  const businessType = row.businessType || 'sampling'

  // 根据业务类型显示不同的审批流程说明
  const approvalFlow = businessType === 'sample'
    ? '市场审批 → 实验室审批（现场审批可选）'
    : '市场审批 → 实验室审批 → 现场审批'

  ElMessageBox.confirm(
    `确认提交项目"${projectName}"的审批申请吗？\n\n业务类型：${businessType === 'sample' ? '送样' : '一般采样'}\n审批流程：${approvalFlow}\n\n提交后将进入市场审批阶段。`,
    "提交审批确认",
    {
      confirmButtonText: "确定提交",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: false
    }
  ).then(() => {
    // 直接提交审批
    confirmSubmitApproval(id)
  }).catch(() => {
    // 用户取消操作
  })
}

/** 确认提交审批 */
function confirmSubmitApproval(id) {
  loading.value = true
  submitProjectQuotationApproval(id).then(response => {
    ElMessage.success(response.msg || "项目已成功提交审批，进入市场审批阶段")
    getList() // 刷新列表
  }).catch(error => {
    ElMessage.error(error.message || "提交审批失败")
  }).finally(() => {
    loading.value = false
  })
}

/** 撤回审批按钮操作 */
function handleWithdrawApproval(row) {
  const id = row.id
  const projectName = row.projectName
  const businessType = row.businessType || 'sampling'

  // 根据业务类型显示不同的审批流程说明
  const approvalFlow = businessType === 'sample'
    ? '市场审批 → 实验室审批（现场审批可选）'
    : '市场审批 → 实验室审批 → 现场审批'

  ElMessageBox.confirm(
    `确认撤回项目"${projectName}"的审批申请吗？\n\n当前审批流程：${approvalFlow}\n\n撤回后项目将恢复为草稿状态，可以重新修改和提交。`,
    "撤回审批确认",
    {
      confirmButtonText: "确定撤回",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: false
    }
  ).then(() => {
    // 执行撤回操作
    confirmWithdrawApproval(id)
  }).catch(() => {
    // 用户取消操作
  })
}

/** 确认撤回审批 */
function confirmWithdrawApproval(id) {
  loading.value = true
  withdrawProjectQuotationApproval(id).then(response => {
    ElMessage.success(response.msg || "项目审批已撤回，项目状态已恢复为草稿")
    getList() // 刷新列表
  }).catch(error => {
    ElMessage.error(error.message || "撤回审批失败")
  }).finally(() => {
    loading.value = false
  })
}

/** 获取状态标签 */
function getStatusLabel(status) {
  const statusMap = {
    '0': '草稿',
    '1': '待审核',
    '2': '审核完成',
    '3': '已撤回',
    '4': '已拒绝',
    '1-market': '待审核（市场）',
    '1-order_confirm': '待审核（项目成单确认）',
    '1-lab': '待审核（实验室）',
    '1-field': '待审核（现场）',
    '1-lab|field': '待审核（实验室|现场）'
  }
  return statusMap[status] || '未知'
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  const statusStr = String(status)
  if (statusStr === '0') return 'info'           // 草稿 - 灰色
  if (statusStr === '2') return 'success'        // 审核完成 - 绿色
  if (statusStr === '3') return 'warning'        // 已撤回 - 橙色
  if (statusStr === '4') return 'danger'         // 已拒绝 - 红色
  if (statusStr === '1-market') return 'primary' // 市场审批 - 蓝色
  if (statusStr === '1-order_confirm') return 'primary' // 项目成单确认 - 蓝色
  if (statusStr === '1-lab') return 'primary'    // 实验室审批 - 蓝色
  if (statusStr === '1-field') return 'primary'  // 现场审批 - 蓝色
  if (statusStr === '1-lab|field') return 'primary' // 实验室|现场审批 - 蓝色
  return 'primary'                            // 默认待审核 - 蓝色
}

/** 导出项目报价单详情 */
async function handleExportDetail(row, format = 'excel') {
  try {
    const formatNames = {
      excel: 'Excel',
      word: 'Word',
      pdf: 'PDF'
    }

    ElMessage.info(`正在生成${formatNames[format]}报价单，请稍候...`)
    const response = await exportProjectQuotationDetail(row.id, format)

    // 根据格式设置MIME类型和文件扩展名
    const mimeTypes = {
      excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      word: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      pdf: 'application/pdf'
    }

    const extensions = {
      excel: 'xlsx',
      word: 'docx',
      pdf: 'pdf'
    }

    // 创建下载链接
    const blob = new Blob([response], {
      type: mimeTypes[format]
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${row.projectName || '项目报价单'}_${new Date().toISOString().slice(0, 10)}.${extensions[format]}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success(`${formatNames[format]}报价单导出成功`)
  } catch (error) {
    console.error('导出报价单失败:', error)
    ElMessage.error('导出报价单失败，请重试')
  }
}

/** 批量检查编辑权限 */
async function checkEditPermissions(quotations) {
  // 清空权限缓存
  editPermissionCache.value.clear()

  // 并发检查所有报价单的编辑权限
  const permissionPromises = quotations.map(async (quotation) => {
    try {
      const response = await checkProjectQuotationEditPermission(quotation.id)
      if (response.code === 200) {
        editPermissionCache.value.set(quotation.id, response.data.canEdit)
      } else {
        // 如果API调用失败，设置为false
        editPermissionCache.value.set(quotation.id, false)
      }
    } catch (error) {
      // 如果出现异常，设置为false
      editPermissionCache.value.set(quotation.id, false)
      console.warn(`检查报价单 ${quotation.id} 编辑权限失败:`, error)
    }
  })

  // 等待所有权限检查完成
  await Promise.all(permissionPromises)
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box-card {
  margin-bottom: 20px;
}
</style>