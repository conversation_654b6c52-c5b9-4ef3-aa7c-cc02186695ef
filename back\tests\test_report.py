import pytest
from datetime import datetime, date
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from module_report.entity.do.report_do import Report
from module_report.service.report_service import ReportService
from module_report.dao.report_dao import ReportDao


class TestReportService:
    """周报月报服务层测试"""

    @pytest.mark.asyncio
    async def test_add_weekly_report(self, async_session: AsyncSession, test_user):
        """测试新增周报"""
        # 准备测试数据
        report_data = {
            'report_type': 'weekly',
            'report_date': datetime(2024, 1, 1),
            'summary': '本周完成了项目A的开发工作',
            'plan': '下周计划完成项目B的设计',
            'problems': '遇到了技术难题',
            'support_needed': '需要技术支持',
            'is_saturated': '1',
            'remark': '测试备注'
        }
        
        # 创建报告对象
        report = Report(**report_data, reporter_id=test_user['user_id'])
        
        # 测试新增
        result = await ReportDao.add_report(async_session, report)
        await async_session.commit()
        
        assert result.report_id is not None
        assert result.report_type == 'weekly'
        assert result.summary == '本周完成了项目A的开发工作'
        assert result.is_saturated == '1'

    @pytest.mark.asyncio
    async def test_add_monthly_report(self, async_session: AsyncSession, test_user):
        """测试新增月报"""
        # 准备测试数据
        report_data = {
            'report_type': 'monthly',
            'report_date': datetime(2024, 1, 1),
            'summary': '本月完成了多个项目',
            'plan': '下月计划启动新项目',
            'problems': '资源不足',
            'support_needed': '需要人力支持',
            'is_saturated': None,  # 月报不需要饱和度
            'remark': '月报测试'
        }
        
        # 创建报告对象
        report = Report(**report_data, reporter_id=test_user['user_id'])
        
        # 测试新增
        result = await ReportDao.add_report(async_session, report)
        await async_session.commit()
        
        assert result.report_id is not None
        assert result.report_type == 'monthly'
        assert result.summary == '本月完成了多个项目'
        assert result.is_saturated is None

    @pytest.mark.asyncio
    async def test_get_report_by_id(self, async_session: AsyncSession, test_user):
        """测试根据ID获取报告"""
        # 先创建一个报告
        report = Report(
            report_type='weekly',
            report_date=datetime(2024, 1, 1),
            reporter_id=test_user['user_id'],
            summary='测试总结',
            plan='测试计划',
            is_saturated='1'
        )
        
        created_report = await ReportDao.add_report(async_session, report)
        await async_session.commit()
        
        # 测试获取
        result = await ReportDao.get_report_by_id(async_session, created_report.report_id)
        
        assert result is not None
        assert result.report_id == created_report.report_id
        assert result.summary == '测试总结'

    @pytest.mark.asyncio
    async def test_check_report_unique(self, async_session: AsyncSession, test_user):
        """测试报告唯一性校验"""
        # 先创建一个报告
        report = Report(
            report_type='weekly',
            report_date=datetime(2024, 1, 1),
            reporter_id=test_user['user_id'],
            summary='测试总结',
            plan='测试计划',
            is_saturated='1'
        )
        
        await ReportDao.add_report(async_session, report)
        await async_session.commit()
        
        # 测试唯一性校验
        is_unique = await ReportService.check_report_unique_services(
            async_session,
            datetime(2024, 1, 1),
            test_user['user_id'],
            'weekly'
        )
        
        assert is_unique is False  # 应该不唯一，因为已存在

    @pytest.mark.asyncio
    async def test_update_report(self, async_session: AsyncSession, test_user):
        """测试更新报告"""
        # 先创建一个报告
        report = Report(
            report_type='weekly',
            report_date=datetime(2024, 1, 1),
            reporter_id=test_user['user_id'],
            summary='原始总结',
            plan='原始计划',
            is_saturated='0'
        )
        
        created_report = await ReportDao.add_report(async_session, report)
        await async_session.commit()
        
        # 更新报告
        created_report.summary = '更新后的总结'
        created_report.plan = '更新后的计划'
        created_report.is_saturated = '1'
        
        result = await ReportDao.update_report(async_session, created_report)
        await async_session.commit()
        
        assert result > 0  # 更新成功
        
        # 验证更新结果
        updated_report = await ReportDao.get_report_by_id(async_session, created_report.report_id)
        assert updated_report.summary == '更新后的总结'
        assert updated_report.plan == '更新后的计划'
        assert updated_report.is_saturated == '1'

    @pytest.mark.asyncio
    async def test_delete_report(self, async_session: AsyncSession, test_user):
        """测试删除报告"""
        # 先创建一个报告
        report = Report(
            report_type='weekly',
            report_date=datetime(2024, 1, 1),
            reporter_id=test_user['user_id'],
            summary='待删除的报告',
            plan='待删除的计划',
            is_saturated='1'
        )
        
        created_report = await ReportDao.add_report(async_session, report)
        await async_session.commit()
        
        # 删除报告
        result = await ReportDao.delete_report(async_session, [created_report.report_id])
        await async_session.commit()
        
        assert result > 0  # 删除成功
        
        # 验证删除结果
        deleted_report = await ReportDao.get_report_by_id(async_session, created_report.report_id)
        assert deleted_report is None  # 应该找不到了


class TestReportAPI:
    """周报月报API测试"""

    @pytest.mark.asyncio
    async def test_create_weekly_report_api(self, async_client: AsyncClient, auth_headers):
        """测试创建周报API"""
        report_data = {
            'reportType': 'weekly',
            'reportDate': '2024-01-01',
            'summary': 'API测试周报总结',
            'plan': 'API测试周报计划',
            'problems': 'API测试问题',
            'supportNeeded': 'API测试支持',
            'isSaturated': '1',
            'remark': 'API测试备注'
        }
        
        response = await async_client.post(
            '/report',
            json=report_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['code'] == 200
        assert data['data']['reportType'] == 'weekly'

    @pytest.mark.asyncio
    async def test_get_report_list_api(self, async_client: AsyncClient, auth_headers):
        """测试获取报告列表API"""
        response = await async_client.get(
            '/report/page?reportType=weekly&pageNum=1&pageSize=10',
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['code'] == 200
        assert 'rows' in data['data']
        assert 'total' in data['data']

    @pytest.mark.asyncio
    async def test_create_monthly_report_api(self, async_client: AsyncClient, auth_headers):
        """测试创建月报API"""
        report_data = {
            'reportType': 'monthly',
            'reportDate': '2024-01-01',
            'summary': 'API测试月报总结',
            'plan': 'API测试月报计划',
            'problems': 'API测试问题',
            'supportNeeded': 'API测试支持',
            'remark': 'API测试备注'
        }
        
        response = await async_client.post(
            '/report',
            json=report_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['code'] == 200
        assert data['data']['reportType'] == 'monthly'
        assert data['data']['isSaturated'] is None  # 月报不应该有饱和度
