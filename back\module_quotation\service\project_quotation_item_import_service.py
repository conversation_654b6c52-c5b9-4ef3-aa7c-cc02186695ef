"""
项目报价明细批量导入服务
"""

import io
from typing import List, Dict, Any, Optional
import pandas as pd
from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.vo.user_vo import CurrentUserModel
from module_quotation.entity.vo.project_quotation_vo import (
    ProjectQuotationItemImportModel,
    ProjectQuotationItemImportErrorModel,
    ProjectQuotationItemImportResultModel,
    ProjectQuotationItemModel,
)


class ProjectQuotationItemImportService:
    """
    项目报价明细批量导入服务
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def import_from_excel(
        self, file: UploadFile, current_user: CurrentUserModel
    ) -> ProjectQuotationItemImportResultModel:
        """
        从Excel文件导入项目报价明细数据

        :param file: 上传的Excel文件
        :param current_user: 当前用户
        :return: 导入结果
        """
        try:
            # 读取Excel文件
            content = await file.read()
            df = pd.read_excel(io.BytesIO(content))

            # 验证Excel格式
            required_columns = [
                "分类",
                "二级分类",
                "指标",
                "方法",
                "样品来源",
                "点位名称",
                "点位数",
                "周期类型",
                "检测周期数",
                "检测频次数",
                "样品数",
                "是否分包",
                "备注",
            ]

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return ProjectQuotationItemImportResultModel(
                    success=False,
                    total_count=0,
                    success_count=0,
                    error_count=1,
                    errors=[
                        ProjectQuotationItemImportErrorModel(
                            row_number=0,
                            error_message=f"Excel模板格式错误，缺少列：{', '.join(missing_columns)}",
                            classification=None,
                            category=None,
                            parameter=None,
                            method=None,
                            sample_source=None,
                            point_name=None,
                            point_count=None,
                            cycle_type=None,
                            cycle_count=None,
                            frequency=None,
                            sample_count=None,
                            is_subcontract=None,
                            remark=None,
                        )
                    ],
                    data=[],
                )

            # 处理数据
            total_count = len(df)
            success_count = 0
            errors = []
            success_data = []

            for index, row in df.iterrows():
                try:
                    # 验证和转换数据
                    import_data = await self._validate_and_convert_row(row, index + 2)  # +2因为Excel从第2行开始

                    # 转换为ProjectQuotationItemModel
                    quotation_item = ProjectQuotationItemModel(
                        classification=import_data.classification,
                        category=import_data.category,
                        parameter=import_data.parameter,
                        method=import_data.method,
                        sample_source=import_data.sample_source,
                        point_name=import_data.point_name,
                        point_count=(
                            int(import_data.point_count)
                            if import_data.point_count and import_data.point_count.isdigit()
                            else 1
                        ),
                        cycle_type=import_data.cycle_type,
                        cycle_count=(
                            int(import_data.cycle_count)
                            if import_data.cycle_count and import_data.cycle_count.isdigit()
                            else 1
                        ),
                        frequency=(
                            int(import_data.frequency)
                            if import_data.frequency and import_data.frequency.isdigit()
                            else 1
                        ),
                        sample_count=(
                            int(import_data.sample_count)
                            if import_data.sample_count and import_data.sample_count.isdigit()
                            else 1
                        ),
                        is_subcontract="1" if import_data.is_subcontract in ["是", "1"] else "0",
                        remark=import_data.remark,
                    )

                    success_data.append(quotation_item)
                    success_count += 1

                except Exception as e:
                    errors.append(
                        ProjectQuotationItemImportErrorModel(
                            row_number=index + 2,
                            error_message=str(e),
                            classification=str(row.get("分类", "")),
                            category=str(row.get("二级分类", "")),
                            parameter=str(row.get("指标", "")),
                            method=str(row.get("方法", "")),
                            sample_source=str(row.get("样品来源", "")),
                            point_name=str(row.get("点位名称", "")),
                            point_count=str(row.get("点位数", "")),
                            cycle_type=str(row.get("周期类型", "")),
                            cycle_count=str(row.get("检测周期数", "")),
                            frequency=str(row.get("检测频次数", "")),
                            sample_count=str(row.get("样品数", "")),
                            is_subcontract=str(row.get("是否分包", "")),
                            remark=str(row.get("备注", "")),
                        )
                    )

            return ProjectQuotationItemImportResultModel(
                success=len(errors) == 0,
                total_count=total_count,
                success_count=success_count,
                error_count=len(errors),
                errors=errors,
                data=success_data,
            )

        except Exception as e:
            return ProjectQuotationItemImportResultModel(
                success=False,
                total_count=0,
                success_count=0,
                error_count=1,
                errors=[
                    ProjectQuotationItemImportErrorModel(
                        row_number=0,
                        error_message=f"文件处理失败：{str(e)}",
                        classification=None,
                        category=None,
                        parameter=None,
                        method=None,
                        sample_source=None,
                        point_name=None,
                        point_count=None,
                        cycle_type=None,
                        cycle_count=None,
                        frequency=None,
                        sample_count=None,
                        is_subcontract=None,
                        remark=None,
                    )
                ],
                data=[],
            )

    async def _validate_and_convert_row(self, row: pd.Series, row_number: int) -> ProjectQuotationItemImportModel:
        """
        验证和转换行数据

        :param row: 行数据
        :param row_number: 行号
        :return: 转换后的数据模型
        """
        # 必填字段验证
        if pd.isna(row["二级分类"]) or str(row["二级分类"]).strip() == "":
            raise ValueError("二级分类不能为空")

        if pd.isna(row["指标"]) or str(row["指标"]).strip() == "":
            raise ValueError("指标不能为空")

        if pd.isna(row["方法"]) or str(row["方法"]).strip() == "":
            raise ValueError("方法不能为空")

        # 数值字段验证
        point_count = str(row.get("点位数", "1")).strip()
        if point_count and not point_count.isdigit():
            raise ValueError("点位数必须为正整数")

        cycle_count = str(row.get("检测周期数", "1")).strip()
        if cycle_count and not cycle_count.isdigit():
            raise ValueError("检测周期数必须为正整数")

        frequency = str(row.get("检测频次数", "1")).strip()
        if frequency and not frequency.isdigit():
            raise ValueError("检测频次数必须为正整数")

        sample_count = str(row.get("样品数", "1")).strip()
        if sample_count and not sample_count.isdigit():
            raise ValueError("样品数必须为正整数")

        # 是否分包字段验证
        is_subcontract = str(row.get("是否分包", "否")).strip()
        if is_subcontract not in ["是", "否", "0", "1"]:
            raise ValueError("是否分包字段值无效，应为：是/否 或 0/1")

        return ProjectQuotationItemImportModel(
            classification=str(row.get("分类", "")).strip() if not pd.isna(row.get("分类")) else None,
            category=str(row["二级分类"]).strip(),
            parameter=str(row["指标"]).strip(),
            method=str(row["方法"]).strip(),
            sample_source=str(row.get("样品来源", "")).strip() if not pd.isna(row.get("样品来源")) else None,
            point_name=str(row.get("点位名称", "")).strip() if not pd.isna(row.get("点位名称")) else None,
            point_count=point_count if point_count else "1",
            cycle_type=str(row.get("周期类型", "")).strip() if not pd.isna(row.get("周期类型")) else None,
            cycle_count=cycle_count if cycle_count else "1",
            frequency=frequency if frequency else "1",
            sample_count=sample_count if sample_count else "1",
            is_subcontract=is_subcontract,
            remark=str(row.get("备注", "")).strip() if not pd.isna(row.get("备注")) else None,
        )

    async def generate_template(self) -> bytes:
        """
        生成Excel导入模板

        :return: Excel文件字节数据
        """
        # 创建示例数据
        template_data = {
            "分类": ["气", "水", "土壤"],
            "二级分类": ["环境空气和废气", "水和废水", "土壤和沉积物"],
            "指标": ["PM2.5", "pH值", "重金属"],
            "方法": ["重量法", "玻璃电极法", "原子吸收分光光度法"],
            "样品来源": ["送样", "采样", "送样"],
            "点位名称": ["监测点1", "监测点2", "监测点3"],
            "点位数": [1, 2, 1],
            "周期类型": ["天", "月", "季度"],
            "检测周期数": [1, 1, 1],
            "检测频次数": [1, 2, 1],
            "样品数": [1, 1, 1],
            "是否分包": ["否", "否", "是"],
            "备注": ["常规监测", "重点监测", "特殊监测"],
        }

        df = pd.DataFrame(template_data)

        # 保存到字节流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="项目报价明细导入模板", index=False)

        output.seek(0)
        return output.getvalue()
