import request from '@/utils/request'

// 获取特殊耗材费用明细列表
export function getSpecialConsumableList(quotationId) {
  return request({
    url: '/quotation/special-consumable/list',
    method: 'get',
    params: { quotation_id: quotationId }
  })
}

// 添加特殊耗材费用明细
export function addSpecialConsumable(quotationId, data) {
  return request({
    url: '/quotation/special-consumable/add',
    method: 'post',
    params: { quotation_id: quotationId },
    data
  })
}

// 编辑特殊耗材费用明细
export function editSpecialConsumable(data) {
  return request({
    url: '/quotation/special-consumable/edit',
    method: 'put',
    data
  })
}

// 删除特殊耗材费用明细
export function deleteSpecialConsumable(specialConsumableId) {
  return request({
    url: '/quotation/special-consumable/delete',
    method: 'delete',
    params: { special_consumable_id: specialConsumableId }
  })
}
