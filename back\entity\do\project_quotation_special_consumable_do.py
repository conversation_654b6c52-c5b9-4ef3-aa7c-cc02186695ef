"""
项目报价特殊耗材费用明细 DO
"""
from sqlalchemy import Column, Integer, String, Decimal, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from entity.do.base_do import BaseDO


class ProjectQuotationSpecialConsumable(BaseDO):
    """项目报价特殊耗材费用明细"""
    
    __tablename__ = "project_quotation_special_consumable"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey("project_quotation.id"), nullable=False, comment="项目报价ID")
    parameter = Column(String(200), nullable=False, comment="参数")
    method = Column(String(200), nullable=False, comment="方法")
    unit_price = Column(Decimal(10, 2), nullable=False, default=0.00, comment="特殊耗材单价")
    quantity = Column(Integer, nullable=False, default=1, comment="数量")
    total_price = Column(Decimal(10, 2), nullable=False, default=0.00, comment="总价")
    remark = Column(Text, comment="备注")
    
    # 关联关系
    project_quotation = relationship("ProjectQuotation", back_populates="special_consumables")
    
    def __repr__(self):
        return f"<ProjectQuotationSpecialConsumable(id={self.id}, parameter='{self.parameter}', method='{self.method}', unit_price={self.unit_price}, quantity={self.quantity})>"
