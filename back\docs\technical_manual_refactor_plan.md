# 技术手册重构方案文档

## 重构背景

技术手册中检测类别存在多个，需要拆解关联表：技术手册类目表，同时将技术价格中的特殊耗材单价字段迁移到技术手册中。

## 需求分析

### 需求2：技术手册类目表
- 从技术手册中抽出新表：技术手册类目表
- 字段：类目唯一编号、分类、检测类别
- 当技术手册新增时，检测类别可能为逗号分割，需要组合成列表，分别插入到技术手册类目表中
- 逻辑：根据分类和检测类别判断是否存在，不存在则插入且生成唯一的类目唯一编号
- 编号规则：CATE+5位递增数，如CATE00001
- 技术手册中删除分类、检测类别字段，由类目唯一编号进行关联查询

### 需求3：前端查询调整
- 技术手册前端查询分类、检测类别字段从技术手册类目表中关联查询

### 需求4：字段迁移
- 技术价格中的"分析特殊耗材单价"字段移动到技术手册中

## 实现方案

### 1. 数据库设计

#### 1.1 新建技术手册类目表
```sql
CREATE TABLE technical_manual_category (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    category_code VARCHAR(20) NOT NULL UNIQUE COMMENT '类目唯一编号',
    classification VARCHAR(50) NOT NULL COMMENT '分类',
    category VARCHAR(50) NOT NULL COMMENT '检测类别',
    status VARCHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    del_flag VARCHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    UNIQUE INDEX idx_technical_manual_category_unique (classification, category)
);
```

#### 1.2 修改技术手册表
```sql
-- 添加类目编号字段
ALTER TABLE technical_manual ADD COLUMN category_code VARCHAR(20) AFTER test_code;

-- 添加特殊耗材单价字段
ALTER TABLE technical_manual ADD COLUMN special_consumables_price DECIMAL(10,2) AFTER has_qualification;

-- 后续可以删除分类和检测类别字段（数据迁移完成后）
-- ALTER TABLE technical_manual DROP COLUMN classification;
-- ALTER TABLE technical_manual DROP COLUMN category;
```

#### 1.3 修改技术手册价格表
```sql
-- 移除特殊耗材单价字段（迁移到技术手册表后）
-- ALTER TABLE technical_manual_price DROP COLUMN special_consumables_price;
```

### 2. 后端实现

#### 2.1 创建技术手册类目表相关文件

**DO模型**: `technical_manual_category_do.py`
- 定义技术手册类目表的数据库模型
- 包含类目编号、分类、检测类别等字段
- 设置唯一索引：分类+检测类别组合唯一

**VO模型**: `technical_manual_category_vo.py`
- 定义各种业务模型：查询、新增、编辑、删除等
- 支持驼峰命名转换

**DAO层**: `technical_manual_category_dao.py`
- 实现类目表的数据访问逻辑
- 提供根据分类和检测类别查询、批量创建等方法
- 自动生成类目编号（CATE+5位递增数）

**Service层**: `technical_manual_category_service.py`
- 实现类目表的业务逻辑
- 提供分类和检测类别的选项数据
- 支持批量创建类目（如果不存在）

**Controller层**: `technical_manual_category_controller.py`
- 提供类目表的REST API接口
- 支持增删改查、分页查询、选项数据获取等

#### 2.2 修改技术手册相关文件

**DO模型更新**: `technical_manual_do.py`
- 移除classification、category字段
- 添加category_code字段（关联类目表）
- 添加special_consumables_price字段

**VO模型更新**: `technical_manual_vo.py`
- 更新所有模型，使用category_code替代category
- 添加special_consumables_price字段
- 保留classification、category字段用于关联查询显示

**Service层更新**: `technical_manual_service.py`
- 在新增技术手册时，自动处理类目表的创建
- 支持逗号分割的检测类别批量处理
- 查询时关联类目表获取分类和检测类别信息

#### 2.3 数据迁移脚本

**迁移脚本**: `migrate_technical_manual_category.py`
- 从现有技术手册表中提取分类和检测类别数据
- 生成类目编号并插入类目表
- 更新技术手册表的类目编号字段
- 迁移技术手册价格表中的特殊耗材单价到技术手册表

### 3. 前端实现

#### 3.1 API接口调整
- 新增技术手册类目表相关API
- 修改技术手册查询API，支持通过分类和检测类别查询

#### 3.2 组件更新
- 技术手册查询组件：通过类目表API获取分类和检测类别选项
- 技术手册表单组件：支持分类和检测类别的级联选择
- 添加特殊耗材单价字段的输入和显示

### 4. 实施步骤

#### 第一阶段：数据库和后端准备
1. ✅ 创建技术手册类目表DO模型
2. ✅ 创建技术手册类目表VO模型
3. ✅ 创建技术手册类目表DAO层
4. ✅ 创建技术手册类目表Service层
5. ✅ 创建技术手册类目表Controller层
6. ✅ 更新技术手册DO模型
7. ✅ 更新技术手册VO模型
8. ✅ 创建数据迁移脚本

#### 第二阶段：数据迁移
1. 🔄 执行数据迁移脚本
2. 🔄 验证数据迁移结果
3. 🔄 更新技术手册Service层逻辑

#### 第三阶段：前端调整
1. 🔄 更新技术手册API调用
2. 🔄 修改技术手册查询组件
3. 🔄 修改技术手册表单组件
4. 🔄 添加特殊耗材单价字段处理

#### 第四阶段：测试和优化
1. 🔄 单元测试
2. 🔄 集成测试
3. 🔄 性能测试
4. 🔄 用户验收测试

### 5. 关键技术点

#### 5.1 类目编号生成
```python
def get_next_category_code(self) -> str:
    # 查询最大的类目编号
    max_code = self.db.query(func.max(TechnicalManualCategory.category_code)).scalar()
    
    if max_code:
        # 提取数字部分并加1
        try:
            num_part = int(max_code[4:])  # 去掉CATE前缀
            next_num = num_part + 1
        except (ValueError, IndexError):
            next_num = 1
    else:
        next_num = 1
    
    # 格式化为5位数字
    return f"CATE{next_num:05d}"
```

#### 5.2 批量创建类目
```python
def batch_create_if_not_exists(self, classification: str, categories: List[str]) -> List[TechnicalManualCategory]:
    created_categories = []
    
    for category in categories:
        # 检查是否已存在
        existing = self.get_by_classification_and_category(classification, category)
        if not existing:
            # 创建新的类目
            category_data = TechnicalManualCategoryModel(
                classification=classification,
                category=category,
                category_code=self.get_next_category_code()
            )
            new_category = self.create(category_data)
            created_categories.append(new_category)
        else:
            created_categories.append(existing)
    
    return created_categories
```

#### 5.3 关联查询
```python
# 查询技术手册时关联类目表获取分类和检测类别
def get_technical_manual_with_category(self, manual_id: int):
    result = self.db.query(TechnicalManual, TechnicalManualCategory).join(
        TechnicalManualCategory,
        TechnicalManual.category_code == TechnicalManualCategory.category_code
    ).filter(TechnicalManual.id == manual_id).first()
    
    if result:
        manual, category = result
        manual_dict = manual.__dict__
        manual_dict['classification'] = category.classification
        manual_dict['category'] = category.category
        return manual_dict
    
    return None
```

### 6. 注意事项

#### 6.1 数据一致性
- 在迁移过程中确保数据的完整性和一致性
- 迁移前备份数据
- 提供回滚机制

#### 6.2 性能考虑
- 类目表数据量相对较小，查询性能良好
- 技术手册查询时需要关联类目表，注意索引优化
- 批量操作时注意事务处理

#### 6.3 兼容性
- 保持API接口的向后兼容性
- 前端组件渐进式升级
- 提供数据验证和错误处理

### 7. 测试计划

#### 7.1 单元测试
- 类目表CRUD操作测试
- 类目编号生成测试
- 批量创建类目测试

#### 7.2 集成测试
- 技术手册与类目表关联查询测试
- 数据迁移脚本测试
- API接口测试

#### 7.3 性能测试
- 大量数据下的查询性能测试
- 并发操作测试

### 8. 部署方案

#### 8.1 数据库变更
1. 创建技术手册类目表
2. 修改技术手册表结构
3. 执行数据迁移脚本

#### 8.2 应用部署
1. 部署后端代码
2. 更新前端代码
3. 验证功能正常

#### 8.3 回滚方案
1. 保留原有字段作为备份
2. 提供数据回滚脚本
3. 应用版本回滚机制

## 总结

本重构方案通过引入技术手册类目表，实现了技术手册中分类和检测类别的规范化管理，同时完成了特殊耗材单价字段的迁移。整个方案考虑了数据一致性、性能优化、兼容性等多个方面，提供了完整的实施步骤和测试计划。
