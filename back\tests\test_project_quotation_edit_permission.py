#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目报价修改权限控制测试
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from config.database import async_engine
from config.get_db import get_db
from module_admin.entity.do.user_do import User
from module_admin.entity.vo.user_vo import CurrentUserModel, UserModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_customer_support_do import ProjectQuotationCustomerSupport
from module_quotation.entity.vo.project_quotation_vo import EditProjectQuotationModel
from module_quotation.service.project_quotation_service import ProjectQuotationService
from exceptions.exception import ServiceException


class TestProjectQuotationEditPermission:
    """项目报价修改权限控制测试类"""

    @pytest.fixture
    async def db_session(self):
        """创建数据库会话"""
        async with async_engine.begin() as conn:
            async_session = AsyncSession(conn, expire_on_commit=False)
            yield async_session
            await async_session.rollback()

    @pytest.fixture
    async def test_users(self, db_session: AsyncSession):
        """创建测试用户"""
        # 创建项目创建人
        creator_user = User(
            user_id=1001,
            user_name="test_creator",
            nick_name="测试创建人",
            email="<EMAIL>",
            phone="13800001001",
            sex="1",
            status="0",
            admin=False,
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        db_session.add(creator_user)

        # 创建客服用户
        support_user = User(
            user_id=1002,
            user_name="test_support",
            nick_name="测试客服",
            email="<EMAIL>",
            phone="13800001002",
            sex="1",
            status="0",
            admin=False,
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        db_session.add(support_user)

        # 创建普通用户
        normal_user = User(
            user_id=1003,
            user_name="test_normal",
            nick_name="测试普通用户",
            email="<EMAIL>",
            phone="13800001003",
            sex="1",
            status="0",
            admin=False,
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        db_session.add(normal_user)

        # 创建管理员用户
        admin_user = User(
            user_id=1004,
            user_name="test_admin",
            nick_name="测试管理员",
            email="<EMAIL>",
            phone="13800001004",
            sex="1",
            status="0",
            admin=True,
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        db_session.add(admin_user)

        await db_session.commit()
        return {
            "creator": creator_user,
            "support": support_user,
            "normal": normal_user,
            "admin": admin_user
        }

    @pytest.fixture
    async def test_quotation(self, db_session: AsyncSession, test_users):
        """创建测试项目报价"""
        quotation = ProjectQuotation(
            id=2001,
            project_name="测试项目",
            project_code="TEST-2024-001",
            customer_name="测试客户",
            status="0",  # 草稿状态
            create_by="test_creator",
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        db_session.add(quotation)

        # 添加客服关联
        customer_support = ProjectQuotationCustomerSupport(
            project_quotation_id=2001,
            user_id=1002,  # 客服用户ID
            create_by="test_creator",
            create_time=datetime.now()
        )
        db_session.add(customer_support)

        await db_session.commit()
        return quotation

    def create_current_user(self, user: User) -> CurrentUserModel:
        """创建当前用户模型"""
        user_model = UserModel(
            user_id=user.user_id,
            user_name=user.user_name,
            nick_name=user.nick_name,
            email=user.email,
            phone=user.phone,
            sex=user.sex,
            status=user.status,
            admin=user.admin
        )
        return CurrentUserModel(user=user_model, roles=[], permissions=[])

    async def test_creator_can_edit_draft_quotation(self, db_session: AsyncSession, test_users, test_quotation):
        """测试创建人可以修改草稿状态的报价单"""
        service = ProjectQuotationService(db_session)
        current_user = self.create_current_user(test_users["creator"])

        edit_model = EditProjectQuotationModel(
            id=2001,
            customer_name="修改后的客户名称",
            status="0",
            items=[],
            attachments=[]
        )

        # 应该能够成功修改
        result = await service.edit_project_quotation(edit_model, current_user)
        assert result.is_success is True

    async def test_support_can_edit_draft_quotation(self, db_session: AsyncSession, test_users, test_quotation):
        """测试客服人员可以修改草稿状态的报价单"""
        service = ProjectQuotationService(db_session)
        current_user = self.create_current_user(test_users["support"])

        edit_model = EditProjectQuotationModel(
            id=2001,
            customer_name="客服修改后的客户名称",
            status="0",
            items=[],
            attachments=[]
        )

        # 应该能够成功修改
        result = await service.edit_project_quotation(edit_model, current_user)
        assert result.is_success is True

    async def test_admin_can_edit_draft_quotation(self, db_session: AsyncSession, test_users, test_quotation):
        """测试管理员可以修改草稿状态的报价单"""
        service = ProjectQuotationService(db_session)
        current_user = self.create_current_user(test_users["admin"])

        edit_model = EditProjectQuotationModel(
            id=2001,
            customer_name="管理员修改后的客户名称",
            status="0",
            items=[],
            attachments=[]
        )

        # 应该能够成功修改
        result = await service.edit_project_quotation(edit_model, current_user)
        assert result.is_success is True

    async def test_normal_user_cannot_edit_quotation(self, db_session: AsyncSession, test_users, test_quotation):
        """测试普通用户不能修改报价单"""
        service = ProjectQuotationService(db_session)
        current_user = self.create_current_user(test_users["normal"])

        edit_model = EditProjectQuotationModel(
            id=2001,
            customer_name="普通用户尝试修改",
            status="0",
            items=[],
            attachments=[]
        )

        # 应该抛出权限异常
        with pytest.raises(ServiceException) as exc_info:
            await service.edit_project_quotation(edit_model, current_user)
        assert "您没有权限修改此报价单" in str(exc_info.value)

    async def test_cannot_edit_pending_quotation(self, db_session: AsyncSession, test_users, test_quotation):
        """测试不能修改待审核状态的报价单"""
        # 修改报价单状态为待审核
        test_quotation.status = "1"
        await db_session.commit()

        service = ProjectQuotationService(db_session)
        current_user = self.create_current_user(test_users["creator"])

        edit_model = EditProjectQuotationModel(
            id=2001,
            customer_name="尝试修改待审核报价单",
            status="1",
            items=[],
            attachments=[]
        )

        # 应该抛出状态异常
        with pytest.raises(ServiceException) as exc_info:
            await service.edit_project_quotation(edit_model, current_user)
        assert "当前报价单状态为待审核，不允许修改" in str(exc_info.value)

    async def test_can_edit_rejected_quotation(self, db_session: AsyncSession, test_users, test_quotation):
        """测试可以修改已拒绝状态的报价单"""
        # 修改报价单状态为已拒绝
        test_quotation.status = "4"
        await db_session.commit()

        service = ProjectQuotationService(db_session)
        current_user = self.create_current_user(test_users["creator"])

        edit_model = EditProjectQuotationModel(
            id=2001,
            customer_name="修改已拒绝的报价单",
            status="4",
            items=[],
            attachments=[]
        )

        # 应该能够成功修改
        result = await service.edit_project_quotation(edit_model, current_user)
        assert result.is_success is True

        # 验证状态自动变为草稿
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == 2001)
        result = await db_session.execute(stmt)
        updated_quotation = result.scalar_one()
        assert updated_quotation.status == "0"  # 应该自动变为草稿状态

    async def test_update_fee_permission_control(self, db_session: AsyncSession, test_users, test_quotation):
        """测试更新费用的权限控制"""
        service = ProjectQuotationService(db_session)
        normal_user = self.create_current_user(test_users["normal"])

        fee_data = {
            "id": 2001,
            "otherFees": [],
            "totalFee": {"discountRate": 100, "taxRate": 0, "adjustmentAmount": 0}
        }

        # 普通用户应该无法更新费用
        with pytest.raises(ServiceException) as exc_info:
            await service.update_project_quotation_fee(fee_data, normal_user)
        assert "您没有权限修改此报价单" in str(exc_info.value)

    async def test_update_other_fee_permission_control(self, db_session: AsyncSession, test_users, test_quotation):
        """测试更新单行其他费用的权限控制"""
        service = ProjectQuotationService(db_session)
        normal_user = self.create_current_user(test_users["normal"])

        fee_data = {
            "id": 2001,
            "otherFeeId": 1,
            "otherFee": {"feeName": "测试费用", "quantity": 1, "unitPrice": 100, "totalPrice": 100}
        }

        # 普通用户应该无法更新其他费用
        with pytest.raises(ServiceException) as exc_info:
            await service.update_single_other_fee(fee_data, normal_user)
        assert "您没有权限修改此报价单" in str(exc_info.value)
