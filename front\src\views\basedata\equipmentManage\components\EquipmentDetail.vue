<template>
  <div>
    <!-- 一、基础信息类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">一、基础信息类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="设备编号">{{ equipmentData.equipmentNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="测量设备名称">{{ equipmentData.equipmentName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="启用日期">{{ equipmentData.enableDate || '-' }}</el-descriptions-item>
      <el-descriptions-item label="设备类型">{{ equipmentData.equipmentType || '-' }}</el-descriptions-item>
      <el-descriptions-item label="型号规格">{{ equipmentData.modelSpecification || '-' }}</el-descriptions-item>
      <el-descriptions-item label="出厂编号">{{ equipmentData.factoryNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="制造商" :span="2">{{ equipmentData.manufacturer || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 二、技术参数类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">二、技术参数类</span>
    </el-divider>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="指标特性">
        <div style="white-space: pre-wrap;">{{ equipmentData.indicatorCharacteristics || '-' }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 三、校准/溯源管理类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">三、校准/溯源管理类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="检定/校准机构">{{ equipmentData.calibrationInstitution || '-' }}</el-descriptions-item>
      <el-descriptions-item label="溯源方式">{{ equipmentData.traceabilityMethod || '-' }}</el-descriptions-item>
      <el-descriptions-item label="本次校准日期">{{ equipmentData.currentCalibrationDate || '-' }}</el-descriptions-item>
      <el-descriptions-item label="证书编号">{{ equipmentData.certificateNumber || '-' }}</el-descriptions-item>
      <el-descriptions-item label="下次校准日期">{{ equipmentData.nextCalibrationDate || '-' }}</el-descriptions-item>
      <el-descriptions-item label="间隔日期">{{ equipmentData.intervalDays ? equipmentData.intervalDays : '-' }}</el-descriptions-item>
      <el-descriptions-item label="期间核查日期" :span="2">{{ equipmentData.interimCheckDate || '-' }}</el-descriptions-item>
    </el-descriptions>
    
    <el-descriptions :column="1" border style="margin-top: 10px;">
      <el-descriptions-item label="备注(校准确认)">
        <div style="white-space: pre-wrap;">{{ equipmentData.calibrationRemark || '-' }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 四、管理责任类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">四、管理责任类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="管理者">{{ equipmentData.manager || '-' }}</el-descriptions-item>
      <el-descriptions-item label="设备状态">
        <el-tag
          :type="equipmentData.equipmentStatus === '正常' ? 'success' : 
                 equipmentData.equipmentStatus === '维修中' ? 'warning' : 
                 equipmentData.equipmentStatus === '停用' ? 'info' : 'danger'"
        >
          {{ equipmentData.equipmentStatus || '未设置' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="放置地点" :span="2">{{ equipmentData.location || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 五、财务与合同类 -->
    <el-divider content-position="left">
      <span style="font-weight: bold; color: #409EFF;">五、财务与合同类</span>
    </el-divider>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="金额">{{ equipmentData.amount ? '¥' + equipmentData.amount : '-' }}</el-descriptions-item>
      <el-descriptions-item label="合同">{{ equipmentData.contract || '-' }}</el-descriptions-item>
      <el-descriptions-item label="发票" :span="2">{{ equipmentData.invoice || '-' }}</el-descriptions-item>
    </el-descriptions>

    <!-- 操作按钮 -->
    <div style="text-align: center; margin-top: 20px;">
      <el-button @click="close">关 闭</el-button>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['close'])

const props = defineProps({
  equipmentData: {
    type: Object,
    default: () => ({})
  }
})

function close() {
  emit('close')
}
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}

.el-divider__text {
  font-size: 16px;
}

.el-descriptions {
  margin-bottom: 20px;
}
</style>
