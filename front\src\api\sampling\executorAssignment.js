import request from '@/utils/request'

// 创建或更新执行人指派
export function assignExecutor(data) {
  return request({
    url: '/sampling/executor-assignment/assign',
    method: 'post',
    data: data
  })
}

// 获取任务的所有执行人指派
export function getTaskExecutorAssignments(taskId) {
  return request({
    url: `/sampling/executor-assignment/task/${taskId}`,
    method: 'get'
  })
}

// 获取用户的所有执行任务
export function getUserExecutorAssignments(userId) {
  return request({
    url: `/sampling/executor-assignment/user/${userId}`,
    method: 'get'
  })
}

// 获取执行任务详情
export function getExecutorAssignmentDetail(assignmentId) {
  return request({
    url: `/sampling/executor-assignment/executions/${assignmentId}`,
    method: 'get'
  })
}

// 获取所有执行任务（仅超级管理员）
export function getAllExecutorAssignments() {
  return request({
    url: '/sampling/executor-assignment/all-executions',
    method: 'get'
  })
}

// 删除执行人指派
export function deleteExecutorAssignment(assignmentId) {
  return request({
    url: `/sampling/executor-assignment/${assignmentId}`,
    method: 'delete'
  })
}
