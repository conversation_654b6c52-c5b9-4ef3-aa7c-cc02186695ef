"""简单的 MissingGreenlet 错误测试"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import MissingGreenlet
from sqlalchemy import text


class TestSimpleGreenletFix:
    """简单的 MissingGreenlet 错误修复测试"""
    
    @pytest.mark.asyncio
    async def test_simple_query_no_greenlet_error(self, db_session: AsyncSession):
        """测试简单查询不会出现 MissingGreenlet 错误"""
        try:
            # 执行一个简单的查询
            result = await db_session.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            
            # 验证查询成功
            assert row is not None
            assert row[0] == 1
            
        except MissingGreenlet as e:
            pytest.fail(f"MissingGreenlet error occurred: {e}")
        except Exception as e:
            if "MissingGreenlet" in str(e):
                pytest.fail(f"MissingGreenlet error occurred: {e}")
            # 其他错误不影响测试
            pass