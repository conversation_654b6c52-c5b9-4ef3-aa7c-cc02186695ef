-- 创建技术手册价格表
CREATE TABLE IF NOT EXISTS technical_manual_price (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 唯一标识字段
    method VARCHAR(100) NOT NULL COMMENT '检测方法',
    category VARCHAR(50) NOT NULL COMMENT '检测类别',
    classification VARCHAR(50) COMMENT '分类',
    
    -- 检测价格相关字段
    first_item_price DECIMAL(10, 2) COMMENT '检测首项单价',
    additional_item_price DECIMAL(10, 2) COMMENT '检测增项单价',
    testing_fee_limit DECIMAL(10, 2) COMMENT '检测费上限',
    
    -- 采集价格相关字段
    sampling_price DECIMAL(10, 2) COMMENT '采集单价',
    pretreatment_price DECIMAL(10, 2) COMMENT '前处理单价',
    
    -- 其他价格相关字段
    special_consumables_price DECIMAL(10, 2) COMMENT '分析特殊耗材单价',
    
    -- 系统字段
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    
    -- 创建唯一索引
    UNIQUE KEY idx_technical_manual_price_unique (method, category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技术手册价格表';
