"""
创建Excel报价单模板文件
"""
import os
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

def create_quotation_template():
    """创建报价单模板"""
    
    wb = Workbook()
    ws = wb.active
    ws.title = "项目报价单"
    
    # 设置列宽
    column_widths = {
        'A': 6, 'B': 12, 'C': 15, 'D': 20, 'E': 12, 'F': 8, 'G': 8, 'H': 8, 'I': 8,
        'J': 10, 'K': 10, 'L': 10, 'M': 10, 'N': 10, 'O': 10, 'P': 15, 'Q': 15
    }
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # 定义样式
    title_font = Font(name='宋体', size=20, bold=True)
    subtitle_font = Font(name='宋体', size=16, bold=True)
    header_font = Font(name='宋体', size=12, bold=True)
    normal_font = Font(name='宋体', size=10)
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    right_alignment = Alignment(horizontal='right', vertical='center')
    
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    current_row = 1
    
    # 1. 公司标题
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    company_title = ws[f'A{current_row}']
    company_title.value = "北京东西分析股份有限公司"
    company_title.font = title_font
    company_title.alignment = center_alignment
    current_row += 2
    
    # 2. 报价单标题
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    quotation_title = ws[f'A{current_row}']
    quotation_title.value = "项目报价单"
    quotation_title.font = subtitle_font
    quotation_title.alignment = center_alignment
    current_row += 2
    
    # 3. 项目基本信息表格
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    info_header = ws[f'A{current_row}']
    info_header.value = "项目基本信息"
    info_header.font = header_font
    info_header.alignment = center_alignment
    info_header.border = thin_border
    current_row += 1
    
    # 基本信息模板行
    info_template_rows = [
        ["项目名称", "{{project_name}}", "项目编号", "{{project_code}}"],
        ["客户名称", "{{customer_name}}", "受检方", "{{inspected_party}}"],
        ["项目负责人", "{{project_manager}}", "市场负责人", "{{market_manager}}"],
        ["技术负责人", "{{technical_manager}}", "委托日期", "{{commission_date}}"],
        ["合同编号", "{{contract_code}}", "业务类别", "{{business_type}}"],
    ]
    
    for row_data in info_template_rows:
        _create_info_row(ws, current_row, row_data, header_font, normal_font, 
                        center_alignment, left_alignment, thin_border)
        current_row += 1
    
    current_row += 1
    
    # 4. 检测项目明细表
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    detail_title = ws[f'A{current_row}']
    detail_title.value = "检测项目明细"
    detail_title.font = header_font
    detail_title.alignment = center_alignment
    detail_title.border = thin_border
    current_row += 1
    
    # 明细表头
    detail_headers = [
        "序号", "样品类别", "检测参数", "检测方法", "点位名称", "点位数", "周期", "频次", "样品数",
        "采样单价", "采样费", "检测首项价", "检测增项价", "检测费上限", "检测费", "前处理费", "备注"
    ]
    
    for col_idx, header in enumerate(detail_headers, 1):
        cell = ws.cell(row=current_row, column=col_idx)
        cell.value = header
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = thin_border
    current_row += 1
    
    # 明细数据模板行（标记行，用于循环）
    cell = ws.cell(row=current_row, column=2)
    cell.value = "$样品类别"  # 标记需要循环的行
    cell.font = normal_font
    cell.alignment = left_alignment
    cell.border = thin_border
    
    # 为模板行的其他列设置样式
    for col_idx in range(1, 18):
        cell = ws.cell(row=current_row, column=col_idx)
        cell.font = normal_font
        cell.alignment = center_alignment if col_idx == 1 else left_alignment
        cell.border = thin_border
    
    current_row += 2
    
    # 5. 其他费用表
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    other_fee_title = ws[f'A{current_row}']
    other_fee_title.value = "其他费用明细"
    other_fee_title.font = header_font
    other_fee_title.alignment = center_alignment
    other_fee_title.border = thin_border
    current_row += 1
    
    # 其他费用表头
    other_fee_headers = ["序号", "费用名称", "费用金额", "备注"]
    for col_idx, header in enumerate(other_fee_headers, 1):
        cell = ws.cell(row=current_row, column=col_idx)
        cell.value = header
        cell.font = header_font
        cell.alignment = center_alignment
        cell.border = thin_border
    current_row += 1
    
    # 其他费用数据模板行（标记行，用于循环）
    cell = ws.cell(row=current_row, column=2)
    cell.value = "$差旅费"  # 标记需要循环的行
    cell.font = normal_font
    cell.alignment = left_alignment
    cell.border = thin_border
    
    # 为模板行的其他列设置样式
    for col_idx in range(1, 5):
        cell = ws.cell(row=current_row, column=col_idx)
        cell.font = normal_font
        cell.alignment = center_alignment if col_idx == 1 else left_alignment
        cell.border = thin_border
    
    current_row += 2
    
    # 6. 费用汇总表
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    summary_title = ws[f'A{current_row}']
    summary_title.value = "费用汇总"
    summary_title.font = header_font
    summary_title.alignment = center_alignment
    summary_title.border = thin_border
    current_row += 1
    
    # 费用汇总模板行
    summary_template_rows = [
        ["采样费用", "{{total_sampling_fee}}", "检测费用", "{{total_testing_fee}}"],
        ["前处理费用", "{{total_pretreatment_fee}}", "特殊耗材费", "{{special_consumables_fee}}"],
        ["其他费用", "{{other_fees_amount}}", "检测项目小计", "{{subtotal_amount}}"],
        ["折扣率", "{{discount_rate}}%", "折后金额", "{{discounted_amount}}"],
        ["税率", "{{tax_rate}}%", "税额", "{{tax_amount}}"],
        ["整体调整", "{{adjustment_amount}}", "最终总价", "{{final_amount}}"]
    ]
    
    for row_data in summary_template_rows:
        _create_info_row(ws, current_row, row_data, header_font, normal_font,
                        center_alignment, right_alignment, thin_border)
        current_row += 1
    
    current_row += 2
    
    # 7. 签字区域
    ws.merge_cells(f'A{current_row}:Q{current_row}')
    signature_title = ws[f'A{current_row}']
    signature_title.value = "审批签字"
    signature_title.font = header_font
    signature_title.alignment = center_alignment
    signature_title.border = thin_border
    current_row += 1
    
    # 签字表格
    signature_headers = ["制表人", "审核人", "批准人"]
    for col_idx, header in enumerate(signature_headers):
        start_col = 1 + col_idx * 5
        end_col = min(start_col + 4, 17)
        if start_col <= 17:
            ws.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
            cell = ws.cell(row=current_row, column=start_col)
            cell.value = f"{header}："
            cell.font = header_font
            cell.alignment = left_alignment
            cell.border = thin_border
    current_row += 1
    
    # 日期行
    for col_idx in range(3):
        start_col = 1 + col_idx * 5
        end_col = min(start_col + 4, 17)
        if start_col <= 17:
            ws.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
            cell = ws.cell(row=current_row, column=start_col)
            cell.value = "日期："
            cell.font = header_font
            cell.alignment = left_alignment
            cell.border = thin_border
    
    # 保存模板文件
    template_path = "templates/quotation_price_export_tpl.xlsx"
    os.makedirs(os.path.dirname(template_path), exist_ok=True)
    wb.save(template_path)
    
    print(f"✅ 模板文件创建成功: {template_path}")
    return template_path

def _create_info_row(ws, row, row_data, header_font, normal_font, 
                    center_alignment, value_alignment, thin_border):
    """创建信息行"""
    # 标签1
    cell_a = ws.cell(row=row, column=1)
    cell_a.value = row_data[0]
    cell_a.font = header_font
    cell_a.alignment = center_alignment
    cell_a.border = thin_border
    
    # 值1 (合并B-H列)
    ws.merge_cells(f'B{row}:H{row}')
    cell_b = ws.cell(row=row, column=2)
    cell_b.value = row_data[1]
    cell_b.font = normal_font
    cell_b.alignment = value_alignment
    cell_b.border = thin_border
    
    # 标签2
    cell_i = ws.cell(row=row, column=9)
    cell_i.value = row_data[2]
    cell_i.font = header_font
    cell_i.alignment = center_alignment
    cell_i.border = thin_border
    
    # 值2 (合并J-Q列)
    ws.merge_cells(f'J{row}:Q{row}')
    cell_j = ws.cell(row=row, column=10)
    cell_j.value = row_data[3]
    cell_j.font = normal_font
    cell_j.alignment = value_alignment
    cell_j.border = thin_border

if __name__ == "__main__":
    print("🚀 开始创建Excel模板文件...")
    template_path = create_quotation_template()
    print("🎉 模板文件创建完成！")
