"""
项目报价审批服务层测试
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService


class TestApprovalService:
    """
    审批服务测试类
    """

    @pytest.fixture
    async def setup_service_test_data(self, db: AsyncSession):
        """
        设置服务测试数据
        """
        # 创建测试角色
        market_role = SysRole(
            role_id=1,
            role_name="市场审批人员",
            role_key="market-approver",
            role_sort=10,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_role = SysRole(
            role_id=2,
            role_name="实验室审批人员", 
            role_key="lab-approver",
            role_sort=11,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_role = SysRole(
            role_id=3,
            role_name="现场审批人员",
            role_key="field-approver", 
            role_sort=12,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([market_role, lab_role, field_role])
        await db.flush()

        # 创建测试用户
        market_user = SysUser(
            user_id=1,
            user_name="market_user",
            nick_name="市场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_user = SysUser(
            user_id=2,
            user_name="lab_user",
            nick_name="实验室用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_user = SysUser(
            user_id=3,
            user_name="field_user",
            nick_name="现场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([market_user, lab_user, field_user])
        await db.flush()

        # 创建用户角色关联
        user_roles = [
            SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
            SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
            SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
        ]
        db.add_all(user_roles)

        # 创建测试项目报价
        quotation_sampling = ProjectQuotation(
            id=1,
            project_name="一般采样测试项目",
            project_code="SAMPLING001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        
        quotation_sample = ProjectQuotation(
            id=2,
            project_name="送样测试项目",
            project_code="SAMPLE001",
            business_type="sample",
            status="0",
            customer_name="测试客户",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        
        db.add_all([quotation_sampling, quotation_sample])
        await db.flush()

        await db.commit()

        return {
            "quotation_sampling": quotation_sampling,
            "quotation_sample": quotation_sample,
            "market_user": market_user,
            "lab_user": lab_user,
            "field_user": field_user,
            "market_role": market_role,
            "lab_role": lab_role,
            "field_role": field_role
        }

    @pytest.mark.asyncio
    async def test_init_approval_records_sampling(self, setup_service_test_data):
        """
        测试一般采样项目的审批记录初始化
        """
        test_data = await setup_service_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        db = test_data["quotation_sampling"].__dict__['_sa_instance_state'].session

        # 创建当前用户模型
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )

        # 初始化审批记录
        approval_service = ProjectQuotationApprovalService(db)
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 验证审批记录是否正确创建
        records = await approval_service.dao.get_approval_records_by_quotation_id(quotation.id)
        
        # 应该有3类审批记录：市场、实验室、现场
        assert len(records) == 3
        
        # 验证市场审批记录
        market_records = [r for r in records if r.approver_type == "market"]
        assert len(market_records) == 1
        assert market_records[0].approval_stage == 1
        assert market_records[0].is_required == "1"

        # 验证实验室审批记录
        lab_records = [r for r in records if r.approver_type == "lab"]
        assert len(lab_records) == 1
        assert lab_records[0].approval_stage == 2
        assert lab_records[0].is_required == "1"

        # 验证现场审批记录
        field_records = [r for r in records if r.approver_type == "field"]
        assert len(field_records) == 1
        assert field_records[0].approval_stage == 2
        assert field_records[0].is_required == "1"  # 一般采样需要现场审批

    @pytest.mark.asyncio
    async def test_init_approval_records_sample(self, setup_service_test_data):
        """
        测试送样项目的审批记录初始化
        """
        test_data = await setup_service_test_data
        quotation = test_data["quotation_sample"]
        market_user = test_data["market_user"]
        db = test_data["quotation_sample"].__dict__['_sa_instance_state'].session

        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )

        approval_service = ProjectQuotationApprovalService(db)
        await approval_service.init_approval_records(quotation.id, "sample", current_user)

        records = await approval_service.dao.get_approval_records_by_quotation_id(quotation.id)
        
        # 验证送样项目没有现场审批记录
        field_records = [r for r in records if r.approver_type == "field"]
        assert len(field_records) == 0  # 送样项目不应该有现场审批记录
        
        # 验证只有市场和实验室审批记录
        assert len(records) == 2
        approver_types = [r.approver_type for r in records]
        assert "market" in approver_types
        assert "lab" in approver_types

    @pytest.mark.asyncio
    async def test_approval_stage_control(self, setup_service_test_data):
        """
        测试审批阶段控制
        """
        test_data = await setup_service_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        lab_user = test_data["lab_user"]
        db = test_data["quotation_sampling"].__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 测试第二阶段审批在第一阶段未完成时不能进行
        lab_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=lab_user.user_id, user_name=lab_user.user_name)
        )
        
        # 尝试实验室审批（第二阶段），应该失败因为市场审批（第一阶段）未完成
        can_approve = await approval_service._can_approve_at_stage(quotation.id, 2)
        assert not can_approve  # 第二阶段不能审批

        # 市场审批通过后，第二阶段才能审批
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="市场审批通过"
        )
        await approval_service.perform_approval(quotation.id, market_action, current_user)

        # 现在第二阶段应该可以审批了
        can_approve = await approval_service._can_approve_at_stage(quotation.id, 2)
        assert can_approve  # 第二阶段可以审批

    @pytest.mark.asyncio
    async def test_get_pending_approvals(self, setup_service_test_data):
        """
        测试获取待审批项目列表
        """
        test_data = await setup_service_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        db = test_data["quotation_sampling"].__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 初始化审批记录并提交
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 获取市场用户的待审批列表
        pending_list = await approval_service.get_pending_approvals(current_user)
        
        # 市场用户应该有待审批的项目
        assert len(pending_list) > 0
        assert pending_list[0]["id"] == quotation.id

    @pytest.mark.asyncio
    async def test_approval_status_calculation(self, setup_service_test_data):
        """
        测试审批状态计算
        """
        test_data = await setup_service_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        db = test_data["quotation_sampling"].__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 获取初始状态
        status = await approval_service.get_approval_status(quotation.id)
        assert status.overall_status == "0"  # 草稿

        # 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)
        status = await approval_service.get_approval_status(quotation.id)
        assert status.overall_status == "1"  # 待审核

        # 市场审批拒绝
        rejection_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="rejected",
            approval_opinion="市场审批拒绝"
        )
        await approval_service.perform_approval(quotation.id, rejection_action, current_user)
        
        status = await approval_service.get_approval_status(quotation.id)
        assert status.overall_status == "4"  # 已拒绝
