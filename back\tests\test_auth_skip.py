#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境跳过认证功能的单元测试

测试内容：
1. 测试跳过认证功能的开启/关闭
2. 测试不同token格式的处理
3. 测试用户模型创建的完整性
4. 测试安全性验证
"""

import pytest
from unittest.mock import patch, MagicMock
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from config.env import AppConfig


class TestAuthSkip:
    """测试认证跳过功能"""

    def setup_method(self):
        """测试前置设置"""
        self.login_service = LoginService()

    @pytest.mark.asyncio
    async def test_skip_auth_enabled_with_test_token(self):
        """测试启用跳过认证且使用test_token时的行为"""
        with patch.object(AppConfig, 'app_skip_auth_in_test', True):
            # 测试 test_token 格式
            result = await self.login_service.get_current_user(token="test_token")
            
            assert isinstance(result, CurrentUserModel)
            assert result.user.user_name == "test_user"
            assert result.user.nick_name == "测试用户"
            assert result.permissions == ["*:*:*"]
            assert "test_role" in result.roles

    @pytest.mark.asyncio
    async def test_skip_auth_enabled_with_bearer_test_token(self):
        """测试启用跳过认证且使用Bearer test_token时的行为"""
        with patch.object(AppConfig, 'app_skip_auth_in_test', True):
            # 测试 Bearer test_token 格式
            result = await self.login_service.get_current_user(token="Bearer test_token")
            
            assert isinstance(result, CurrentUserModel)
            assert result.user.user_name == "test_user"
            assert result.user.user_id == 999
            assert result.user.dept.dept_name == "测试部门"
            assert len(result.user.role) > 0
            assert result.user.role[0].role_name == "测试角色"

    @pytest.mark.asyncio
    async def test_skip_auth_disabled(self):
        """测试禁用跳过认证时的行为"""
        with patch.object(AppConfig, 'app_skip_auth_in_test', False):
            # 应该继续正常的JWT验证流程，这里会抛出异常
            with pytest.raises(Exception):
                await self.login_service.get_current_user(token="test_token")

    @pytest.mark.asyncio
    async def test_skip_auth_with_invalid_token(self):
        """测试启用跳过认证但使用无效token时的行为"""
        with patch.object(AppConfig, 'app_skip_auth_in_test', True):
            # 使用非test_token，应该继续正常验证流程
            with pytest.raises(Exception):
                await self.login_service.get_current_user(token="invalid_token")

    @pytest.mark.asyncio
    async def test_test_user_model_completeness(self):
        """测试测试用户模型的完整性"""
        with patch.object(AppConfig, 'app_skip_auth_in_test', True):
            result = await self.login_service.get_current_user(token="test_token")
            
            # 验证用户基本信息
            assert result.user.user_id == 999
            assert result.user.user_name == "test_user"
            assert result.user.nick_name == "测试用户"
            assert result.user.email == "<EMAIL>"
            assert result.user.phonenumber == "13800138000"
            assert result.user.sex == "0"
            assert result.user.dept_id == 1
            assert result.user.status == "0"
            assert result.user.admin is True
            
            # 验证部门信息
            assert result.user.dept is not None
            assert result.user.dept.dept_id == 1
            assert result.user.dept.dept_name == "测试部门"
            assert result.user.dept.leader == 999
            
            # 验证角色信息
            assert result.user.role is not None
            assert len(result.user.role) > 0
            assert result.user.role[0].role_id == 999
            assert result.user.role[0].role_name == "测试角色"
            assert result.user.role[0].role_key == "test_role"
            
            # 验证权限信息
            assert result.permissions == ["*:*:*"]
            assert result.roles == ["test_role"]

    def test_security_config_validation(self):
        """测试安全配置验证"""
        # 确保默认配置是安全的
        assert hasattr(AppConfig, 'app_skip_auth_in_test')
        # 在实际部署中，这个值应该默认为False
        # 这里只是验证配置项存在

    @pytest.mark.asyncio
    async def test_logging_behavior(self):
        """测试日志记录行为"""
        with patch.object(AppConfig, 'app_skip_auth_in_test', True):
            with patch('utils.log_util.logger.info') as mock_logger:
                await self.login_service.get_current_user(token="test_token")
                
                # 验证是否记录了跳过认证的日志
                mock_logger.assert_called_with("测试环境跳过认证，使用测试用户: test_user")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])