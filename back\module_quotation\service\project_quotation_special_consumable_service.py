"""
项目报价特殊耗材费用明细服务
"""
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_quotation.entity.do.project_quotation_special_consumable_do import ProjectQuotationSpecialConsumable
from module_quotation.entity.vo.project_quotation_special_consumable_vo import (
    ProjectQuotationSpecialConsumableModel,
    AddProjectQuotationSpecialConsumableModel,
    EditProjectQuotationSpecialConsumableModel,
    ProjectQuotationSpecialConsumableResponseModel
)
from module_technical_manual.entity.do.technical_manual_do import TechnicalManual
from utils.common_util import CamelCaseUtil


class ProjectQuotationSpecialConsumableService(BaseService[ProjectQuotationSpecialConsumable]):
    """项目报价特殊耗材费用明细服务"""

    def __init__(self, db: AsyncSession):
        super().__init__(ProjectQuotationSpecialConsumable, db)
        self.db = db

    async def get_special_consumables_by_quotation_id(self, quotation_id: int) -> List[ProjectQuotationSpecialConsumableResponseModel]:
        """根据项目报价ID获取特殊耗材费用明细列表"""
        try:
            query = select(ProjectQuotationSpecialConsumable).where(
                ProjectQuotationSpecialConsumable.project_quotation_id == quotation_id
            )
            result = await self.db.execute(query)
            special_consumables = result.scalars().all()
            
            response_list = []
            for item in special_consumables:
                response_list.append(ProjectQuotationSpecialConsumableResponseModel(
                    id=item.id,
                    projectQuotationId=item.project_quotation_id,
                    parameter=item.parameter,
                    method=item.method,
                    unitPrice=str(item.unit_price),
                    quantity=item.quantity,
                    totalPrice=str(item.total_price),
                    remark=item.remark
                ))
            
            return response_list
            
        except Exception as e:
            raise ServiceException(f"获取特殊耗材费用明细失败: {str(e)}")

    async def sync_special_consumables_from_technical_manual(self, quotation_id: int, items: List[dict]) -> None:
        """从技术手册同步特殊耗材单价"""
        try:
            # 删除现有的特殊耗材记录
            delete_query = delete(ProjectQuotationSpecialConsumable).where(
                ProjectQuotationSpecialConsumable.project_quotation_id == quotation_id
            )
            await self.db.execute(delete_query)
            
            # 获取所有参数-方法组合的特殊耗材单价
            for item in items:
                parameter = item.get('parameter')
                method = item.get('method')
                
                if not parameter or not method:
                    continue
                
                # 查询技术手册中的特殊耗材单价
                query = select(TechnicalManual).where(
                    TechnicalManual.parameter == parameter,
                    TechnicalManual.method == method
                )
                result = await self.db.execute(query)
                technical_manual = result.scalar_one_or_none()
                
                if technical_manual and technical_manual.special_consumables_price and technical_manual.special_consumables_price > 0:
                    # 创建特殊耗材记录
                    special_consumable = ProjectQuotationSpecialConsumable(
                        project_quotation_id=quotation_id,
                        parameter=parameter,
                        method=method,
                        unit_price=technical_manual.special_consumables_price,
                        quantity=1,
                        total_price=technical_manual.special_consumables_price,
                        create_time=datetime.now(),
                        update_time=datetime.now()
                    )
                    self.db.add(special_consumable)
            
            await self.db.flush()
            
        except Exception as e:
            raise ServiceException(f"同步特殊耗材单价失败: {str(e)}")

    async def add_special_consumable(self, quotation_id: int, add_model: AddProjectQuotationSpecialConsumableModel, current_user: CurrentUserModel) -> ProjectQuotationSpecialConsumableResponseModel:
        """添加特殊耗材费用明细"""
        try:
            # 查询技术手册获取单价
            query = select(TechnicalManual).where(
                TechnicalManual.parameter == add_model.parameter,
                TechnicalManual.method == add_model.method
            )
            result = await self.db.execute(query)
            technical_manual = result.scalar_one_or_none()
            
            if not technical_manual:
                raise ServiceException("未找到对应的技术手册记录")
            
            unit_price = technical_manual.special_consumables_price or Decimal('0.00')
            total_price = unit_price * add_model.quantity
            
            special_consumable = ProjectQuotationSpecialConsumable(
                project_quotation_id=quotation_id,
                parameter=add_model.parameter,
                method=add_model.method,
                unit_price=unit_price,
                quantity=add_model.quantity,
                total_price=total_price,
                remark=add_model.remark,
                create_by=current_user.user.user_name,
                create_time=datetime.now(),
                update_by=current_user.user.user_name,
                update_time=datetime.now()
            )
            
            self.db.add(special_consumable)
            await self.db.flush()
            
            return ProjectQuotationSpecialConsumableResponseModel(
                id=special_consumable.id,
                projectQuotationId=special_consumable.project_quotation_id,
                parameter=special_consumable.parameter,
                method=special_consumable.method,
                unitPrice=str(special_consumable.unit_price),
                quantity=special_consumable.quantity,
                totalPrice=str(special_consumable.total_price),
                remark=special_consumable.remark
            )
            
        except Exception as e:
            raise ServiceException(f"添加特殊耗材费用明细失败: {str(e)}")

    async def edit_special_consumable(self, edit_model: EditProjectQuotationSpecialConsumableModel, current_user: CurrentUserModel) -> ProjectQuotationSpecialConsumableResponseModel:
        """编辑特殊耗材费用明细"""
        try:
            query = select(ProjectQuotationSpecialConsumable).where(
                ProjectQuotationSpecialConsumable.id == edit_model.id
            )
            result = await self.db.execute(query)
            special_consumable = result.scalar_one_or_none()
            
            if not special_consumable:
                raise ServiceException("特殊耗材费用明细不存在")
            
            # 更新数量和备注
            special_consumable.quantity = edit_model.quantity
            special_consumable.remark = edit_model.remark
            special_consumable.total_price = special_consumable.unit_price * edit_model.quantity
            special_consumable.update_by = current_user.user.user_name
            special_consumable.update_time = datetime.now()
            
            await self.db.flush()
            
            return ProjectQuotationSpecialConsumableResponseModel(
                id=special_consumable.id,
                projectQuotationId=special_consumable.project_quotation_id,
                parameter=special_consumable.parameter,
                method=special_consumable.method,
                unitPrice=str(special_consumable.unit_price),
                quantity=special_consumable.quantity,
                totalPrice=str(special_consumable.total_price),
                remark=special_consumable.remark
            )
            
        except Exception as e:
            raise ServiceException(f"编辑特殊耗材费用明细失败: {str(e)}")

    async def delete_special_consumable(self, special_consumable_id: int) -> None:
        """删除特殊耗材费用明细"""
        try:
            query = select(ProjectQuotationSpecialConsumable).where(
                ProjectQuotationSpecialConsumable.id == special_consumable_id
            )
            result = await self.db.execute(query)
            special_consumable = result.scalar_one_or_none()
            
            if not special_consumable:
                raise ServiceException("特殊耗材费用明细不存在")
            
            await self.db.delete(special_consumable)
            await self.db.flush()
            
        except Exception as e:
            raise ServiceException(f"删除特殊耗材费用明细失败: {str(e)}")

    async def calculate_special_consumables_total_fee(self, quotation_id: int) -> Decimal:
        """计算特殊耗材总费用"""
        try:
            query = select(ProjectQuotationSpecialConsumable).where(
                ProjectQuotationSpecialConsumable.project_quotation_id == quotation_id
            )
            result = await self.db.execute(query)
            special_consumables = result.scalars().all()
            
            total_fee = Decimal('0.00')
            for item in special_consumables:
                total_fee += item.total_price
            
            return total_fee
            
        except Exception as e:
            raise ServiceException(f"计算特殊耗材总费用失败: {str(e)}")
