<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="任务编号" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 200px">
          <el-option
            v-for="dictItem in (dict.type && dict.type.sampling_task_status) || []"
            :key="dictItem.value"
            :label="dictItem.label"
            :value="dictItem.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分配用户" prop="assignedUserId">
        <el-select v-model="queryParams.assignedUserId" placeholder="请选择分配用户" clearable style="width: 200px">
          <el-option
            v-for="user in userList || []"
            :key="user.userId"
            :label="user.nickName"
            :value="user.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['sampling:task:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sampling:task:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sampling:task:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['sampling:task:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务编号" align="center" prop="taskCode" width="180">
          <template #default="scope">
            <span v-if="scope?.row?.isUrgent" class="urgent-prefix">【加急】</span>{{ scope?.row?.taskCode }}
          </template>
        </el-table-column>
        <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
        <el-table-column label="项目报价" align="center" prop="projectName" :show-overflow-tooltip="true" />
        <el-table-column label="任务状态" align="center" prop="statusLabel" width="100">
          <template #default="scope">
            <span>{{ scope?.row?.statusLabel }}</span>
          </template>
        </el-table-column>

        <el-table-column label="分配用户" align="center" prop="assignedUserName" width="100" />
        <el-table-column label="计划开始时间" align="center" prop="plannedStartDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope?.row?.plannedStartDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划结束时间" align="center" prop="plannedEndDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope?.row?.plannedEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope?.row?.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
          <template #default="scope">
            <el-button
              type="text"
              icon="Edit"
              @click="handleUpdate(scope?.row)"
              v-hasPermi="['sampling:task:edit']"
            >修改</el-button>
            <el-button
              type="text"
              icon="View"
              @click="handleDetail(scope?.row)"
              v-hasPermi="['sampling:task:query']"
            >详情</el-button>
            <el-button
              type="text"
              :icon="scope?.row?.isUrgent ? 'RemoveFilled' : 'Flag'"
              @click="handleUrgent(scope?.row)"
              v-if="hasUrgentPermission"
              :style="{ color: scope?.row?.isUrgent ? '#f56c6c' : '#409eff' }"
            >{{ scope?.row?.isUrgent ? '取消加急' : '加急' }}</el-button>
            <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope?.row)"
              v-hasPermi="['sampling:task:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采样任务对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目报价" prop="projectQuotationId">
              <el-select v-model="form.projectQuotationId" placeholder="请选择项目报价" @change="handleProjectQuotationChange">
                <el-option
                  v-for="quotation in quotationList || []"
                  :key="quotation.id"
                  :label="quotation.projectName"
                  :value="quotation.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分配用户" prop="assignedUserId">
              <el-select v-model="form.assignedUserId" placeholder="请选择分配用户">
                <el-option
                  v-for="user in userList || []"
                  :key="user.userId"
                  :label="user.nickName"
                  :value="user.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择任务状态">
                <el-option
                  v-for="dictItem in (dict.type && dict.type.sampling_task_status) || []"
                  :key="dictItem.value"
                  :label="dictItem.label"
                  :value="dictItem.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计划开始时间" prop="plannedStartDate">
              <el-date-picker
                v-model="form.plannedStartDate"
                type="date"
                placeholder="选择计划开始时间"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划结束时间" prop="plannedEndDate">
              <el-date-picker
                v-model="form.plannedEndDate"
                type="date"
                placeholder="选择计划结束时间"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入任务描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        
        <!-- 检测周期条目选择 -->
        <el-form-item label="检测周期条目" v-if="form.projectQuotationId">
          <el-transfer
            v-model="selectedCycleItems"
            :data="availableCycleItems"
            :titles="['可选条目', '已选条目']"
            :button-texts="['移除', '添加']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            @change="handleCycleItemChange"
          >
            <template #default="{ option }">
              <span>{{ option.label }}</span>
            </template>
          </el-transfer>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" v-model="detailOpen" width="1400px" append-to-body class="detail-dialog">
      <div class="detail-content">
        <el-descriptions :column="2" border class="detail-descriptions">
          <el-descriptions-item label="任务编号">
              <span v-if="taskDetail.isUrgent" class="urgent-prefix">【加急】</span>{{ taskDetail.taskCode }}
            </el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.taskName }}</el-descriptions-item>
          <el-descriptions-item label="项目报价">{{ taskDetail.projectName }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <span>{{ taskDetail.statusLabel }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="分配用户">{{ taskDetail.assignedUserName }}</el-descriptions-item>
          <el-descriptions-item label="创建用户">{{ taskDetail.createUserName }}</el-descriptions-item>
          <el-descriptions-item label="计划开始时间">{{ parseTime(taskDetail.plannedStartDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="计划结束时间">{{ parseTime(taskDetail.plannedEndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="实际开始时间">{{ parseTime(taskDetail.actualStartDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="实际结束时间">{{ parseTime(taskDetail.actualEndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ taskDetail.description || '暂无描述' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ taskDetail.remark || '暂无备注' }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 关联的检测周期条目 -->
        <div class="cycle-items-section">
          <h4 class="section-title">关联的检测周期条目</h4>
          <el-table :data="taskDetail.cycleItems" border class="cycle-items-table">
            <el-table-column label="周期序号" prop="cycleNumber" align="center" width="100" />
            <el-table-column label="周期类型" prop="cycleType" align="center" width="100" :show-overflow-tooltip="true" />
            <el-table-column label="检测资质" prop="detectionQualification" align="center" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="检测分类" prop="detectionClassification" align="center" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="检测类别" prop="detectionCategory" align="center" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="检测参数" prop="detectionParameter" align="center" width="150" :show-overflow-tooltip="true" />
            <el-table-column label="检测方法" prop="detectionMethod" align="center" width="150" :show-overflow-tooltip="true" />
            <el-table-column label="样品来源" prop="sampleSource" align="center" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="点位名称" prop="pointName" align="center" width="120" :show-overflow-tooltip="true" />
            <el-table-column label="状态" prop="statusLabel" align="center" width="100">
              <template #default="scope">
                <span>{{ scope.row?.statusLabel }}</span>
              </template>
            </el-table-column>
            <el-table-column label="计划检测日期" prop="plannedDetectionDate" align="center" width="120">
              <template #default="scope">
                <span>{{ parseTime(scope.row?.plannedDetectionDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" align="center" width="150">
              <template #default="scope">
                <span>{{ parseTime(scope.row?.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSamplingTask, getSamplingTask, delSamplingTask, addSamplingTask, updateSamplingTask, setSamplingTaskUrgent } from "@/api/sampling/samplingTask";
import { getCycleItemsByProjectQuotationId } from "@/api/sampling/detectionCycleItem";
import { listUser } from "@/api/system/user";
import { listProjectQuotation } from "@/api/quotation/projectQuotation";
import { getDicts } from "@/api/system/dict/data";
import useUserStore from '@/store/modules/user';

export default {
  name: "SamplingTask",
  dicts: ['sampling_task_status', 'detection_cycle_status'],
  computed: {
    // 检查用户是否有加急权限（task-urgent权限或admin角色）
    hasUrgentPermission() {
      const userStore = useUserStore();
      console.log('用户权限:', userStore.permissions);
      console.log('用户角色:', userStore.roles);
      const hasTaskUrgentPermission = userStore.permissions.some(permission => 
        permission === '*:*:*' || permission === 'task-urgent'
      );
      const isAdmin = userStore.roles.includes('admin');
      console.log('是否有task-urgent权限:', hasTaskUrgentPermission);
      console.log('是否为admin角色:', isAdmin);
      const result = hasTaskUrgentPermission || isAdmin;
      console.log('最终权限结果:', result);
      return result;
    }
  },
  data() {
    return {
      // 字典数据
      dict: {
        type: {
          sampling_task_status: [],
          detection_cycle_status: []
        }
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采样任务表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskCode: null,
        taskName: null,
        status: null,
        assignedUserId: null
      },
      // 表单参数
      form: {},
      // 任务详情
      taskDetail: {},
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        projectQuotationId: [
          { required: true, message: "项目报价不能为空", trigger: "change" }
        ],
        assignedUserId: [
          { required: true, message: "分配用户不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "任务状态不能为空", trigger: "change" }
        ]
      },
      // 用户列表
      userList: [],
      // 项目报价列表
      quotationList: [],
      // 可选的检测周期条目
      availableCycleItems: [],
      // 已选择的检测周期条目
      selectedCycleItems: []
    };
  },
  created() {
    this.getDictData();
    this.getList();
    this.getUserList();
    this.getQuotationList();
  },
  methods: {
    /** 获取字典数据 */
    getDictData() {
      // 获取采样任务状态字典
      getDicts('sampling_task_status').then(response => {
        this.dict.type.sampling_task_status = response.data.map(p => ({
          label: p.dictLabel,
          value: p.dictValue,
          elTagType: p.listClass,
          elTagClass: p.cssClass
        }));
      }).catch(error => {
        console.error('获取采样任务状态字典失败:', error);
        this.dict.type.sampling_task_status = [];
      });
      
      // 获取检测周期状态字典
      getDicts('detection_cycle_status').then(response => {
        this.dict.type.detection_cycle_status = response.data.map(p => ({
          label: p.dictLabel,
          value: p.dictValue,
          elTagType: p.listClass,
          elTagClass: p.cssClass
        }));
      }).catch(error => {
        console.error('获取检测周期状态字典失败:', error);
        this.dict.type.detection_cycle_status = [];
      });
    },
    /** 查询采样任务列表 */
    getList() {
      this.loading = true;
      listSamplingTask(this.queryParams).then(response => {
        // 处理接口返回的数据结构：response.data.records
        const records = response.data?.records || [];
        this.taskList = records.filter(item => item != null);
        this.total = response.data?.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('获取采样任务列表失败:', error);
        this.taskList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 查询用户列表 */
    getUserList() {
      listUser({ pageSize: 100000 }).then(response => {
        this.userList = response.rows || [];
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.userList = [];
      });
    },
    /** 查询项目报价列表 */
    getQuotationList() {
      listProjectQuotation({ status: 'approved' }).then(response => {
        this.quotationList = response.rows || [];
      }).catch(error => {
        console.error('获取项目报价列表失败:', error);
        this.quotationList = [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskName: null,
        projectQuotationId: null,
        assignedUserId: null,
        status: "pending",
        plannedStartDate: null,
        plannedEndDate: null,
        description: null,
        remark: null
      };
      this.selectedCycleItems = [];
      this.availableCycleItems = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采样任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const taskId = row.id || this.ids
      getSamplingTask(taskId).then(response => {
        this.form = response.data;
        this.selectedCycleItems = response.data.cycleItems ? response.data.cycleItems.map(item => item.id) : [];
        if (this.form.projectQuotationId) {
          this.handleProjectQuotationChange(this.form.projectQuotationId);
        }
        this.open = true;
        this.title = "修改采样任务";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const taskId = row.id;
      getSamplingTask(taskId).then(response => {
        this.taskDetail = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 添加选中的检测周期条目ID
          this.form.cycleItemIds = this.selectedCycleItems;
          
          if (this.form.id != null) {
            updateSamplingTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSamplingTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const taskIds = row.id || this.ids;
      this.$modal.confirm('是否确认删除采样任务编号为"' + taskIds + '"的数据项？').then(function() {
        return delSamplingTask(taskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sampling/task/export', {
        ...this.queryParams
      }, `task_${new Date().getTime()}.xlsx`)
    },
    /** 项目报价变更处理 */
    handleProjectQuotationChange(projectQuotationId) {
      if (projectQuotationId) {
        getCycleItemsByProjectQuotationId(projectQuotationId).then(response => {
          this.availableCycleItems = response.data.map(item => ({
            key: item.id,
            label: `周期${item.cycleNumber} - ${item.status}`,
            disabled: item.status === 'assigned'
          }));
        });
      } else {
        this.availableCycleItems = [];
        this.selectedCycleItems = [];
      }
    },
    /** 检测周期条目变更处理 */
    handleCycleItemChange(value, direction, movedKeys) {
      console.log(value, direction, movedKeys);
    },
    /** 加急按钮操作 */
    handleUrgent(row) {
      const isUrgent = !row.isUrgent;
      const action = isUrgent ? '设置' : '取消';
      this.$modal.confirm(`是否确认${action}任务"${row.taskName}"的加急状态？`).then(() => {
        setSamplingTaskUrgent(row.id, isUrgent).then(() => {
          this.$modal.msgSuccess(`${action}加急成功`);
          this.getList();
        }).catch(error => {
          console.error('设置加急状态失败:', error);
          this.$modal.msgError(`${action}加急失败`);
        });
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: 15px;
}

.box-card {
  margin-top: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-button--link {
  color: #409eff;
  font-weight: 500;
}

.el-button--link:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.mb8 {
  margin-bottom: 8px;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-input, .el-select {
  width: 100%;
}

/* 搜索表单样式 */
.el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

/* 操作按钮区域 */
.el-row.mb8 {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

/* 表格操作列样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

.fixed-width .el-button {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 对话框样式优化 */
.el-dialog__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog__title {
  font-weight: 600;
  color: #303133;
}

/* 详情页面样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 600;
  color: #606266;
}

/* Transfer 组件样式优化 */
.el-transfer {
  text-align: left;
}

.el-transfer-panel {
  border-radius: 6px;
}

.el-transfer-panel__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 600;
}

/* 状态标签样式 */
.dict-tag {
  font-weight: 500;
}

/* 搜索表单样式 */
.search-form {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

/* 详情对话框样式 */
.detail-dialog .el-dialog__body {
  padding: 20px;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-descriptions {
  margin-bottom: 24px;
}

.cycle-items-section {
  margin-top: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  color: #303133;
  font-weight: 600;
  font-size: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.cycle-items-table {
  border-radius: 6px;
  overflow: hidden;
}

/* 卡片头部样式增强 */
.box-card .el-card__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #e9ecef;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f0f9ff !important;
}

/* 按钮组样式优化 */
.el-row.mb8 .el-button {
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-row.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 状态标签样式增强 */
.dict-tag {
  font-weight: 500;
  border-radius: 12px;
  padding: 4px 8px;
}

/* 加急前缀样式 */
.urgent-prefix {
  color: #d32f2f;
  font-weight: bold;
  margin-right: 4px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 12px;
  }
  
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .el-form-item .el-input,
  .el-form-item .el-select {
    width: 100% !important;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .card-subtitle {
    display: none;
  }
  
  .el-table-column {
    min-width: 80px;
  }
}
</style>