"""
测试配置文件
"""
import asyncio
import os
import pytest
import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from typing import AsyncGenerator, Generator

from config.database import Base
from config.get_db import get_db
from server import app as main_app
from utils.log_util import logger

@pytest_asyncio.fixture(scope="module")
async def test_engine():
    # Create test database URL
    TEST_DATABASE_URL = os.environ.get(
        "TEST_DATABASE_URL", "sqlite+aiosqlite:///:memory:"
    )
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield engine
    await engine.dispose()

@pytest_asyncio.fixture(scope="function")
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    async with test_engine.connect() as connection:
        async with connection.begin() as transaction:
            async_session = sessionmaker(
                test_engine, class_=AsyncSession, expire_on_commit=False
            )
            async with async_session(bind=connection) as session:
                yield session
            await transaction.rollback()


@pytest.fixture
def app(db_session: AsyncSession) -> FastAPI:
    """
    创建测试应用

    :param db: 测试数据库会话
    :return: 测试应用
    """
    # 覆盖依赖项
    async def override_get_db():
        yield db

    main_app.dependency_overrides[get_db] = override_get_db
    return main_app


@pytest.fixture
def client(app: FastAPI) -> Generator[TestClient, None, None]:
    """
    创建测试客户端

    :param app: 测试应用
    :return: 测试客户端
    """
    with TestClient(app) as client:
        yield client
