"""
项目报价总费用数据模型
"""
from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationTotalFee(Base):
    """
    项目报价总费用表
    """
    __tablename__ = 'project_quotation_total_fee'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment='项目报价ID')
    item_testing_fees = Column(DECIMAL(10, 2), nullable=False, default=0, comment='明细检测项目汇总费用')
    discount_rate = Column(DECIMAL(5, 2), nullable=False, default=100, comment='检测项目总折扣率')
    discounted_testing_fee = Column(DECIMAL(10, 2), nullable=False, default=0, comment='检测折后费用')
    other_fee = Column(DECIMAL(10, 2), nullable=False, default=0, comment='其他费用')
    total_fee_before_discount = Column(DECIMAL(10, 2), nullable=False, default=0, comment='优惠前总费用=检测折后费用+其他费用')
    tax_rate = Column(DECIMAL(5, 2), nullable=False, default=0, comment='税率')
    tax = Column(DECIMAL(10, 2), nullable=False, default=0, comment='税费=优惠前总费用*税率')
    total_fee_after_tax = Column(DECIMAL(10, 2), nullable=False, default=0, comment='优惠前总费用(税后)=优惠前总费用+税费')
    adjustment_amount = Column(DECIMAL(10, 2), nullable=False, default=0, comment='整体调整金额')
    final_amount = Column(DECIMAL(10, 2), nullable=False, default=0, comment='优惠后总金额=优惠前总费用(税后)-整体调整金额')
    
    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment='创建人')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(50), nullable=True, comment='更新人')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
    
    # 关联项目报价
    project_quotation = relationship("ProjectQuotation", back_populates="total_fee")
