#!/usr/bin/env python3
"""
清理重复的assignment-execution:all权限记录
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import AsyncSessionLocal
from sqlalchemy import text

async def clean_permissions():
    """清理重复的权限记录"""
    
    async with AsyncSessionLocal() as session:
        try:
            # 1. 查询所有重复的权限记录
            result = await session.execute(text("""
                SELECT menu_id, menu_name, create_time
                FROM sys_menu 
                WHERE perms = 'assignment-execution:all'
                ORDER BY create_time ASC
            """))
            
            rows = result.fetchall()
            print(f"找到 {len(rows)} 个重复的权限记录：")
            for i, row in enumerate(rows):
                print(f"  {i+1}. ID: {row[0]}, 名称: {row[1]}, 创建时间: {row[2]}")
            
            if len(rows) > 1:
                # 保留第一个（最早创建的），删除其他的
                keep_id = rows[0][0]
                delete_ids = [row[0] for row in rows[1:]]
                
                print(f"\n保留权限ID: {keep_id}")
                print(f"删除权限ID: {delete_ids}")
                
                # 删除重复的角色权限关联
                for delete_id in delete_ids:
                    await session.execute(text("""
                        DELETE FROM sys_role_menu WHERE menu_id = :menu_id
                    """), {"menu_id": delete_id})
                
                # 删除重复的权限记录
                for delete_id in delete_ids:
                    await session.execute(text("""
                        DELETE FROM sys_menu WHERE menu_id = :menu_id
                    """), {"menu_id": delete_id})
                
                await session.commit()
                print("✅ 重复权限清理完成！")
            else:
                print("✅ 没有重复的权限记录")
                
            # 确保超级管理员有这个权限
            await session.execute(text("""
                INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) 
                SELECT 1, menu_id 
                FROM sys_menu 
                WHERE perms = 'assignment-execution:all'
            """))
            
            await session.commit()
            
            # 最终查询结果
            result = await session.execute(text("""
                SELECT 
                    m.menu_id,
                    m.menu_name,
                    m.perms,
                    m.create_time
                FROM sys_menu m
                WHERE m.perms = 'assignment-execution:all'
            """))
            
            final_rows = result.fetchall()
            print(f"\n📋 最终权限信息：")
            for row in final_rows:
                print(f"  - 权限ID: {row[0]}")
                print(f"  - 权限名称: {row[1]}")
                print(f"  - 权限标识: {row[2]}")
                print(f"  - 创建时间: {row[3]}")
                
        except Exception as e:
            await session.rollback()
            print(f"❌ 清理权限失败: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(clean_permissions())
