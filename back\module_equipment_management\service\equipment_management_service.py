"""
设备管理服务层
"""

import io
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from typing import List, Optional, Dict, Any

import pandas as pd
from sqlalchemy import select, and_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions.exception import ServiceException
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_equipment_management.entity.do.equipment_management_do import EquipmentManagement
from module_equipment_management.entity.vo.equipment_management_vo import (
    AddEquipmentManagementModel,
    EditEquipmentManagementModel,
    EquipmentManagementQueryModel,
    EquipmentManagementImportErrorModel,
    EquipmentManagementImportResultModel,
    AttachmentModel,
)
from utils.common_util import CamelCaseUtil
from utils.log_util import logger
from utils.page_util import PageUtil


class EquipmentManagementService:
    """设备管理服务"""

    def __init__(self, db: AsyncSession):
        self.db = db

    # async def __aenter__(self):
    #     self.db = AsyncSessionLocal()
    #     return self
    #
    # async def __aexit__(self, exc_type, exc_val, exc_tb):
    #     if self.db:
    #         await self.db.close()

    async def get_equipment_page(self, query: EquipmentManagementQueryModel) -> Dict[str, Any]:
        """获取设备列表"""
        try:
            # 构建查询条件
            conditions = []
            if query.equipment_number:
                conditions.append(EquipmentManagement.equipment_number == query.equipment_number)
            if query.equipment_name:
                conditions.append(EquipmentManagement.equipment_name.like(f"%{query.equipment_name}%"))
            if query.equipment_type:
                conditions.append(EquipmentManagement.equipment_type.like(f"%{query.equipment_type}%"))
            if query.equipment_status:
                conditions.append(EquipmentManagement.equipment_status == query.equipment_status)
            if query.manager:
                conditions.append(EquipmentManagement.manager.like(f"%{query.manager}%"))
            if query.manufacturer:
                conditions.append(EquipmentManagement.manufacturer.like(f"%{query.manufacturer}%"))
            # 执行查询
            stmt = (
                select(EquipmentManagement)
                .where(and_(*conditions))
                .order_by(
                    EquipmentManagement.id.desc(),
                )
            )
            page_result = await PageUtil.paginate(self.db, stmt, query.page_num, query.page_size, True)
            # 将下划线命名转换为驼峰命名
            return CamelCaseUtil.transform_result(page_result)
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"获取设备列表失败：{str(e)}")

    async def get_equipment_by_id(self, equipment_id: int) -> Dict[str, Any]:
        """根据ID获取设备信息"""
        try:
            query = select(EquipmentManagement).where(EquipmentManagement.id == equipment_id)
            result = await self.db.execute(query)
            equipment = result.scalar_one_or_none()

            if not equipment:
                raise ServiceException(message="设备不存在")

            equipment_dict = {
                "id": equipment.id,
                "equipment_number": equipment.equipment_number,
                "equipment_name": equipment.equipment_name,
                "enable_date": equipment.enable_date,
                "equipment_type": equipment.equipment_type,
                "model_specification": equipment.model_specification,
                "factory_number": equipment.factory_number,
                "manufacturer": equipment.manufacturer,
                "indicator_characteristics": equipment.indicator_characteristics,
                "calibration_institution": equipment.calibration_institution,
                "traceability_method": equipment.traceability_method,
                "current_calibration_date": equipment.current_calibration_date,
                "certificate_number": equipment.certificate_number,
                "next_calibration_date": equipment.next_calibration_date,
                "interval_days": equipment.interval_days,
                "interim_check_date": equipment.interim_check_date,
                "calibration_remark": equipment.calibration_remark,
                "calibration_content": equipment.calibration_content,
                "manager": equipment.manager,
                "equipment_status": equipment.equipment_status,
                "location": equipment.location,
                "amount": equipment.amount,
                "contract": equipment.contract,
                "invoice": equipment.invoice,
                "attachments": equipment.attachments,
            }

            return CamelCaseUtil.transform_result(equipment_dict)

        except ServiceException:
            raise
        except Exception as e:
            raise ServiceException(message=f"获取设备信息失败：{str(e)}")

    async def add_equipment(
        self, equipment_data: AddEquipmentManagementModel, current_user: CurrentUserModel
    ) -> Dict[str, Any]:
        """添加设备"""
        try:
            # 检查设备编号是否已存在
            existing_query = select(EquipmentManagement).where(
                EquipmentManagement.equipment_number == equipment_data.equipment_number
            )
            existing_result = await self.db.execute(existing_query)
            existing_equipment = existing_result.scalar_one_or_none()

            if existing_equipment:
                raise ServiceException(message=f"设备编号 {equipment_data.equipment_number} 已存在，不允许重复录入")

            # 处理附件信息
            attachments_json = None
            if equipment_data.attachments:
                attachments_json = [attachment.dict() for attachment in equipment_data.attachments]

            # 创建设备对象
            equipment = EquipmentManagement(
                equipment_number=equipment_data.equipment_number,
                equipment_name=equipment_data.equipment_name,
                enable_date=equipment_data.enable_date,
                equipment_type=equipment_data.equipment_type,
                model_specification=equipment_data.model_specification,
                factory_number=equipment_data.factory_number,
                manufacturer=equipment_data.manufacturer,
                indicator_characteristics=equipment_data.indicator_characteristics,
                calibration_institution=equipment_data.calibration_institution,
                traceability_method=equipment_data.traceability_method,
                current_calibration_date=equipment_data.current_calibration_date,
                certificate_number=equipment_data.certificate_number,
                next_calibration_date=equipment_data.next_calibration_date,
                interval_days=equipment_data.interval_days,
                interim_check_date=equipment_data.interim_check_date,
                calibration_remark=equipment_data.calibration_remark,
                calibration_content=equipment_data.calibration_content,
                attachments=attachments_json,
                manager=equipment_data.manager,
                equipment_status=equipment_data.equipment_status,
                location=equipment_data.location,
                amount=equipment_data.amount,
                contract=equipment_data.contract,
                invoice=equipment_data.invoice,
                create_by=current_user.user.user_name,
                create_time=datetime.now(),
            )

            self.db.add(equipment)
            await self.db.commit()
            await self.db.refresh(equipment)

            return {"id": equipment.id, "message": "设备添加成功"}

        except ServiceException:
            await self.db.rollback()
            raise
        except IntegrityError:
            await self.db.rollback()
            raise ServiceException(message=f"设备编号 {equipment_data.equipment_number} 已存在，不允许重复录入")
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"添加设备失败：{str(e)}")

    async def update_equipment(
        self, equipment_data: EditEquipmentManagementModel, current_user: CurrentUserModel
    ) -> Dict[str, Any]:
        """更新设备"""
        try:
            # 获取现有设备
            query = select(EquipmentManagement).where(EquipmentManagement.id == equipment_data.id)
            result = await self.db.execute(query)
            equipment = result.scalar_one_or_none()

            if not equipment:
                raise ServiceException(message="设备不存在")

            # 检查设备编号是否与其他设备重复
            if equipment.equipment_number != equipment_data.equipment_number:
                existing_query = select(EquipmentManagement).where(
                    and_(
                        EquipmentManagement.equipment_number == equipment_data.equipment_number,
                        EquipmentManagement.id != equipment_data.id,
                    )
                )
                existing_result = await self.db.execute(existing_query)
                existing_equipment = existing_result.scalar_one_or_none()

                if existing_equipment:
                    raise ServiceException(message=f"设备编号 {equipment_data.equipment_number} 已存在，不允许重复录入")

            # 处理附件信息
            attachments_json = None
            if equipment_data.attachments:
                attachments_json = [attachment.dict() for attachment in equipment_data.attachments]

            # 更新设备信息
            equipment.equipment_number = equipment_data.equipment_number
            equipment.equipment_name = equipment_data.equipment_name
            equipment.enable_date = equipment_data.enable_date
            equipment.equipment_type = equipment_data.equipment_type
            equipment.model_specification = equipment_data.model_specification
            equipment.factory_number = equipment_data.factory_number
            equipment.manufacturer = equipment_data.manufacturer
            equipment.indicator_characteristics = equipment_data.indicator_characteristics
            equipment.calibration_institution = equipment_data.calibration_institution
            equipment.traceability_method = equipment_data.traceability_method
            equipment.current_calibration_date = equipment_data.current_calibration_date
            equipment.certificate_number = equipment_data.certificate_number
            equipment.next_calibration_date = equipment_data.next_calibration_date
            equipment.interval_days = equipment_data.interval_days
            equipment.interim_check_date = equipment_data.interim_check_date
            equipment.calibration_remark = equipment_data.calibration_remark
            equipment.calibration_content = equipment_data.calibration_content
            equipment.attachments = attachments_json
            equipment.manager = equipment_data.manager
            equipment.equipment_status = equipment_data.equipment_status
            equipment.location = equipment_data.location
            equipment.amount = equipment_data.amount
            equipment.contract = equipment_data.contract
            equipment.invoice = equipment_data.invoice
            equipment.update_by = current_user.user.user_name
            equipment.update_time = datetime.now()

            await self.db.commit()

            return {"message": "设备更新成功"}

        except ServiceException:
            await self.db.rollback()
            raise
        except IntegrityError:
            await self.db.rollback()
            raise ServiceException(message=f"设备编号 {equipment_data.equipment_number} 已存在，不允许重复录入")
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"更新设备失败：{str(e)}")

    async def delete_equipment(self, equipment_id: int, current_user: CurrentUserModel) -> Dict[str, Any]:
        """删除设备（物理删除）"""
        try:
            # 获取设备
            query = select(EquipmentManagement).where(EquipmentManagement.id == equipment_id)
            result = await self.db.execute(query)
            equipment = result.scalar_one_or_none()

            if not equipment:
                raise ServiceException(message="设备不存在")

            # 物理删除
            await self.db.delete(equipment)
            await self.db.commit()

            return {"message": "设备删除成功"}

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"删除设备失败：{str(e)}")

    async def batch_delete_equipment(self, equipment_ids: List[int], current_user: CurrentUserModel) -> Dict[str, Any]:
        """批量删除设备（物理删除）"""
        try:
            if not equipment_ids:
                raise ServiceException(message="请选择要删除的设备")

            # 查询要删除的设备
            query = select(EquipmentManagement).where(EquipmentManagement.id.in_(equipment_ids))
            result = await self.db.execute(query)
            equipments = result.scalars().all()

            if len(equipments) != len(equipment_ids):
                raise ServiceException(message="部分设备不存在")

            # 批量物理删除
            for equipment in equipments:
                await self.db.delete(equipment)

            await self.db.commit()

            return {"message": f"成功删除 {len(equipments)} 个设备"}

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"批量删除设备失败：{str(e)}")

    async def import_equipment_from_excel(
        self, file_content: bytes, current_user: CurrentUserModel
    ) -> EquipmentManagementImportResultModel:
        """从Excel导入设备数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(io.BytesIO(file_content))

            # 定义列名映射（按照要求的字段顺序）
            expected_columns = [
                "设备编号",
                "测量设备（器具）名称",
                "启用日期",
                "设备类型",
                "型号规格",
                "出厂编号",
                "制造商",
                "指标特性",
                "检定/校准机构",
                "溯源方式",
                "本次校准/核查日期",
                "证书编号",
                "下次校准/核查日期",
                "间隔日期",
                "期间核查日期",
                "管理者",
                "设备状态",
                "放置地点",
                "备注（校准确认）",
                "校准内容",
                "金额",
                "合同",
                "发票",
            ]

            # 检查列名
            if len(df.columns) != len(expected_columns):
                raise ServiceException(
                    message=f"Excel文件列数不正确，期望{len(expected_columns)}列，实际{len(df.columns)}列"
                )

            # 重命名列
            df.columns = expected_columns

            success_count = 0
            error_count = 0
            errors = []
            success_data = []

            # 获取现有设备编号，用于重复检查
            existing_numbers_query = select(EquipmentManagement.equipment_number)
            existing_result = await self.db.execute(existing_numbers_query)
            existing_numbers = set(row[0] for row in existing_result.fetchall())

            # 处理每一行数据
            for index, row in df.iterrows():
                row_number = index + 2  # Excel行号从2开始（第1行是标题）
                row_errors = []

                try:
                    # 验证必填字段
                    equipment_number = str(row["设备编号"]).strip() if pd.notna(row["设备编号"]) else ""
                    equipment_name = (
                        str(row["测量设备（器具）名称"]).strip() if pd.notna(row["测量设备（器具）名称"]) else ""
                    )

                    if not equipment_number:
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number, field="设备编号", message="设备编号不能为空"
                            )
                        )

                    if not equipment_name:
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number, field="测量设备（器具）名称", message="设备名称不能为空"
                            )
                        )

                    # 检查设备编号是否重复
                    if equipment_number and equipment_number in existing_numbers:
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number,
                                field="设备编号",
                                message=f"设备编号 {equipment_number} 已存在，不允许重复导入",
                            )
                        )

                    # 如果有错误，跳过这一行
                    if row_errors:
                        errors.extend(row_errors)
                        error_count += 1
                        continue

                    # 解析日期字段，并记录解析错误
                    enable_date = self._parse_date(row["启用日期"])
                    if pd.notna(row["启用日期"]) and row["启用日期"] != "" and enable_date is None:
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number,
                                field="启用日期",
                                message=f"日期格式错误: {row['启用日期']}，支持格式: YYYY-MM-DD, YYYY/MM/DD, YYYY.MM.DD, YYYY.MM, YYYY/MM, YYYY-MM",
                            )
                        )

                    current_calibration_date = self._parse_date(row["本次校准/核查日期"])
                    if (
                        pd.notna(row["本次校准/核查日期"])
                        and row["本次校准/核查日期"] != ""
                        and current_calibration_date is None
                    ):
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number,
                                field="本次校准/核查日期",
                                message=f"日期格式错误: {row['本次校准/核查日期']}，支持格式: YYYY-MM-DD, YYYY/MM/DD, YYYY.MM.DD, YYYY.MM, YYYY/MM, YYYY-MM",
                            )
                        )

                    next_calibration_date = self._parse_date(row["下次校准/核查日期"])
                    if (
                        pd.notna(row["下次校准/核查日期"])
                        and row["下次校准/核查日期"] != ""
                        and next_calibration_date is None
                    ):
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number,
                                field="下次校准/核查日期",
                                message=f"日期格式错误: {row['下次校准/核查日期']}，支持格式: YYYY-MM-DD, YYYY/MM/DD, YYYY.MM.DD, YYYY.MM, YYYY/MM, YYYY-MM",
                            )
                        )

                    interim_check_date = self._parse_date(row["期间核查日期"])
                    if pd.notna(row["期间核查日期"]) and row["期间核查日期"] != "" and interim_check_date is None:
                        row_errors.append(
                            EquipmentManagementImportErrorModel(
                                row=row_number,
                                field="期间核查日期",
                                message=f"日期格式错误: {row['期间核查日期']}，支持格式: YYYY-MM-DD, YYYY/MM/DD, YYYY.MM.DD, YYYY.MM, YYYY/MM, YYYY-MM",
                            )
                        )

                    # 解析数值字段
                    amount = self._parse_decimal(row["金额"])

                    # 创建设备对象
                    equipment = EquipmentManagement(
                        equipment_number=equipment_number,
                        equipment_name=equipment_name,
                        enable_date=enable_date,
                        equipment_type=str(row["设备类型"]).strip() if pd.notna(row["设备类型"]) else None,
                        model_specification=str(row["型号规格"]).strip() if pd.notna(row["型号规格"]) else None,
                        factory_number=str(row["出厂编号"]).strip() if pd.notna(row["出厂编号"]) else None,
                        manufacturer=str(row["制造商"]).strip() if pd.notna(row["制造商"]) else None,
                        indicator_characteristics=str(row["指标特性"]).strip() if pd.notna(row["指标特性"]) else None,
                        calibration_institution=(
                            str(row["检定/校准机构"]).strip() if pd.notna(row["检定/校准机构"]) else None
                        ),
                        traceability_method=str(row["溯源方式"]).strip() if pd.notna(row["溯源方式"]) else None,
                        current_calibration_date=current_calibration_date,
                        certificate_number=str(row["证书编号"]).strip() if pd.notna(row["证书编号"]) else None,
                        next_calibration_date=next_calibration_date,
                        interval_days=str(row["间隔日期"]).strip() if pd.notna(row["间隔日期"]) else None,
                        interim_check_date=interim_check_date,
                        calibration_remark=(
                            str(row["备注（校准确认）"]).strip() if pd.notna(row["备注（校准确认）"]) else None
                        ),
                        calibration_content=(str(row["校准内容"]).strip() if pd.notna(row["校准内容"]) else None),
                        manager=str(row["管理者"]).strip() if pd.notna(row["管理者"]) else None,
                        equipment_status=str(row["设备状态"]).strip() if pd.notna(row["设备状态"]) else None,
                        location=str(row["放置地点"]).strip() if pd.notna(row["放置地点"]) else None,
                        amount=amount,
                        contract=str(row["合同"]).strip() if pd.notna(row["合同"]) else None,
                        invoice=str(row["发票"]).strip() if pd.notna(row["发票"]) else None,
                        create_by=current_user.user.user_name,
                        create_time=datetime.now(),
                    )

                    self.db.add(equipment)
                    existing_numbers.add(equipment_number)  # 添加到已存在集合中，防止同批次重复
                    success_count += 1

                    # 构建成功数据
                    equipment_dict = {
                        "id": 0,  # 导入时还没有ID
                        "equipment_number": equipment.equipment_number,
                        "equipment_name": equipment.equipment_name,
                        "enable_date": equipment.enable_date,
                        "equipment_type": equipment.equipment_type,
                        "model_specification": equipment.model_specification,
                        "factory_number": equipment.factory_number,
                        "manufacturer": equipment.manufacturer,
                        "indicator_characteristics": equipment.indicator_characteristics,
                        "calibration_institution": equipment.calibration_institution,
                        "traceability_method": equipment.traceability_method,
                        "current_calibration_date": equipment.current_calibration_date,
                        "certificate_number": equipment.certificate_number,
                        "next_calibration_date": equipment.next_calibration_date,
                        "interval_days": equipment.interval_days,
                        "interim_check_date": equipment.interim_check_date,
                        "calibration_remark": equipment.calibration_remark,
                        "calibration_content": equipment.calibration_content,
                        "manager": equipment.manager,
                        "equipment_status": equipment.equipment_status,
                        "location": equipment.location,
                        "amount": equipment.amount,
                        "contract": equipment.contract,
                        "invoice": equipment.invoice,
                    }
                    success_data.append(CamelCaseUtil.transform_result(equipment_dict))

                except Exception as e:
                    row_errors.append(
                        EquipmentManagementImportErrorModel(
                            row=row_number, field="数据处理", message=f"处理数据时出错：{str(e)}"
                        )
                    )
                    errors.extend(row_errors)
                    error_count += 1

            # 提交事务
            if success_count > 0:
                await self.db.commit()

            return EquipmentManagementImportResultModel(
                success_count=success_count, error_count=error_count, errors=errors, data=success_data
            )

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"导入设备数据失败：{str(e)}")

    def _parse_date(self, value) -> Optional[date]:
        """解析日期"""
        if pd.isna(value) or value == "":
            return None

        try:
            if isinstance(value, str):
                value = value.strip()

                # 处理特殊格式：2016.04 -> 2016-04-01
                if len(value) == 7 and "." in value and value.count(".") == 1:
                    parts = value.split(".")
                    if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                        year = int(parts[0])
                        month = int(parts[1])
                        if 1900 <= year <= 2100 and 1 <= month <= 12:
                            return date(year, month, 1)

                # 处理特殊格式：2016/04 -> 2016-04-01
                if len(value) == 7 and "/" in value and value.count("/") == 1:
                    parts = value.split("/")
                    if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                        year = int(parts[0])
                        month = int(parts[1])
                        if 1900 <= year <= 2100 and 1 <= month <= 12:
                            return date(year, month, 1)

                # 处理特殊格式：2016-04 -> 2016-04-01
                if len(value) == 7 and "-" in value and value.count("-") == 1:
                    parts = value.split("-")
                    if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                        year = int(parts[0])
                        month = int(parts[1])
                        if 1900 <= year <= 2100 and 1 <= month <= 12:
                            return date(year, month, 1)

                # 尝试多种完整日期格式
                date_formats = [
                    "%Y-%m-%d",
                    "%Y/%m/%d",
                    "%Y.%m.%d",
                    "%Y-%m-%d %H:%M:%S",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y.%m.%d %H:%M:%S",
                    "%Y年%m月%d日",
                    "%Y年%m月",
                    "%Y-%m",
                    "%Y/%m",
                    "%Y.%m",
                ]

                for fmt in date_formats:
                    try:
                        parsed_date = datetime.strptime(value, fmt)
                        return parsed_date.date()
                    except ValueError:
                        continue

                # 如果都无法解析，尝试pandas的日期解析
                try:
                    parsed_date = pd.to_datetime(value)
                    if not pd.isna(parsed_date):
                        return parsed_date.date()
                except:
                    pass

                logger.info(f"警告: 无法解析日期格式: {value}")
                return None

            elif hasattr(value, "date"):
                return value.date()
            elif isinstance(value, date):
                return value
            else:
                # 尝试转换为字符串再解析
                return self._parse_date(str(value))

        except Exception as e:
            logger.info(f"日期解析错误: {value} -> {str(e)}")
            return None

    def _parse_int(self, value) -> Optional[int]:
        """解析整数"""
        if pd.isna(value) or value == "":
            return None

        try:
            return int(float(value))
        except (ValueError, TypeError):
            return None

    def _parse_decimal(self, value) -> Optional[Decimal]:
        """解析金额"""
        if pd.isna(value) or value == "":
            return None

        try:
            return Decimal(str(value))
        except (ValueError, TypeError, InvalidOperation):
            return None

    async def update_attachment(
        self, equipment_number: int, add_attachment: AttachmentModel, is_del: bool
    ) -> List[AttachmentModel]:
        """删除或更新设备附件"""
        # 检查设备编号是否已存在
        existing_query = select(EquipmentManagement).where(EquipmentManagement.equipment_number == equipment_number)
        existing_result = await self.db.execute(existing_query)
        existing_equipment = existing_result.scalar_one_or_none()

        if not existing_equipment:
            return []

        # 处理附件信息
        attachments_json = []
        if existing_equipment.attachments:
            attachments_json = [attachment.dict() for attachment in existing_equipment.attachments]
        for attachment in existing_equipment.attachments or []:
            if is_del and attachment.file_name == add_attachment.file_name:
                continue
            attachments_json.append(attachment.dict())
        if not is_del:
            attachments_json.append(add_attachment.dict())

        # 更新附件
        existing_equipment.attachment = attachments_json
        try:
            await self.db.commit()
            logger.info(f"设备编号： {equipment_number} 的附件已更新")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新设备 {equipment_number} 的附件时发生错误: {str(e)}")
        return attachments_json
