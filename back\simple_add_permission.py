#!/usr/bin/env python3
"""
简单添加assignment-execution:all权限的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import AsyncSessionLocal
from sqlalchemy import text

async def add_permission():
    """添加assignment-execution:all权限"""
    
    async with AsyncSessionLocal() as session:
        try:
            # 1. 添加权限菜单项
            await session.execute(text("""
                INSERT IGNORE INTO `sys_menu` (
                    `menu_name`, `parent_id`, `order_num`, `path`, `component`, 
                    `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, 
                    `status`, `perms`, `icon`, `create_by`, `create_time`, 
                    `update_by`, `update_time`, `remark`
                ) VALUES (
                    '查看所有执行任务', 0, 999, '', '', 
                    NULL, 1, 0, 'F', '0', 
                    '0', 'assignment-execution:all', '#', 'admin', NOW(), 
                    '', NULL, '查看所有执行任务权限'
                )
            """))
            
            # 2. 为超级管理员角色分配权限
            await session.execute(text("""
                INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) 
                SELECT 1, menu_id 
                FROM sys_menu 
                WHERE perms = 'assignment-execution:all'
            """))
            
            await session.commit()
            print("✅ 权限添加成功！")
            
            # 3. 查询添加的权限
            result = await session.execute(text("""
                SELECT 
                    m.menu_id,
                    m.menu_name,
                    m.perms,
                    m.create_time
                FROM sys_menu m
                WHERE m.perms = 'assignment-execution:all'
            """))
            
            rows = result.fetchall()
            if rows:
                print("\n📋 添加的权限信息：")
                for row in rows:
                    print(f"  - 权限ID: {row[0]}")
                    print(f"  - 权限名称: {row[1]}")
                    print(f"  - 权限标识: {row[2]}")
                    print(f"  - 创建时间: {row[3]}")
            else:
                print("⚠️  未找到添加的权限")
                
        except Exception as e:
            await session.rollback()
            print(f"❌ 添加权限失败: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(add_permission())
