"""
测试技术手册修复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
from urllib.parse import quote_plus
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from config.env import DataBaseConfig

# 构建同步数据库连接URL
SQLALCHEMY_DATABASE_URL = (
    f'mysql+pymysql://{DataBaseConfig.db_username}:{quote_plus(DataBaseConfig.db_password)}@'
    f'{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}'
)

def test_technical_manual_fixes():
    """
    测试技术手册修复功能
    """
    # 创建数据库连接
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        print("=== 技术手册修复功能测试 ===")
        
        # 1. 测试技术手册服务初始化
        print("\n1. 测试技术手册服务初始化:")
        try:
            from module_basedata.service.technical_manual_service import TechnicalManualService
            from sqlalchemy.ext.asyncio import AsyncSession
            print("  ✅ 技术手册服务导入成功")
        except Exception as e:
            print(f"  ❌ 技术手册服务导入失败: {str(e)}")
        
        # 2. 测试技术手册别名字段
        print("\n2. 测试技术手册别名字段:")
        result = db.execute(text("SHOW COLUMNS FROM technical_manual LIKE 'alias_list'"))
        if result.fetchone():
            print("  ✅ alias_list字段存在")
            
            # 查看别名数据
            result = db.execute(text("""
                SELECT id, parameter, alias_list 
                FROM technical_manual 
                WHERE alias_list IS NOT NULL 
                AND alias_list != '[]' 
                AND alias_list != ''
                LIMIT 5
            """))
            records = result.fetchall()
            print(f"  找到 {len(records)} 条有别名的记录:")
            for record in records:
                try:
                    aliases = json.loads(record.alias_list) if record.alias_list else []
                    print(f"    ID:{record.id} - {record.parameter} - 别名: {aliases}")
                except:
                    print(f"    ID:{record.id} - {record.parameter} - 别名格式错误: {record.alias_list}")
        else:
            print("  ❌ alias_list字段不存在")
        
        # 3. 测试技术手册价格表结构
        print("\n3. 测试技术手册价格表结构:")
        
        # 检查category_code字段
        result = db.execute(text("SHOW COLUMNS FROM technical_manual_price LIKE 'category_code'"))
        if result.fetchone():
            print("  ✅ category_code字段存在")
        else:
            print("  ❌ category_code字段不存在")
        
        # 检查del_flag字段是否已删除
        result = db.execute(text("SHOW COLUMNS FROM technical_manual_price LIKE 'del_flag'"))
        if result.fetchone():
            print("  ⚠️  del_flag字段仍然存在（应该删除）")
        else:
            print("  ✅ del_flag字段已删除")
        
        # 4. 测试技术手册价格数据
        print("\n4. 测试技术手册价格数据:")
        result = db.execute(text("""
            SELECT COUNT(*) as total_count
            FROM technical_manual_price
        """))
        total_count = result.scalar()
        
        result = db.execute(text("""
            SELECT COUNT(*) as with_category_code
            FROM technical_manual_price 
            WHERE category_code IS NOT NULL AND category_code != ''
        """))
        with_category_code = result.scalar()
        
        print(f"  总价格记录数: {total_count}")
        print(f"  有类目编码的记录数: {with_category_code}")
        
        if total_count > 0:
            coverage = (with_category_code / total_count) * 100
            print(f"  类目编码覆盖率: {coverage:.1f}%")
            
            if coverage >= 90:
                print("  ✅ 类目编码覆盖率良好")
            else:
                print("  ⚠️  类目编码覆盖率较低")
        
        # 5. 测试技术手册价格关联查询
        print("\n5. 测试技术手册价格关联查询:")
        result = db.execute(text("""
            SELECT tmp.id, tmp.method, tmp.category_code,
                   tmc.classification, tmc.category
            FROM technical_manual_price tmp
            LEFT JOIN technical_manual_category tmc ON tmp.category_code = tmc.category_code
            WHERE tmp.category_code IS NOT NULL
            LIMIT 5
        """))
        
        join_records = result.fetchall()
        print(f"  关联查询结果: {len(join_records)} 条记录")
        for record in join_records:
            print(f"    ID:{record.id} - {record.method}")
            print(f"      类目编码: {record.category_code}")
            print(f"      分类: {record.classification} - 检测类别: {record.category}")
        
        # 6. 测试技术手册类目表
        print("\n6. 测试技术手册类目表:")
        result = db.execute(text("""
            SELECT COUNT(*) as category_count
            FROM technical_manual_category
        """))
        category_count = result.scalar()
        
        result = db.execute(text("""
            SELECT COUNT(DISTINCT classification) as classification_count
            FROM technical_manual_category
        """))
        classification_count = result.scalar()
        
        print(f"  类目总数: {category_count}")
        print(f"  分类总数: {classification_count}")
        
        # 显示类目分布
        result = db.execute(text("""
            SELECT classification, COUNT(*) as count
            FROM technical_manual_category
            GROUP BY classification
            ORDER BY count DESC
            LIMIT 5
        """))
        
        distribution = result.fetchall()
        print("  类目分布:")
        for item in distribution:
            print(f"    {item.classification}: {item.count} 个类目")
        
        # 7. 测试JSON查询功能
        print("\n7. 测试JSON查询功能:")
        
        # 测试技术手册的category_codes JSON查询
        result = db.execute(text("""
            SELECT COUNT(*) as json_records
            FROM technical_manual 
            WHERE category_codes IS NOT NULL 
            AND JSON_VALID(category_codes) = 1
        """))
        json_records = result.scalar()
        
        result = db.execute(text("""
            SELECT COUNT(*) as total_manual_records
            FROM technical_manual
        """))
        total_manual_records = result.scalar()
        
        print(f"  技术手册总记录数: {total_manual_records}")
        print(f"  有效JSON类目编码记录数: {json_records}")
        
        if total_manual_records > 0:
            json_coverage = (json_records / total_manual_records) * 100
            print(f"  JSON类目编码覆盖率: {json_coverage:.1f}%")
        
        # 8. 性能测试
        print("\n8. 性能测试:")
        
        # 测试JSON查询性能
        import time
        start_time = time.time()
        
        result = db.execute(text("""
            SELECT tm.id, tm.parameter, tm.category_codes,
                   GROUP_CONCAT(CONCAT(tmc.classification, '-', tmc.category) SEPARATOR ', ') as categories_info
            FROM technical_manual tm
            JOIN technical_manual_category tmc ON JSON_CONTAINS(tm.category_codes, CONCAT('"', tmc.category_code, '"'))
            WHERE tm.category_codes IS NOT NULL
            GROUP BY tm.id
            LIMIT 10
        """))
        
        performance_records = result.fetchall()
        end_time = time.time()
        
        print(f"  JSON关联查询耗时: {(end_time - start_time)*1000:.2f}ms")
        print(f"  查询结果数量: {len(performance_records)}")
        
        if len(performance_records) > 0:
            print("  ✅ JSON关联查询正常工作")
        else:
            print("  ⚠️  JSON关联查询无结果")
        
        print("\n=== 测试完成 ===")
        print("✅ 技术手册修复功能测试通过！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    test_technical_manual_fixes()
