"""
简化的审批流程测试
"""

import pytest
import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# 尝试导入必要的模块
try:
    from config.database import Base
    from module_admin.entity.do.user_do import SysUser, SysUserRole
    from module_admin.entity.do.role_do import SysRole
    from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
    from module_quotation.entity.do.project_quotation_do import ProjectQuotation
    from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
    from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
    from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
    
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"导入失败: {e}")
    IMPORTS_AVAILABLE = False


# 测试数据库配置
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_approval.db"


@pytest.fixture
async def db_session():
    """创建测试数据库会话"""
    if not IMPORTS_AVAILABLE:
        pytest.skip("必要的模块导入失败")
    
    # 创建测试数据库引擎
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 创建会话
    async with TestSessionLocal() as session:
        yield session
    
    # 清理表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


class TestSimpleApproval:
    """简化的审批测试"""

    @pytest.mark.asyncio
    async def test_imports(self):
        """测试模块导入"""
        assert IMPORTS_AVAILABLE, "必要的模块无法导入"

    @pytest.mark.asyncio
    async def test_create_basic_entities(self, db_session):
        """测试创建基本实体"""
        if not IMPORTS_AVAILABLE:
            pytest.skip("必要的模块导入失败")
        
        # 创建角色
        role = SysRole(
            role_id=1,
            role_name="测试角色",
            role_key="test-role",
            role_sort=1,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        db_session.add(role)
        await db_session.flush()
        
        # 创建用户
        user = SysUser(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            password="test_password",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        db_session.add(user)
        await db_session.flush()
        
        # 创建项目报价
        quotation = ProjectQuotation(
            id=1,
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=user.user_id,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        await db_session.commit()
        
        # 验证创建成功
        assert role.role_id == 1
        assert user.user_id == 1
        assert quotation.id == 1

    @pytest.mark.asyncio
    async def test_approval_record_creation(self, db_session):
        """测试审批记录创建"""
        if not IMPORTS_AVAILABLE:
            pytest.skip("必要的模块导入失败")
        
        # 创建用户
        user = SysUser(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            password="test_password",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        db_session.add(user)
        await db_session.flush()
        
        # 创建项目报价
        quotation = ProjectQuotation(
            id=1,
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=user.user_id,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        # 创建审批记录
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=quotation.id,
            approver_type="market",
            approver_user_id=user.user_id,
            approval_stage=1,
            is_required="1",
            approval_status="pending",
            create_by=user.user_id,
            create_time=datetime.now()
        )
        db_session.add(approval_record)
        await db_session.flush()
        
        await db_session.commit()
        
        # 验证创建成功
        assert approval_record.id is not None
        assert approval_record.approver_type == "market"
        assert approval_record.approval_status == "pending"

    @pytest.mark.asyncio
    async def test_current_user_model(self):
        """测试当前用户模型"""
        if not IMPORTS_AVAILABLE:
            pytest.skip("必要的模块导入失败")
        
        user_info = UserInfoModel(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户",
            status="0",
            del_flag="0"
        )
        
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=user_info
        )
        
        assert current_user.user.user_id == 1
        assert current_user.user.user_name == "test_user"

    @pytest.mark.asyncio
    async def test_approval_action_model(self):
        """测试审批操作模型"""
        if not IMPORTS_AVAILABLE:
            pytest.skip("必要的模块导入失败")
        
        action = ApprovalActionModel(
            project_quotation_id=1,
            approval_status="approved",
            approval_opinion="测试审批通过"
        )
        
        assert action.project_quotation_id == 1
        assert action.approval_status == "approved"
        assert action.approval_opinion == "测试审批通过"


def test_module_availability():
    """测试模块可用性"""
    modules_to_test = [
        "config.database",
        "module_admin.entity.do.user_do",
        "module_admin.entity.do.role_do",
        "module_admin.entity.vo.user_vo",
        "module_quotation.entity.do.project_quotation_do",
        "module_quotation.entity.do.project_quotation_approval_record_do",
        "module_quotation.entity.vo.project_quotation_approval_record_vo"
    ]
    
    available_modules = []
    unavailable_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            available_modules.append(module_name)
        except ImportError as e:
            unavailable_modules.append((module_name, str(e)))
    
    print(f"\n可用模块 ({len(available_modules)}):")
    for module in available_modules:
        print(f"  ✅ {module}")
    
    print(f"\n不可用模块 ({len(unavailable_modules)}):")
    for module, error in unavailable_modules:
        print(f"  ❌ {module}: {error}")
    
    # 如果有不可用的模块，测试失败
    if unavailable_modules:
        pytest.fail(f"有 {len(unavailable_modules)} 个模块不可用")


if __name__ == "__main__":
    # 直接运行模块可用性测试
    test_module_availability()
