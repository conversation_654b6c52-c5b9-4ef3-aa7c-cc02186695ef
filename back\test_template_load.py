"""
测试模板文件加载
"""
import os
from openpyxl import load_workbook

def test_template_load():
    """测试模板文件是否可以正常加载"""
    
    template_path = "templates/quotation_price_export_tpl.xlsx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    try:
        # 尝试加载模板文件
        wb = load_workbook(template_path)
        ws = wb.active
        
        print(f"✅ 模板文件加载成功!")
        print(f"📊 工作表名称: {ws.title}")
        print(f"📏 最大行数: {ws.max_row}")
        print(f"📏 最大列数: {ws.max_column}")
        
        # 查找标记
        markers_found = []
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value and isinstance(cell.value, str):
                    if "$样品类别" in cell.value:
                        markers_found.append(f"$样品类别 在 行{row_idx} 列{col_idx}")
                    elif "$差旅费" in cell.value:
                        markers_found.append(f"$差旅费 在 行{row_idx} 列{col_idx}")
        
        if markers_found:
            print("🔍 找到的标记:")
            for marker in markers_found:
                print(f"  - {marker}")
        else:
            print("⚠️  未找到标记 $样品类别 或 $差旅费")
        
        # 显示前几行的内容
        print("\n📋 前10行内容预览:")
        for row_idx in range(1, min(11, ws.max_row + 1)):
            row_values = []
            for col_idx in range(1, min(11, ws.max_column + 1)):
                cell = ws.cell(row=row_idx, column=col_idx)
                value = cell.value
                if value is None:
                    value = ""
                elif isinstance(value, str) and len(value) > 20:
                    value = value[:20] + "..."
                row_values.append(str(value))
            print(f"  行{row_idx}: {' | '.join(row_values)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板文件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试模板文件加载...")
    success = test_template_load()
    
    if success:
        print("🎉 模板文件测试通过！")
    else:
        print("💥 模板文件测试失败")
