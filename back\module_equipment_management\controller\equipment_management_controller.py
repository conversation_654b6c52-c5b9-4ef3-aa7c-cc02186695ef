"""
设备管理控制器
"""

from typing import List
from pathlib import Path

from fastapi import APIRouter, Depends, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_equipment_management.entity.vo.equipment_management_vo import (
    AddEquipmentManagementModel,
    EditEquipmentManagementModel,
    EquipmentManagementQueryModel,
    EquipmentManagementImportResultModel,
)
from module_equipment_management.service.equipment_management_service import EquipmentManagementService
from utils.response_util import ResponseUtil

router = APIRouter(prefix="/equipment-management", tags=["设备管理"])


@router.get("/list")
async def get_equipment_page(
    query_params: EquipmentManagementQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
):
    """获取设备列表"""

    result = await EquipmentManagementService(db).get_equipment_page(query_params)
    return ResponseUtil.success(data=result)


@router.get("/{equipment_id}")
async def get_equipment_by_id(
    equipment_id: int,
    db: AsyncSession = Depends(get_db),
):
    """根据ID获取设备信息"""
    result = await EquipmentManagementService(db).get_equipment_by_id(equipment_id)
    return ResponseUtil.success(data=result)


@router.post("/add")
async def add_equipment(
    equipment_data: AddEquipmentManagementModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """添加设备"""
    result = await EquipmentManagementService(db).add_equipment(equipment_data, current_user)
    return ResponseUtil.success(data=result)


@router.put("/update")
async def update_equipment(
    equipment_data: EditEquipmentManagementModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """更新设备"""
    result = await EquipmentManagementService(db).update_equipment(equipment_data, current_user)
    return ResponseUtil.success(data=result)


@router.delete("/{equipment_id}")
async def delete_equipment(
    equipment_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """删除设备"""
    result = await EquipmentManagementService(db).delete_equipment(equipment_id, current_user)
    return ResponseUtil.success(data=result)


@router.delete("/batch")
async def batch_delete_equipment(
    equipment_ids: List[int],
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """批量删除设备"""
    result = await EquipmentManagementService(db).batch_delete_equipment(equipment_ids, current_user)
    return ResponseUtil.success(data=result)


@router.post("/import", response_model=EquipmentManagementImportResultModel)
async def import_equipment_from_excel(
    file: UploadFile = File(..., description="Excel文件"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """从Excel导入设备数据"""
    # 验证文件类型
    if not file.filename.endswith((".xlsx", ".xls")):
        return ResponseUtil.error(message="请上传Excel文件（.xlsx或.xls格式）")

    # 读取文件内容
    file_content = await file.read()

    result = await EquipmentManagementService(db).import_equipment_from_excel(file_content, current_user)
    return ResponseUtil.success(data=result)


@router.get("/export/template")
async def download_import_template(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """下载导入模板"""
    import pandas as pd
    import io
    from fastapi.responses import StreamingResponse

    # 创建模板数据
    template_data = {
        "设备编号": ["EQ001", "EQ002"],
        "测量设备（器具）名称": ["示例设备1", "示例设备2"],
        "启用日期": ["2024-01-01", "2024-01-02"],
        "设备类型": ["测量仪器", "检测设备"],
        "型号规格": ["Model-A", "Model-B"],
        "出厂编号": ["SN001", "SN002"],
        "制造商": ["制造商A", "制造商B"],
        "指标特性": ["精度±0.1%", "精度±0.2%"],
        "检定/校准机构": ["校准机构A", "校准机构B"],
        "溯源方式": ["检定", "校准"],
        "本次校准/核查日期": ["2024-01-01", "2024-01-02"],
        "证书编号": ["CERT001", "CERT002"],
        "下次校准/核查日期": ["2025-01-01", "2025-01-02"],
        "间隔日期": [365, 365],
        "期间核查日期": ["2024-07-01", "2024-07-02"],
        "管理者": ["张三", "李四"],
        "设备状态": ["正常", "正常"],
        "放置地点": ["实验室A", "实验室B"],
        "备注（校准确认）": ["校准正常", "校准正常"],
        "金额": [10000.00, 15000.00],
        "合同": ["合同001", "合同002"],
        "发票": ["发票001", "发票002"],
    }

    # 创建DataFrame
    df = pd.DataFrame(template_data)

    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="设备管理导入模板", index=False)

    output.seek(0)

    # 返回文件
    return StreamingResponse(
        io.BytesIO(output.getvalue()),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": "attachment; filename=equipment_management_import_template.xlsx"},
    )


@router.post("/upload-attachments")
async def upload_attachments(
    files: List[UploadFile] = File(...),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """上传设备附件"""
    from utils.file_utils import file_manager

    try:
        # 验证文件
        if not files or len(files) == 0:
            return ResponseUtil.error(message="请选择要上传的文件")

        # 验证文件大小和类型
        max_file_size = 10 * 1024 * 1024  # 10MB
        allowed_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.txt'}

        for file in files:
            if file.size > max_file_size:
                return ResponseUtil.error(message=f"文件 {file.filename} 超过最大限制 10MB")

            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in allowed_extensions:
                return ResponseUtil.error(message=f"不支持的文件类型: {file_ext}")

        # 保存文件
        file_infos = await file_manager.save_multiple_files(files, "equipment")

        return ResponseUtil.success(data=file_infos, message=f"成功上传 {len(file_infos)} 个文件")

    except Exception as e:
        return ResponseUtil.error(message=f"文件上传失败: {str(e)}")


@router.delete("/delete-attachment")
async def delete_attachment(
    file_path: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """删除设备附件"""
    from utils.file_utils import file_manager

    try:
        success = file_manager.delete_file(file_path)
        if success:
            return ResponseUtil.success(message="文件删除成功")
        else:
            return ResponseUtil.error(message="文件删除失败或文件不存在")
    except Exception as e:
        return ResponseUtil.error(message=f"文件删除失败: {str(e)}")


@router.get("/download-attachment")
async def download_attachment(
    file_path: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """下载设备附件"""
    from utils.file_utils import file_manager
    from fastapi.responses import FileResponse

    try:
        full_path = file_manager.get_file_path(file_path)

        if not full_path.exists():
            return ResponseUtil.error(message="文件不存在")

        # 获取原始文件名（从数据库或文件信息中获取）
        file_info = file_manager.get_file_info(file_path)
        filename = file_info.get("file_name", full_path.name) if file_info else full_path.name

        return FileResponse(
            path=str(full_path),
            filename=filename,
            media_type='application/octet-stream'
        )

    except Exception as e:
        return ResponseUtil.error(message=f"文件下载失败: {str(e)}")
