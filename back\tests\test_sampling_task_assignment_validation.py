import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from module_sampling.service.sampling_task_assignment_service import SamplingTaskAssignmentService
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from exceptions.exception import ServiceException


class TestSamplingTaskAssignmentValidation:
    """采样任务分配验证测试"""

    @pytest.fixture
    def mock_db(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def service(self, mock_db):
        """创建服务实例"""
        return SamplingTaskAssignmentService(mock_db)

    def create_mock_cycle_item(self, item_id: int, project_quotation_item_id: int, cycle_number: int, status: int = 0):
        """创建模拟的检测周期条目"""
        item = MagicMock(spec=DetectionCycleItem)
        item.id = item_id
        item.project_quotation_item_id = project_quotation_item_id
        item.cycle_number = cycle_number
        item.status = status
        return item

    @pytest.mark.asyncio
    async def test_validate_cycle_items_continuous_from_first(self, service, mock_db):
        """测试从第1周期开始的连续选择"""
        # 准备测试数据：项目明细985的第1、2、3周期
        cycle_items = [
            self.create_mock_cycle_item(1, 985, 1),
            self.create_mock_cycle_item(2, 985, 2),
            self.create_mock_cycle_item(3, 985, 3)
        ]
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = cycle_items
        mock_db.execute.return_value = mock_result
        
        # 执行验证，应该通过
        await service._validate_cycle_items([1, 2, 3])

    @pytest.mark.asyncio
    async def test_validate_cycle_items_continuous_from_second(self, service, mock_db):
        """测试从第2周期开始的连续选择"""
        # 准备测试数据：项目明细985的第2、3、4周期
        cycle_items = [
            self.create_mock_cycle_item(1, 985, 2),
            self.create_mock_cycle_item(2, 985, 3),
            self.create_mock_cycle_item(3, 985, 4)
        ]
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = cycle_items
        mock_db.execute.return_value = mock_result
        
        # 执行验证，应该通过
        await service._validate_cycle_items([1, 2, 3])

    @pytest.mark.asyncio
    async def test_validate_cycle_items_non_continuous(self, service, mock_db):
        """测试非连续的周期选择"""
        # 准备测试数据：项目明细985的第1、3周期（跳过第2周期）
        cycle_items = [
            self.create_mock_cycle_item(1, 985, 1),
            self.create_mock_cycle_item(2, 985, 3)  # 跳过了第2周期
        ]
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = cycle_items
        mock_db.execute.return_value = mock_result
        
        # 执行验证，应该抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await service._validate_cycle_items([1, 2])
        
        assert "周期选择必须连续" in str(exc_info.value.message)

    @pytest.mark.asyncio
    async def test_validate_cycle_items_already_assigned(self, service, mock_db):
        """测试已分配的周期条目"""
        # 准备测试数据：项目明细985的第1周期已分配
        cycle_items = [
            self.create_mock_cycle_item(1, 985, 1, status=1),  # 已分配
            self.create_mock_cycle_item(2, 985, 2)
        ]
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = cycle_items
        mock_db.execute.return_value = mock_result
        
        # 执行验证，应该抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await service._validate_cycle_items([1, 2])
        
        assert "已被分配" in str(exc_info.value.message)

    @pytest.mark.asyncio
    async def test_validate_cycle_items_multiple_projects(self, service, mock_db):
        """测试多个项目明细的周期选择"""
        # 准备测试数据：项目明细985的第2、3周期，项目明细986的第1、2周期
        cycle_items = [
            self.create_mock_cycle_item(1, 985, 2),
            self.create_mock_cycle_item(2, 985, 3),
            self.create_mock_cycle_item(3, 986, 1),
            self.create_mock_cycle_item(4, 986, 2)
        ]
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = cycle_items
        mock_db.execute.return_value = mock_result
        
        # 执行验证，应该通过
        await service._validate_cycle_items([1, 2, 3, 4])

    @pytest.mark.asyncio
    async def test_validate_cycle_items_empty_list(self, service, mock_db):
        """测试空的周期条目列表"""
        # 执行验证，应该抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await service._validate_cycle_items([])
        
        assert "请至少选择一个检测周期条目" in str(exc_info.value.message)

    @pytest.mark.asyncio
    async def test_validate_cycle_items_missing_items(self, service, mock_db):
        """测试部分周期条目不存在"""
        # 准备测试数据：只返回1个条目，但请求了2个
        cycle_items = [
            self.create_mock_cycle_item(1, 985, 1)
        ]
        
        # 模拟数据库查询结果
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = cycle_items
        mock_db.execute.return_value = mock_result
        
        # 执行验证，应该抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await service._validate_cycle_items([1, 2])
        
        assert "部分检测周期条目不存在" in str(exc_info.value.message)