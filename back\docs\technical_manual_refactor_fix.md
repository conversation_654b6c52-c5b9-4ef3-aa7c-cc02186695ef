# 技术手册重构修复文档

## 修复概述

本次修复解决了技术手册重构后无法正常使用的问题，主要包括：

1. **类目编码字段改为JSON数组**：支持一个技术手册关联多个类目
2. **自动创建类目功能**：新增技术手册时自动创建不存在的类目
3. **修复列表查询**：确保关联查询正常工作

## 数据库变更

### 技术手册表 (technical_manual)

#### 新增字段
```sql
-- 类目编码列表（JSON数组格式）
category_codes JSON COMMENT '类目编码列表（JSON数组格式）'
```

#### 字段说明
- `category_codes`: JSON数组，存储多个类目编码，如 `["CATE00001", "CATE00002"]`
- `category_code`: 保留字段，标记为已废弃

#### 数据迁移
```sql
-- 将单个类目编码转换为JSON数组
UPDATE technical_manual 
SET category_codes = JSON_ARRAY(category_code) 
WHERE category_code IS NOT NULL AND category_code != '';
```

## 后端实现

### 1. DO模型更新

```python
class TechnicalManual(Base):
    # 原有字段
    category_code = Column(String(20), nullable=True, comment="类目编号（已废弃）")
    # 新增字段
    category_codes = Column(JSON, nullable=True, comment="类目编码列表（JSON数组格式）")
```

### 2. VO模型更新

```python
class TechnicalManualModel(BaseModel):
    category_code: Optional[str] = Field(default=None, description="类目编号（已废弃）")
    category_codes: Optional[List[str]] = Field(default=None, description="类目编码列表")

class AddTechnicalManualModel(TechnicalManualModel):
    # 用于新增时的分类和检测类别
    classification: str = Field(..., description="分类")
    category: str = Field(..., description="检测类别（逗号分隔）")
```

### 3. 类目自动创建服务

```python
class TechnicalManualCategoryService:
    def create_categories_if_not_exists(self, classification: str, categories_str: str, current_user: CurrentUserModel) -> List[str]:
        """
        根据分类和逗号分割的检测类别字符串，创建不存在的类目并返回类目编码列表
        """
        # 解析检测类别
        categories = [cat.strip() for cat in categories_str.split(',') if cat.strip()]
        
        category_codes = []
        for category in categories:
            # 检查是否已存在
            existing_category = self.dao.get_by_classification_and_category(classification, category)
            
            if existing_category:
                category_codes.append(existing_category.category_code)
            else:
                # 创建新的类目
                category_data = TechnicalManualCategoryModel(
                    classification=classification,
                    category=category,
                    category_code=self.dao.get_next_category_code(),
                    remark=f"技术手册新增时自动创建 - {classification}/{category}"
                )
                new_category = self.dao.create(category_data)
                category_codes.append(new_category.category_code)
        
        return category_codes
```

### 4. 技术手册服务更新

#### 新增技术手册
```python
async def add_technical_manual(self, technical_manual_model: AddTechnicalManualModel, current_user: CurrentUserModel):
    # 根据分类和检测类别创建类目并获取类目编码数组
    category_codes = self.category_service.create_categories_if_not_exists(
        technical_manual_model.classification, 
        technical_manual_model.category, 
        current_user
    )
    
    # 创建技术手册对象
    technical_manual = TechnicalManual(
        category_codes=category_codes,  # 使用类目编码数组
        parameter=technical_manual_model.parameter,
        method=technical_manual_model.method,
        # ... 其他字段
    )
```

#### 查询技术手册
```python
async def get_technical_manual_list(self, query_object: TechnicalManualQueryModel):
    # 如果有类目编号条件，使用JSON_CONTAINS查询
    if category_codes:
        category_conditions = []
        for code in category_codes:
            category_conditions.append(func.json_contains(TechnicalManual.category_codes, f'"{code}"'))
        conditions.append(or_(*category_conditions))
    
    # 执行关联查询
    stmt = (
        select(TechnicalManual, TechnicalManualCategory)
        .join(TechnicalManualCategory, func.json_contains(TechnicalManual.category_codes, func.concat('"', TechnicalManualCategory.category_code, '"')))
        .where(and_(*conditions))
    )
```

#### 唯一性检查
```python
async def check_technical_manual_unique(self, category_code: str, parameter: str, method: str, id: int = None):
    conditions = [
        func.json_contains(TechnicalManual.category_codes, f'"{category_code}"'),
        TechnicalManual.parameter == parameter,
        TechnicalManual.method == method,
    ]
```

## API接口

### 1. 新增技术手册

```http
POST /api/basedata/technical-manual
```

**请求体**:
```json
{
  "classification": "水",
  "category": "水和废水,地表水",  // 逗号分隔的检测类别
  "parameter": "pH值",
  "method": "玻璃电极法",
  "aliasList": ["酸碱度", "氢离子浓度"],
  "specialConsumablesPrice": 15.50,
  "qualificationCode": "CMA001",
  "limitationScope": "地表水、地下水",
  "qualificationDate": "2023-01-01",
  "hasQualification": "0",
  "status": "0",
  "remark": "备注信息"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "新增成功",
  "data": {
    "id": 1,
    "categoryCodes": ["CATE00001", "CATE00002"]  // 自动创建的类目编码
  }
}
```

### 2. 查询技术手册

```http
GET /api/basedata/technical-manual/list?classification=水&category=水和废水
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryCodes": ["CATE00001", "CATE00002"],
      "parameter": "pH值",
      "method": "玻璃电极法",
      "classification": "水",
      "category": "水和废水",
      "aliasList": ["酸碱度", "氢离子浓度"],
      "specialConsumablesPrice": 15.50,
      "qualificationCode": "CMA001",
      "status": "0"
    }
  ]
}
```

## 数据库查询示例

### 1. JSON数组查询
```sql
-- 查询包含特定类目编码的技术手册
SELECT * FROM technical_manual 
WHERE JSON_CONTAINS(category_codes, '"CATE00001"');

-- 查询包含多个类目编码之一的技术手册
SELECT * FROM technical_manual 
WHERE JSON_CONTAINS(category_codes, '"CATE00001"')
   OR JSON_CONTAINS(category_codes, '"CATE00002"');

-- 获取类目编码数量
SELECT id, parameter, JSON_LENGTH(category_codes) as category_count
FROM technical_manual 
WHERE category_codes IS NOT NULL;
```

### 2. 关联查询
```sql
-- 技术手册与类目表关联查询
SELECT tm.id, tm.parameter, tm.method, tm.category_codes,
       GROUP_CONCAT(CONCAT(tmc.classification, '-', tmc.category) SEPARATOR ', ') as categories_info
FROM technical_manual tm
JOIN technical_manual_category tmc ON JSON_CONTAINS(tm.category_codes, CONCAT('"', tmc.category_code, '"'))
WHERE tm.category_codes IS NOT NULL
GROUP BY tm.id, tm.parameter, tm.method, tm.category_codes;
```

## 核心功能特性

### 1. 多类目支持
- 一个技术手册可以关联多个类目
- 支持逗号分隔的检测类别输入
- 自动创建不存在的类目

### 2. 自动类目创建
- 新增技术手册时自动解析检测类别
- 检查类目是否存在，不存在则自动创建
- 生成唯一的类目编号（CATE+5位递增数）

### 3. JSON查询优化
- 使用JSON_CONTAINS进行高效查询
- 支持多类目编码的OR查询
- 保持查询性能

### 4. 数据完整性
- 保留原有字段确保向后兼容
- 自动数据迁移，零数据丢失
- JSON格式验证

## 验证结果

### 数据迁移验证
- ✅ category_codes字段正确添加
- ✅ 现有数据成功迁移到JSON格式
- ✅ 所有JSON格式验证通过
- ✅ 关联查询正常工作

### 功能验证
- ✅ 类目自动创建功能正常
- ✅ JSON查询功能正常
- ✅ 多类目编码支持正常
- ✅ 数据完整性验证通过

## 使用示例

### 前端使用
```javascript
// 新增技术手册
const data = {
  classification: '水',
  category: '水和废水,地表水,地下水',  // 逗号分隔
  parameter: 'pH值',
  method: '玻璃电极法',
  aliasList: ['酸碱度', '氢离子浓度'],
  specialConsumablesPrice: 15.50
}

const result = await api.post('/api/basedata/technical-manual', data)
// 返回: { categoryCodes: ['CATE00001', 'CATE00002', 'CATE00003'] }

// 查询技术手册
const manuals = await api.get('/api/basedata/technical-manual/list?classification=水')
```

### 后端使用
```python
# 创建类目
category_codes = category_service.create_categories_if_not_exists(
    "水", "水和废水,地表水", current_user
)

# 查询技术手册
conditions.append(func.json_contains(TechnicalManual.category_codes, '"CATE00001"'))
```

## 注意事项

1. **性能考虑**：JSON查询可能比普通字段查询稍慢，但在可接受范围内
2. **数据一致性**：确保类目编码的唯一性和有效性
3. **向后兼容**：保留原有字段，支持渐进式升级
4. **错误处理**：完善的异常处理和日志记录

这次修复完全解决了技术手册重构后的使用问题，提供了更强大和灵活的类目管理功能。
