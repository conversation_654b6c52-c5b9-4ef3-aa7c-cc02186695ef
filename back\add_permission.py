#!/usr/bin/env python3
"""
添加assignment-execution:all权限的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import AsyncSessionLocal
from sqlalchemy import text

async def add_permission():
    """添加assignment-execution:all权限"""
    
    sql_statements = [
        # 1. 查找采样管理菜单ID
        """
        SET @sampling_menu_id = (
            SELECT menu_id 
            FROM sys_menu 
            WHERE path = 'sampling' 
            AND parent_id = 0
        );
        """,
        
        # 2. 添加采样执行菜单（如果不存在）
        """
        INSERT IGNORE INTO `sys_menu` (
            `menu_name`, `parent_id`, `order_num`, `path`, `component`, 
            `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, 
            `status`, `perms`, `icon`, `create_by`, `create_time`, 
            `update_by`, `update_time`, `remark`
        ) VALUES (
            '采样执行', @sampling_menu_id, 4, 'execution', 'sampling/execution/index', 
            NULL, 1, 0, 'C', '0', 
            '0', 'sampling:execution:list', 'execution', 'admin', NOW(), 
            '', NULL, '采样执行菜单'
        );
        """,
        
        # 3. 获取采样执行菜单ID
        """
        SET @execution_menu_id = (
            SELECT menu_id 
            FROM sys_menu 
            WHERE path = 'execution' 
            AND parent_id = @sampling_menu_id
        );
        """,
        
        # 4. 添加"查看所有执行任务"权限
        """
        INSERT IGNORE INTO `sys_menu` (
            `menu_name`, `parent_id`, `order_num`, `path`, `component`, 
            `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, 
            `status`, `perms`, `icon`, `create_by`, `create_time`, 
            `update_by`, `update_time`, `remark`
        ) VALUES (
            '查看所有执行任务', @execution_menu_id, 1, '', '', 
            NULL, 1, 0, 'F', '0', 
            '0', 'assignment-execution:all', '#', 'admin', NOW(), 
            '', NULL, '查看所有执行任务权限'
        );
        """,
        
        # 5. 为超级管理员角色分配权限
        """
        INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) 
        SELECT 1, menu_id 
        FROM sys_menu 
        WHERE perms = 'assignment-execution:all';
        """
    ]
    
    async with AsyncSessionLocal() as session:
        try:
            for sql in sql_statements:
                await session.execute(text(sql))
            
            await session.commit()
            print("✅ 权限添加成功！")
            
            # 查询添加的权限
            result = await session.execute(text("""
                SELECT 
                    m.menu_id,
                    m.menu_name,
                    m.perms,
                    p.menu_name as parent_menu_name
                FROM sys_menu m
                LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
                WHERE m.perms = 'assignment-execution:all'
            """))
            
            rows = result.fetchall()
            if rows:
                print("\n📋 添加的权限信息：")
                for row in rows:
                    print(f"  - 权限ID: {row[0]}")
                    print(f"  - 权限名称: {row[1]}")
                    print(f"  - 权限标识: {row[2]}")
                    print(f"  - 父级菜单: {row[3]}")
            else:
                print("⚠️  未找到添加的权限，可能已存在")
                
        except Exception as e:
            await session.rollback()
            print(f"❌ 添加权限失败: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(add_permission())
