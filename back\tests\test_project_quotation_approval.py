"""
项目报价审批功能单元测试
"""

import pytest
import json
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService


class TestProjectQuotationApproval:
    """
    项目报价审批测试类
    """

    @pytest.fixture
    async def setup_test_data(self, db_session: AsyncSession):
        """
        设置测试数据
        """
        # 创建测试角色
        market_role = SysRole(
            role_name="市场审批人员",
            role_key="market-approver",
            role_sort=10,
            status="0"
        )
        lab_role = SysRole(
            role_name="实验室审批人员",
            role_key="lab-approver",
            role_sort=11,
            status="0"
        )
        field_role = SysRole(
            role_name="现场审批人员",
            role_key="field-approver",
            role_sort=12,
            status="0"
        )

        db_session.add_all([market_role, lab_role, field_role])
        await db_session.flush()

        # 创建测试用户
        market_user = SysUser(
            user_name="market_user",
            nick_name="市场用户",
            status="0",
            del_flag="0"
        )
        lab_user = SysUser(
            user_name="lab_user",
            nick_name="实验室用户",
            status="0",
            del_flag="0"
        )
        field_user = SysUser(
            user_name="field_user",
            nick_name="现场用户",
            status="0",
            del_flag="0"
        )

        db_session.add_all([market_user, lab_user, field_user])
        await db_session.flush()

        # 创建用户角色关联
        user_roles = [
            SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
            SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
            SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
        ]
        db_session.add_all(user_roles)

        # 创建测试项目报价
        quotation = ProjectQuotation(
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()

        await db_session.commit()

        return {
            "quotation": quotation,
            "market_user": market_user,
            "lab_user": lab_user,
            "field_user": field_user,
            "market_role": market_role,
            "lab_role": lab_role,
            "field_role": field_role
        }

    async def test_init_approval_records_sampling(self, db_session: AsyncSession, setup_test_data):
        """
        测试一般采样项目的审批记录初始化
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        market_user = test_data["market_user"]

        # 创建当前用户模型
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )

        # 初始化审批记录
        approval_service = ProjectQuotationApprovalService(db_session)
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 验证审批记录是否正确创建
        records = await approval_service.dao.get_approval_records_by_quotation_id(quotation.id)

        # 应该有3类审批记录：市场、实验室、现场
        assert len(records) == 3

        # 验证市场审批记录
        market_records = [r for r in records if r.approver_type == "market"]
        assert len(market_records) == 1
        assert market_records[0].approval_stage == 1
        assert market_records[0].is_required == "1"

        # 验证实验室审批记录
        lab_records = [r for r in records if r.approver_type == "lab"]
        assert len(lab_records) == 1
        assert lab_records[0].approval_stage == 2
        assert lab_records[0].is_required == "1"

        # 验证现场审批记录
        field_records = [r for r in records if r.approver_type == "field"]
        assert len(field_records) == 1
        assert field_records[0].approval_stage == 2
        assert field_records[0].is_required == "1"  # 一般采样需要现场审批

    async def test_init_approval_records_sample(self, db_session: AsyncSession, setup_test_data):
        """
        测试送样项目的审批记录初始化
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        quotation.business_type = "sample"  # 修改为送样
        market_user = test_data["market_user"]

        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )

        approval_service = ProjectQuotationApprovalService(db_session)
        await approval_service.init_approval_records(quotation.id, "sample", current_user)

        records = await approval_service.dao.get_approval_records_by_quotation_id(quotation.id)

        # 验证送样项目没有现场审批记录
        field_records = [r for r in records if r.approver_type == "field"]
        assert len(field_records) == 0  # 送样项目不应该有现场审批记录
        
        # 验证只有市场和实验室审批记录
        assert len(records) == 2
        approver_types = [r.approver_type for r in records]
        assert "market" in approver_types
        assert "lab" in approver_types

    async def test_approval_workflow(self, db_session: AsyncSession, setup_test_data):
        """
        测试完整的审批流程
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        market_user = test_data["market_user"]
        lab_user = test_data["lab_user"]
        field_user = test_data["field_user"]

        approval_service = ProjectQuotationApprovalService(db_session)

        # 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 市场审批通过
        market_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="市场审批通过"
        )
        await approval_service.perform_approval(quotation.id, market_action, market_current_user)

        # 实验室审批通过
        lab_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=lab_user.user_id, user_name=lab_user.user_name)
        )
        lab_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="实验室审批通过"
        )
        await approval_service.perform_approval(quotation.id, lab_action, lab_current_user)

        # 现场审批通过
        field_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=field_user.user_id, user_name=field_user.user_name)
        )
        field_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="现场审批通过"
        )
        await approval_service.perform_approval(quotation.id, field_action, field_current_user)

        # 验证最终状态
        approval_status = await approval_service.get_approval_status(quotation.id)
        assert approval_status.overall_status == "2"  # 已审核

    async def test_approval_rejection(self, db_session: AsyncSession, setup_test_data):
        """
        测试审批拒绝流程
        """
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        market_user = test_data["market_user"]

        approval_service = ProjectQuotationApprovalService(db_session)

        # 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 市场审批拒绝
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="rejected",
            approval_opinion="市场审批拒绝"
        )
        await approval_service.perform_approval(quotation.id, market_action, current_user)

        # 验证最终状态
        approval_status = await approval_service.get_approval_status(quotation.id)
        assert approval_status.overall_status == "4"  # 已拒绝


class TestProjectQuotationApprovalAPI:
    """
    项目报价审批API接口测试类
    """

    @pytest.fixture
    async def setup_api_test_data(self, db: AsyncSession):
        """
        设置API测试数据
        """
        # 创建测试角色
        market_role = SysRole(
            role_id=1,
            role_name="市场审批人员",
            role_key="market-approver",
            role_sort=10,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_role = SysRole(
            role_id=2,
            role_name="实验室审批人员",
            role_key="lab-approver",
            role_sort=11,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_role = SysRole(
            role_id=3,
            role_name="现场审批人员",
            role_key="field-approver",
            role_sort=12,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )

        db.add_all([market_role, lab_role, field_role])
        await db.flush()

        # 创建测试用户
        market_user = SysUser(
            user_id=1,
            user_name="market_user",
            nick_name="市场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_user = SysUser(
            user_id=2,
            user_name="lab_user",
            nick_name="实验室用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_user = SysUser(
            user_id=3,
            user_name="field_user",
            nick_name="现场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )

        db.add_all([market_user, lab_user, field_user])
        await db.flush()

        # 创建用户角色关联
        user_roles = [
            SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
            SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
            SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
        ]
        db.add_all(user_roles)

        # 创建测试项目报价
        quotation = ProjectQuotation(
            id=1,
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        db.add(quotation)
        await db.flush()

        await db.commit()

        return {
            "quotation": quotation,
            "market_user": market_user,
            "lab_user": lab_user,
            "field_user": field_user,
            "market_role": market_role,
            "lab_role": lab_role,
            "field_role": field_role
        }

    def test_init_approval_records_api(self, client: TestClient, setup_api_test_data):
        """
        测试初始化审批记录API
        """
        # 模拟登录用户
        headers = {"Authorization": "Bearer test_token"}

        # 测试初始化审批记录
        response = client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "成功" in data["message"]

    def test_get_approval_status_api(self, client: TestClient, setup_api_test_data):
        """
        测试获取审批状态API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 先初始化审批记录
        client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )

        # 获取审批状态
        response = client.get(
            "/quotation/approval/status/1",
            headers=headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data

        approval_data = data["data"]
        assert approval_data["projectQuotationId"] == 1
        assert approval_data["businessType"] == "sampling"
        assert "approvalRecords" in approval_data

    def test_submit_for_approval_api(self, client: TestClient, setup_api_test_data):
        """
        测试提交审批API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 先初始化审批记录
        client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )

        # 提交审批
        response = client.post(
            "/quotation/approval/submit/1",
            headers=headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "提交审批成功" in data["message"]

    def test_perform_approval_api(self, client: TestClient, setup_api_test_data):
        """
        测试执行审批操作API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 先初始化审批记录并提交
        client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )
        client.post(
            "/quotation/approval/submit/1",
            headers=headers
        )

        # 执行审批操作
        approval_data = {
            "projectQuotationId": 1,
            "approvalStatus": "approved",
            "approvalOpinion": "测试审批通过"
        }

        response = client.post(
            "/quotation/approval/approve/1",
            headers=headers,
            json=approval_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "审批通过成功" in data["message"]

    def test_get_pending_approvals_api(self, client: TestClient, setup_api_test_data):
        """
        测试获取待审批项目列表API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 先初始化审批记录并提交
        client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )
        client.post(
            "/quotation/approval/submit/1",
            headers=headers
        )

        # 获取待审批列表
        response = client.get(
            "/quotation/approval/pending",
            headers=headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
        assert isinstance(data["data"], list)

    def test_approval_workflow_complete_api(self, client: TestClient, setup_api_test_data):
        """
        测试完整审批流程API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 1. 初始化审批记录
        response = client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )
        assert response.status_code == 200

        # 2. 提交审批
        response = client.post(
            "/quotation/approval/submit/1",
            headers=headers
        )
        assert response.status_code == 200

        # 3. 市场审批通过
        market_approval = {
            "projectQuotationId": 1,
            "approvalStatus": "approved",
            "approvalOpinion": "市场审批通过"
        }
        response = client.post(
            "/quotation/approval/approve/1",
            headers=headers,
            json=market_approval
        )
        assert response.status_code == 200

        # 4. 实验室审批通过
        lab_approval = {
            "projectQuotationId": 1,
            "approvalStatus": "approved",
            "approvalOpinion": "实验室审批通过"
        }
        response = client.post(
            "/quotation/approval/approve/1",
            headers=headers,
            json=lab_approval
        )
        assert response.status_code == 200

        # 5. 现场审批通过
        field_approval = {
            "projectQuotationId": 1,
            "approvalStatus": "approved",
            "approvalOpinion": "现场审批通过"
        }
        response = client.post(
            "/quotation/approval/approve/1",
            headers=headers,
            json=field_approval
        )
        assert response.status_code == 200

        # 6. 验证最终状态
        response = client.get(
            "/quotation/approval/status/1",
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["overallStatus"] == "2"  # 已审核

    def test_approval_rejection_api(self, client: TestClient, setup_api_test_data):
        """
        测试审批拒绝流程API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 1. 初始化审批记录
        client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )

        # 2. 提交审批
        client.post(
            "/quotation/approval/submit/1",
            headers=headers
        )

        # 3. 市场审批拒绝
        rejection_data = {
            "projectQuotationId": 1,
            "approvalStatus": "rejected",
            "approvalOpinion": "市场审批拒绝，需要修改"
        }
        response = client.post(
            "/quotation/approval/approve/1",
            headers=headers,
            json=rejection_data
        )
        assert response.status_code == 200

        # 4. 验证最终状态
        response = client.get(
            "/quotation/approval/status/1",
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["overallStatus"] == "4"  # 已拒绝

    def test_sample_business_type_workflow_api(self, client: TestClient, setup_api_test_data):
        """
        测试送样业务类型的审批流程API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 1. 初始化送样项目的审批记录
        response = client.post(
            "/quotation/approval/init/1?business_type=sample",
            headers=headers
        )
        assert response.status_code == 200

        # 2. 获取审批状态，验证现场审批不是必需的
        response = client.get(
            "/quotation/approval/status/1",
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()

        # 验证审批记录中现场审批不是必需的
        approval_records = data["data"]["approvalRecords"]
        field_records = [r for r in approval_records if r["approverType"] == "field"]
        # 对于送样项目，现场审批应该存在但不是必需的
        assert len(field_records) > 0

    def test_error_handling_api(self, client: TestClient):
        """
        测试错误处理API
        """
        headers = {"Authorization": "Bearer test_token"}

        # 测试不存在的项目ID
        response = client.get(
            "/quotation/approval/status/999",
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] != 200  # 应该返回错误

        # 测试无效的审批操作
        invalid_approval = {
            "projectQuotationId": 999,
            "approvalStatus": "invalid_status",
            "approvalOpinion": "测试无效状态"
        }
        response = client.post(
            "/quotation/approval/approve/999",
            headers=headers,
            json=invalid_approval
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] != 200  # 应该返回错误

    def test_permission_control_api(self, client: TestClient, setup_api_test_data):
        """
        测试权限控制API
        """
        # 测试无权限用户的访问
        headers = {"Authorization": "Bearer invalid_token"}

        response = client.get(
            "/quotation/approval/status/1",
            headers=headers
        )
        # 根据实际的权限控制实现，这里可能返回401或403
        assert response.status_code in [401, 403, 200]  # 允许多种可能的响应
