-- 简单添加assignment-execution:all权限
-- 直接插入权限菜单项

-- 1. 添加"查看所有执行任务"权限菜单项
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, 
    `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, 
    `status`, `perms`, `icon`, `create_by`, `create_time`, 
    `update_by`, `update_time`, `remark`
) VALUES (
    '查看所有执行任务', 0, 999, '', '', 
    NULL, 1, 0, 'F', '0', 
    '0', 'assignment-execution:all', '#', 'admin', NOW(), 
    '', NULL, '查看所有执行任务权限'
);

-- 2. 为超级管理员角色分配权限（假设超级管理员角色ID为1）
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 1, menu_id 
FROM sys_menu 
WHERE perms = 'assignment-execution:all';

-- 3. 查看添加的权限
SELECT 
    m.menu_id,
    m.menu_name,
    m.perms,
    m.create_time
FROM sys_menu m
WHERE m.perms = 'assignment-execution:all';
