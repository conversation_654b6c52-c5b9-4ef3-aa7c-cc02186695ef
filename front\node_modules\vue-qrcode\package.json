{"name": "vue-qrcode", "version": "2.2.2", "type": "module", "description": "🤳 A Vue component for QR code generation with `qrcode`", "repository": "**************:rx-ts/vue.git", "homepage": "https://github.com/rx-ts/vue/blob/master/packages/vue-qrcode", "author": "<PERSON><PERSON><PERSON><PERSON> (https://www.1stG.me) <<EMAIL>>", "funding": "https://opencollective.com/rxts", "license": "MIT", "main": "./lib/index.cjs", "module": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.esm.mjs", "require": "./lib/index.cjs", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "types": "./lib/index.d.ts", "files": ["lib", "!**/*.tsbuildinfo"], "keywords": ["vue-qrcode", "qrcode", "vue"], "peerDependencies": {"qrcode": "^1.0.0", "vue": "^2.7.0 || ^3.0.0"}, "dependencies": {"tslib": "^2.6.2"}, "publishConfig": {"access": "public"}, "sideEffects": false}