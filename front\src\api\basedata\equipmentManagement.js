import request from '@/utils/request'

// 获取设备管理列表
export function listEquipmentManagement(query) {
  return request({
    url: '/equipment-management/list',
    method: 'get',
    params: query
  })
}

// 获取设备管理详细信息
export function getEquipmentManagement(id) {
  return request({
    url: `/equipment-management/${id}`,
    method: 'get'
  })
}

// 新增设备管理
export function addEquipmentManagement(data) {
  return request({
    url: '/equipment-management/add',
    method: 'post',
    data: data
  })
}

// 修改设备管理
export function updateEquipmentManagement(data) {
  return request({
    url: '/equipment-management/update',
    method: 'put',
    data: data
  })
}

// 删除设备管理
export function delEquipmentManagement(id) {
  return request({
    url: `/equipment-management/${id}`,
    method: 'delete'
  })
}

// 批量删除设备管理
export function batchDelEquipmentManagement(ids) {
  return request({
    url: '/equipment-management/batch',
    method: 'delete',
    data: ids
  })
}

// 导入设备管理
export function importEquipmentManagement(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/equipment-management/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载导入模板
export function downloadEquipmentManagementTemplate() {
  return request({
    url: '/equipment-management/export/template',
    method: 'get',
    responseType: 'blob',
    timeout: 30000
  })
}

// 新增设备附件
export function addEquipmentAttachMents(id, filePath) {
  return request({
    url: `/equipment-management/add-attachments`,
    method: 'delete',
    data: {file_path: filePath}
  })
}

// 删除设备附件
export function delEquipmentAttachMent(filePath) {
  return request({
    url: `/equipment-management/delete-attachment`,
    method: 'delete',
    data: {file_path: filePath}
  })
}


// 下载设备附件
export function downEquipmentAttachMent(filePath) {
  return request({
    url: `/equipment-management/download-attachment`,
    method: 'get',
    params: {file_path: filePath},
    responseType: 'blob'
  })
}