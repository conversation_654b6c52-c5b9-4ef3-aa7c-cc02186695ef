import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from module_sampling.service.detection_cycle_item_service import DetectionCycleItemService
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO
from datetime import datetime


@pytest.mark.asyncio
class TestDetectionCycleItemDTOFix:
    """测试检测周期条目DTO的project_name字段修复"""

    async def test_detection_cycle_item_dto_has_project_name_field(self, db_session: AsyncSession):
        """测试DetectionCycleItemDTO包含project_name字段"""
        # 创建测试数据
        quotation = ProjectQuotation(
            project_name="测试项目名称",
            project_code="TEST001",
            customer_name="测试客户",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            detection_qualification="CMA",
            detection_classification="环境检测",
            detection_category="水质",
            detection_parameter="pH",
            detection_method="玻璃电极法",
            sample_source="地表水",
            point_name="采样点1",
            cycle_type="月度",
            cycle_count=12,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试服务层方法
        service = DetectionCycleItemService(db_session)
        result = await service.get_cycle_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(result) == 1
        dto = result[0]
        
        # 验证DTO是DetectionCycleItemDTO类型
        assert isinstance(dto, DetectionCycleItemDTO)
        
        # 验证project_name字段存在且有正确的值
        assert hasattr(dto, 'project_name')
        assert dto.project_name == "测试项目名称"
        
        # 验证其他字段也正常
        assert dto.detection_parameter == "pH"
        assert dto.detection_method == "玻璃电极法"
        assert dto.sample_source == "地表水"
        assert dto.point_name == "采样点1"

    async def test_detection_cycle_item_dto_serialization(self, db_session: AsyncSession):
        """测试DetectionCycleItemDTO序列化包含project_name字段"""
        # 创建测试数据
        quotation = ProjectQuotation(
            project_name="序列化测试项目",
            project_code="SERIAL001",
            customer_name="序列化测试客户",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            detection_qualification="CMA",
            detection_classification="环境检测",
            detection_category="大气",
            detection_parameter="PM2.5",
            detection_method="重量法",
            sample_source="环境空气",
            point_name="监测点1",
            cycle_type="日",
            cycle_count=30,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试服务层方法
        service = DetectionCycleItemService(db_session)
        result = await service.get_cycle_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(result) == 1
        dto = result[0]
        
        # 测试序列化为字典
        dto_dict = dto.model_dump()
        assert 'project_name' in dto_dict
        assert dto_dict['project_name'] == "序列化测试项目"
        
        # 测试序列化为JSON
        dto_json = dto.model_dump_json()
        assert '"project_name"' in dto_json
        assert '"序列化测试项目"' in dto_json

    def test_detection_cycle_item_dto_field_definition(self):
        """测试DetectionCycleItemDTO字段定义"""
        # 验证DTO类有project_name字段定义
        dto_fields = DetectionCycleItemDTO.model_fields
        assert 'project_name' in dto_fields
        
        # 验证字段类型
        project_name_field = dto_fields['project_name']
        assert project_name_field.default is None
        
        # 创建空DTO实例测试
        dto = DetectionCycleItemDTO(
            id=1,
            project_quotation_id=1,
            project_quotation_item_id=1,
            cycle_number=1,
            status=0
        )
        
        # 验证project_name字段可以访问
        assert hasattr(dto, 'project_name')
        assert dto.project_name is None
        
        # 验证可以设置project_name
        dto.project_name = "测试项目"
        assert dto.project_name == "测试项目"