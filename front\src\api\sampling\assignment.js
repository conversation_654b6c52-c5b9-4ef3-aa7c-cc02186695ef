import request from '@/utils/request'

// 查询已审批项目报价分页列表
export function getApprovedQuotationsPage(query) {
  return request({
    url: '/sampling/assignment/approved-quotations/page',
    method: 'get',
    params: query
  })
}

// 获取项目报价的检测周期条目
export function getQuotationCycleItems(quotationId) {
  return request({
    url: `/sampling/assignment/quotation/${quotationId}/cycle-items`,
    method: 'get'
  })
}

// 创建采样任务
export function createSamplingTask(data) {
  return request({
    url: '/sampling/assignment/create-task',
    method: 'post',
    data: data
  })
}