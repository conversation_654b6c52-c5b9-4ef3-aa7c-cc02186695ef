"""
项目报价特殊耗材费用明细控制器
"""
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from config.database import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_quotation.entity.vo.project_quotation_special_consumable_vo import (
    ProjectQuotationSpecialConsumableResponseModel,
    AddProjectQuotationSpecialConsumableModel,
    EditProjectQuotationSpecialConsumableModel
)
from module_quotation.service.project_quotation_special_consumable_service import ProjectQuotationSpecialConsumableService
from utils.response_util import ResponseUtil

router = APIRouter(prefix="/quotation/special-consumable", tags=["项目报价特殊耗材费用明细"])


@router.get("/list", response_model=List[ProjectQuotationSpecialConsumableResponseModel], summary="获取特殊耗材费用明细列表")
async def get_special_consumables_list(
    quotation_id: int = Query(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取特殊耗材费用明细列表

    :param quotation_id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 特殊耗材费用明细列表
    """
    service = ProjectQuotationSpecialConsumableService(db)
    result = await service.get_special_consumables_by_quotation_id(quotation_id)
    
    return ResponseUtil.success(data=result)


@router.post("/add", response_model=CrudResponseModel, summary="添加特殊耗材费用明细")
async def add_special_consumable(
    quotation_id: int = Query(..., description="项目报价ID"),
    add_model: AddProjectQuotationSpecialConsumableModel = ...,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    添加特殊耗材费用明细

    :param quotation_id: 项目报价ID
    :param add_model: 添加模型
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 添加结果
    """
    service = ProjectQuotationSpecialConsumableService(db)
    result = await service.add_special_consumable(quotation_id, add_model, current_user)
    
    return ResponseUtil.success(data=result, message="添加成功")


@router.put("/edit", response_model=CrudResponseModel, summary="编辑特殊耗材费用明细")
async def edit_special_consumable(
    edit_model: EditProjectQuotationSpecialConsumableModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑特殊耗材费用明细

    :param edit_model: 编辑模型
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = ProjectQuotationSpecialConsumableService(db)
    result = await service.edit_special_consumable(edit_model, current_user)
    
    return ResponseUtil.success(data=result, message="编辑成功")


@router.delete("/delete", response_model=CrudResponseModel, summary="删除特殊耗材费用明细")
async def delete_special_consumable(
    special_consumable_id: int = Query(..., description="特殊耗材费用明细ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除特殊耗材费用明细

    :param special_consumable_id: 特殊耗材费用明细ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = ProjectQuotationSpecialConsumableService(db)
    await service.delete_special_consumable(special_consumable_id)
    
    return ResponseUtil.success(message="删除成功")
