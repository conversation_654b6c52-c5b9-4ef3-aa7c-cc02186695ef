import request from '@/utils/request'

// 查询项目报价列表
export function listProjectQuotation(query) {
  return request({
    url: '/quotation/project-quotation/list',
    method: 'get',
    params: query
  })
}

// 查询已审批项目报价列表
export function listApprovedProjectQuotation(query) {
  return request({
    url: '/quotation/project-quotation/approved-list',
    method: 'get',
    params: query
  })
}



// 查询项目报价分页列表
export function pageProjectQuotation(query) {
  return request({
    url: '/quotation/project-quotation/page',
    method: 'get',
    params: query
  })
}

// 查询项目报价详细
export function getProjectQuotation(id) {
  return request({
    url: '/quotation/project-quotation/' + id,
    method: 'get'
  })
}

// 新增项目报价
export function addProjectQuotation(data) {
  return request({
    url: '/quotation/project-quotation',
    method: 'post',
    data: data
  })
}

// 修改项目报价
export function updateProjectQuotation(data) {
  return request({
    url: '/quotation/project-quotation',
    method: 'put',
    data: data
  })
}

// 更新项目报价费用
export function updateProjectQuotationFee(data) {
  return request({
    url: '/quotation/project-quotation/quotation-fee',
    method: 'put',
    data: data
  })
}

// 更新单行其他费用
export function updateSingleOtherFee(data) {
  return request({
    url: '/quotation/project-quotation/other-fee',
    method: 'put',
    data: data
  })
}

// 删除项目报价
export function delProjectQuotation(id) {
  return request({
    url: '/quotation/project-quotation/' + id,
    method: 'delete'
  })
}

// 导出项目报价
export function exportProjectQuotation(query) {
  return request({
    url: '/quotation/project-quotation/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function searchCustomer(query) {
  return request({
    url: '/quotation/project-quotation/customer/search',
    method: 'get',
    params: query
  })
}

// 获取项目报价费用计算详情
export function getProjectQuotationFeeCalculation(id) {
  return request({
    url: `/quotation/project-quotation/${id}/fee-calculation`,
    method: 'get'
  })
}

// 同步基础价目表数据
export function syncBasedataPrices(id) {
  return request({
    url: `/quotation/project-quotation/${id}/sync-basedata-prices`,
    method: 'post'
  })
}

// 下载项目报价明细批量导入模板
export function downloadProjectQuotationItemsImportTemplate() {
  return request({
    url: '/quotation/project-quotation/items/import/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导入项目报价明细
export function importProjectQuotationItems(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: `/quotation/project-quotation/items/import`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取项目报价客服
export function getProjectQuotationCustomerSupports(id) {
  return request({
    url: `/quotation/project-quotation/${id}/customer-supports`,
    method: 'get'
  })
}

// 设置项目报价客服
export function setProjectQuotationCustomerSupports(id, data) {
  return request({
    url: `/quotation/project-quotation/${id}/customer-supports`,
    method: 'post',
    data: data
  })
}

// 删除项目报价客服
export function deleteProjectQuotationCustomerSupports(id) {
  return request({
    url: `/quotation/project-quotation/${id}/customer-supports`,
    method: 'delete'
  })
}

// 提交项目报价审批
export function submitProjectQuotationApproval(id) {
  return request({
    url: `/quotation/project-quotation/${id}/submit-approval`,
    method: 'post'
  })
}

// 撤回项目报价审批
export function withdrawProjectQuotationApproval(id) {
  return request({
    url: `/quotation/project-quotation/${id}/withdraw-approval`,
    method: 'post'
  })
}

// 检查项目报价编辑权限
export function checkProjectQuotationEditPermission(id) {
  return request({
    url: `/quotation/project-quotation/${id}/check-edit-permission`,
    method: 'get'
  })
}