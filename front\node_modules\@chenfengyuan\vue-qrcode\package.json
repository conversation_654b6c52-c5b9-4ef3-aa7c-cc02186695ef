{"name": "@chenfengyuan/vue-qrcode", "version": "2.0.0", "description": "QR code component for Vue 3.", "main": "dist/vue-qrcode.js", "module": "dist/vue-qrcode.esm.js", "types": "dist/vue-qrcode.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c --environment BUILD:production", "build:docs": "webpack --env production", "build:types": "move-file dist/index.d.ts dist/vue-qrcode.d.ts", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "clean": "del-cli dist", "lint": "npm run lint:js && npm run lint:css", "lint:css": "stylelint **/*.{css,scss,vue} --fix", "lint:js": "eslint . --ext .js,.ts,.vue --fix", "prepare": "husky install", "release": "npm run clean && npm run lint && npm run build && npm run build:types && npm run build:docs && npm test && npm run changelog", "serve": "webpack serve --hot --open", "start": "npm run serve", "test": "jest", "test:coverage": "cat coverage/lcov.info | codecov"}, "repository": {"type": "git", "url": "https://github.com/fengyuanchen/vue-qrcode.git"}, "keywords": ["qrcode", "qr-code", "2d-bar-code", "vue", "vue3", "vue-component", "front-end", "web"], "author": "<PERSON> (https://chenfengyuan.com/)", "license": "MIT", "bugs": "https://github.com/fengyuanchen/vue-qrcode/issues", "homepage": "https://fengyuanchen.github.io/vue-qrcode", "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@commitlint/cli": "^16.1.0", "@commitlint/config-conventional": "^16.0.0", "@types/jest": "^27.4.0", "@types/qrcode": "^1.4.2", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "@vue/test-utils": "^2.0.0-rc.18", "babel-jest": "^27.5.0", "babel-loader": "^8.2.3", "canvas": "^2.9.0", "change-case": "^4.1.2", "codecov": "^3.8.3", "conventional-changelog-cli": "^2.2.2", "create-banner": "^2.0.0", "css-loader": "^6.6.0", "del-cli": "^4.0.1", "eslint": "^8.8.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-vue": "^8.4.1", "html-webpack-plugin": "^5.5.0", "husky": "^7.0.4", "jest": "^27.5.0", "lint-staged": "^12.3.3", "markdown-it-anchor": "^8.4.1", "markdown-to-vue-loader": "^3.1.3", "mini-css-extract-plugin": "^2.5.3", "move-file-cli": "^3.0.0", "qrcode": "^1.5.0", "rollup": "^2.67.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "sass": "^1.49.7", "sass-loader": "^12.4.0", "style-loader": "^3.3.1", "stylelint": "^14.3.0", "stylelint-config-recommended-scss": "^5.0.2", "stylelint-config-recommended-vue": "^1.1.0", "stylelint-order": "^5.0.0", "ts-jest": "^27.1.3", "ts-loader": "^9.2.6", "tslib": "^2.3.1", "typescript": "^4.5.5", "vue": "^3.2.29", "vue-loader": "^17.0.0", "webpack": "^5.68.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}, "peerDependencies": {"qrcode": "^1.5.0", "vue": "^3.0.0"}, "publishConfig": {"access": "public"}}