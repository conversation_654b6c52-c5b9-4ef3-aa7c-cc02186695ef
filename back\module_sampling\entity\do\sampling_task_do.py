from sqlalchemy import Column, BigInteger, Integer, String, Text, Date, DateTime, ForeignKey, Index, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingTask(Base):
    """采样任务表"""
    __tablename__ = 'sampling_task'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 基本信息
    task_name = Column(String(200), nullable=False, comment='任务名称')
    task_code = Column(String(100), unique=True, nullable=False, comment='任务编号')
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment='项目报价ID')
    description = Column(Text, comment='任务描述')
    
    # 分配信息
    responsible_user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='负责人用户ID')
    status = Column(Integer, default=0, comment='任务状态：0-待执行，1-执行中，2-已完成')
    is_urgent = Column(Boolean, default=False, comment='是否加急：0-否，1-是')
    
    # 时间信息
    planned_start_date = Column(Date, comment='计划开始日期')
    planned_end_date = Column(Date, comment='计划结束日期')
    actual_start_date = Column(Date, comment='实际开始日期')
    actual_end_date = Column(Date, comment='实际结束日期')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    project_quotation = relationship("ProjectQuotation", back_populates="sampling_tasks")
    responsible_user = relationship("SysUser", foreign_keys=[responsible_user_id])
    cycle_item_relations = relationship("SamplingTaskCycleItem", back_populates="sampling_task", cascade="all, delete-orphan")
    task_assignments = relationship("SamplingTaskAssignment", back_populates="sampling_task", cascade="all, delete-orphan")
    
    # 创建人和更新人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sampling_task_project_quotation_id', 'project_quotation_id'),
        Index('idx_sampling_task_responsible_user_id', 'responsible_user_id'),
        Index('idx_sampling_task_status', 'status'),
        Index('idx_sampling_task_task_code', 'task_code'),
        Index('idx_sampling_task_is_urgent', 'is_urgent'),
        {'comment': '采样任务表'}
    )
    
    def __repr__(self):
        return f"<SamplingTask(id={self.id}, task_name='{self.task_name}', task_code='{self.task_code}', status={self.status})>"
    
    @property
    def status_label(self):
        """状态标签"""
        status_map = {
            0: '待执行',
            1: '执行中',
            2: '已完成'
        }
        return status_map.get(self.status, '未知状态')
    
    # 注意：cycle_items 属性已移除，因为在异步环境中使用列表推导式访问关联对象会导致 MissingGreenlet 错误
    # 如需获取关联的检测周期条目，请在服务层中使用适当的查询方法
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'task_name': self.task_name,
            'task_code': self.task_code,
            'project_quotation_id': self.project_quotation_id,
            'description': self.description,
            'responsible_user_id': self.responsible_user_id,
            'status': self.status,
            'status_label': self.status_label,
            'is_urgent': self.is_urgent,
            'planned_start_date': self.planned_start_date.isoformat() if self.planned_start_date else None,
            'planned_end_date': self.planned_end_date.isoformat() if self.planned_end_date else None,
            'actual_start_date': self.actual_start_date.isoformat() if self.actual_start_date else None,
            'actual_end_date': self.actual_end_date.isoformat() if self.actual_end_date else None,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }