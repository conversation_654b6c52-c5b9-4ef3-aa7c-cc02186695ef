<template>
  <!-- 查看报价弹框 -->
  <el-dialog :title="'[编辑]项目报价 - ' + quotationForm.projectName" :model-value="dialogVisible" @update:model-value="dialogVisible = $event" width="80%" append-to-body @closed="handleClosed">
    <!-- 项目基本信息 -->
    <el-descriptions title="项目基本信息" :column="3" border>
      <el-descriptions-item label="项目名称">{{ quotationForm.projectName }}</el-descriptions-item>
      <el-descriptions-item label="项目编号">{{ quotationForm.projectCode }}</el-descriptions-item>
      <el-descriptions-item label="合同编号">{{ quotationForm.contractCode }}</el-descriptions-item>
      <el-descriptions-item label="服务类型">{{ quotationForm.serviceType }}</el-descriptions-item>
      <el-descriptions-item label="客户名称">{{ quotationForm.customerName }}</el-descriptions-item>
      <el-descriptions-item label="受检方企业名称">{{ quotationForm.inspectedParty }}</el-descriptions-item>
      <el-descriptions-item label="项目负责人">{{ quotationForm.projectManager }}</el-descriptions-item>
      <el-descriptions-item label="市场负责人">{{ quotationForm.marketManager }}</el-descriptions-item>
      <el-descriptions-item label="项目技术人">{{ quotationForm.technicalManager }}</el-descriptions-item>
      <el-descriptions-item label="委托日期">{{ quotationForm.commissionDate }}</el-descriptions-item>
      <el-descriptions-item label="客服">{{ getCustomerSupportNames(quotationForm.customerSupportList) }}</el-descriptions-item>
      <el-descriptions-item label="项目状态">{{ getStatusLabel(quotationForm.status) }}</el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <el-collapse v-model="activeNames">
      <!-- 检测项目费用明细 -->
      <el-collapse-item title="费用项1：检测项目费用明细" name="1">
        <!-- <div class="operation-bar" v-if="isEditable">
          <el-button type="primary" size="small" @click="addItem">添加检测项目</el-button>
          <el-button type="success" size="small" @click="handleSyncBasedataPrices" style="margin-left: 10px;">同步基础价目表</el-button>
        </div> -->

        <!-- 详细费用明细表格 -->
        <el-table :data="feeDetailTableData" style="width: 100%" border>
          <el-table-column label="序号" type="index" width="50" align="center"  fixed />
          <el-table-column label="样品类别" width="120" fixed>
            <template #default="scope">
              <el-tooltip :content="scope.row.category" placement="top" :disabled="!scope.row.category">
                <div class="cell-content">{{ scope.row.category || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="点位名称" width="120" fixed>
            <template #default="scope">
              <el-tooltip :content="scope.row.pointName" placement="top" :disabled="!scope.row.pointName">
                <div class="cell-content">{{ scope.row.pointName || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="参数" width="200"  fixed>
            <template #default="scope">
              <el-tooltip :content="scope.row.parameters" placement="top" :disabled="!scope.row.parameters">
                <div class="cell-content">{{ scope.row.parameters || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>          
          <el-table-column label="参数个数" width="80" align="center" fixed>
            <template #default="scope">
              <span>{{ scope.row.parameterCount || '0' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="方法" width="200" fixed>
            <template #default="scope">
              <el-tooltip :content="scope.row.method" placement="top" :disabled="!scope.row.method">
                <div class="cell-content">{{ scope.row.method || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="检测点位" width="80" align="center">
            <template #default="scope">
              <span>{{ scope.row.pointCount || '0' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测周期" width="80" align="center">
            <template #default="scope">
              <span>{{ scope.row.cycleCount || '0' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测频率" width="80" align="center">
            <template #default="scope">
              <span>{{ scope.row.frequency || '0' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="样品数" width="80" align="center">
            <template #default="scope">
              <span>{{ scope.row.sampleCount || '0' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采样单价" width="100" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.samplingUnitPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采样费用" width="100" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.samplingFee) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测首项单价" width="120" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.firstItemPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测增项单价" width="120" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.additionalItemPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测封顶单价" width="120" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.testingFeeLimit) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测单价" width="100" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.testingUnitPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="检测总费用" width="120" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.testingFee) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="前处理单价" width="120" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.pretreatmentUnitPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="前处理费" width="100" align="right">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.pretreatmentFee) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总价（元）" width="120" align="right">
            <template #default="scope">
              <span class="total-price">{{ formatCurrency(scope.row.itemTotalFee) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" width="150">
            <template #default="scope">
              <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
                <div class="cell-content">{{ scope.row.remark || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <!-- 编辑模式下的简化表格 -->
        <!-- <el-table :data="quotationForm.items" style="width: 100%" border v-if="isEditable">
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="服务类型" prop="serviceType" width="120">
            <template #default="scope">
              <el-select v-model="scope.row.serviceType" placeholder="请选择服务类型" style="width: 100%" :disabled="!isEditable">
                <el-option label="采样检测" value="采样检测" />
                <el-option label="送样检测" value="送样检测" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="检测类别" prop="category" width="120">
            <template #default="scope">
              <el-select
                v-model="scope.row.category"
                placeholder="请选择检测类别"
                style="width: 100%"
                @change="handleCategoryChange(scope.row)"
                :disabled="!isEditable"
              >
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.category"
                  :label="item.category"
                  :value="item.category"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="检测参数" prop="parameter" width="120">
            <template #default="scope">
              <el-select
                v-model="scope.row.parameter"
                placeholder="请选择检测参数"
                style="width: 100%"
                @change="handleParameterChange(scope.row)"
                :disabled="!isEditable"
              >
                <el-option
                  v-for="item in getLocalParameterOptions(scope.row.category)"
                  :key="item.parameter"
                  :label="item.parameter"
                  :value="item.parameter"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="检测方法" prop="method" width="120">
            <template #default="scope">
              <el-select
                v-model="scope.row.method"
                placeholder="请选择检测方法"
                style="width: 100%"
                @change="handleMethodChange(scope.row)"
                :disabled="!isEditable"
              >
                <el-option
                  v-for="item in getLocalMethodOptions(scope.row.category, scope.row.parameter)"
                  :key="item.method"
                  :label="item.method"
                  :value="item.method"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="点位名称" prop="pointName" width="120">
            <template #default="scope">
              <el-input v-model="scope.row.pointName" placeholder="请输入点位名称" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="点位数" prop="pointCount" width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.pointCount" :min="1" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="周期类型" prop="cycleType" width="100">
            <template #default="scope">
              <el-select v-model="scope.row.cycleType" placeholder="请选择" style="width: 100%" :disabled="!isEditable">
                <el-option label="日" value="日" />
                <el-option label="周" value="周" />
                <el-option label="月" value="月" />
                <el-option label="季" value="季" />
                <el-option label="年" value="年" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="周期数" prop="cycleCount" width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.cycleCount" :min="1" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="频次数" prop="frequency" width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.frequency" :min="1" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="样品数" prop="sampleCount" width="80">
            <template #default="scope">
              <el-input-number v-model="scope.row.sampleCount" :min="1" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="采样单价" prop="samplingPrice" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.samplingPrice" :precision="2" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="检测单价" prop="testingPrice" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.testingPrice" :precision="2" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="差旅费单价" prop="travelPrice" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.travelPrice" :precision="2" @change="calculateItemTotal(scope.row)" :disabled="!isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="总价" prop="totalPrice" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.totalPrice" :precision="2" disabled />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" v-if="isEditable">
            <template #default="scope">
              <el-button type="danger" icon="Delete" circle @click="removeItem(scope.$index)" />
            </template>
          </el-table-column>
        </el-table> -->
        <!-- 编辑模式下的简化汇总 -->
        <!-- <div v-if="isEditable">
          <div class="fee-summary">
            <span>检测项目费用合计：{{ getTestingFeeTotal() }}</span>
          </div>
          <div class="fee-summary">
            <span>折扣率(%)：</span>
            <el-input-number v-model="totalFee.discountRate" :min="0" :max="100" :precision="0" @change="calculateTotalFee" />
          </div>
          <div class="fee-summary">
            <span>折后费用合计：{{ totalFee.discountedTestingFee }}</span>
          </div>
        </div> -->

        <!-- 费用汇总 -->
        <div class="fee-summary-section">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="fee-summary-item">
                <span class="fee-label">采样费用合计：</span>
                <span class="fee-value">{{ formatCurrency(feeCalculationData.samplingFee) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="fee-summary-item">
                <span class="fee-label">检测费用合计：</span>
                <span class="fee-value">{{ formatCurrency(feeCalculationData.testingFee) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="fee-summary-item">
                <span class="fee-label">前处理费用合计：</span>
                <span class="fee-value">{{ formatCurrency(feeCalculationData.pretreatmentFee) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="fee-summary-item">
                <span class="fee-label">特殊耗材费用合计：</span>
                <span class="fee-value">{{ formatCurrency(feeCalculationData.specialConsumablesFee) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24">
              <div class="fee-summary-item total-fee">
                <span class="fee-label">检测项目费用合计：</span>
                <span class="fee-value total-amount">{{ formatCurrency(feeCalculationData.totalFee) }}</span>
              </div>
            </el-col>
            <el-col :span="24">
               <!-- 编辑模式下的简化汇总 -->
              <div class="fee-summary-item total-fee" v-if="isEditable">
                <span class="fee-label">检测项目总折扣率(%):</span>
                <span class="fee-value total-amount">
                  <el-input-number v-model="totalFee.discountRate" :min="0" :max="100" :precision="0"  @change="calculateTotalFee" />
                </span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-collapse-item>

      <!-- 项目其他费用表 -->
      <el-collapse-item title="费用项2：项目其他费用表" name="2">
        <div class="operation-bar" v-if="isEditable">
          <el-button type="primary" size="small" @click="addOtherFee">添加其他费用</el-button>
        </div>

        <!-- 查看模式下的其他费用表格 -->
        <el-table :data="quotationForm.otherFees" style="width: 100%" border v-if="!isEditable">
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="费用名称" width="150">
            <template #default="scope">
              <el-tooltip :content="scope.row.feeName" placement="top" :disabled="!scope.row.feeName">
                <div class="cell-content">{{ scope.row.feeName || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="数量" width="100">
            <template #default="scope">
              <span>{{ scope.row.quantity || '0' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="单价" width="100">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.unitPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总价" width="100">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.totalPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template #default="scope">
              <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
                <div class="cell-content">{{ scope.row.remark || '-' }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <!-- 编辑模式下的其他费用表格 -->
        <el-table :data="quotationForm.otherFees" style="width: 100%" border v-if="isEditable">
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="费用名称" prop="feeName">
            <template #default="scope">
              <el-input v-model="scope.row.feeName" placeholder="如差旅费" :disabled="!scope.row.editing && !isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="quantity" >
            <template #default="scope">
              <el-input-number v-model="scope.row.quantity" :min="1"
              controls-position="right" size="small" @change="calculateOtherFeeTotal(scope.row)" :disabled="!scope.row.editing && !isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="单价" prop="unitPrice">
            <template #default="scope">
              <el-input-number v-model="scope.row.unitPrice"
              controls-position="right" size="small" :precision="2" @change="calculateOtherFeeTotal(scope.row)" :disabled="!scope.row.editing && !isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="总价" prop="totalPrice">
            <template #default="scope">
              <span>{{ formatCurrency(scope.row.totalPrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark">
            <template #default="scope">
              <el-input v-model="scope.row.remark" placeholder="请输入备注" :disabled="!scope.row.editing && !isEditable" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140">
            <template #default="scope">
              <el-button v-if="!scope.row.editing && (isEditable || scope.row.id)" type="primary" size="small" @click="editOtherFee(scope.row)">编辑</el-button>
              <el-button v-if="scope.row.editing" type="success" size="small" @click="saveOtherFee(scope.row, scope.$index)">保存</el-button>
              <el-button v-if="isEditable || scope.row.editing" type="danger" size="small" @click="removeOtherFee(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="fee-summary">
          <span>其他费用合计：{{ getOtherFeeTotal() }}</span>
        </div>
      </el-collapse-item>

      <!-- 项目总费用表 -->
      <el-collapse-item title="费用项3：项目总费用表" name="3">
        <el-form :model="totalFee" label-width="180px">
          <el-form-item label="项目编号">
            <el-input v-model="quotationForm.projectCode" disabled />
          </el-form-item>
          <el-form-item label="检测项目总折扣率">
            <!-- <el-input :value="totalFee.discountRate + '%'" disabled /> -->
            <el-input-number v-model="totalFee.discountRate" :min="0" :max="100" :precision="0" :disabled="!isEditable"  @change="calculateTotalFee" />
          </el-form-item>
          <el-form-item label="检测折后费用">
            <el-input v-model="totalFee.discountedTestingFee" disabled />
          </el-form-item>
          <el-form-item label="其他费用">
            <el-input v-model="totalFee.otherFee" disabled />
          </el-form-item>
          <el-form-item label="优惠前总费用">
            <el-input v-model="totalFee.totalFeeBeforeDiscount" disabled />
          </el-form-item>
          <el-form-item label="税率(%)">
            <el-input-number v-model="totalFee.taxRate" :min="0" :max="100" :precision="0" :disabled="!isEditable" @change="calculateTotalFee" />
          </el-form-item>
          <el-form-item label="税费">
            <el-input v-model="totalFee.tax" disabled />
          </el-form-item>
          <el-form-item label="优惠前总费用(税后)">
            <el-input v-model="totalFee.totalFeeAfterTax" disabled />
          </el-form-item>
          <el-form-item label="整体优惠金额">
            <el-input-number v-model="totalFee.adjustmentAmount" :precision="2" :disabled="!isEditable" @change="calculateTotalFee" />
          </el-form-item>
          <el-form-item label="优惠后总金额">
            <el-input v-model="totalFee.finalAmount" disabled />
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">关 闭</el-button>
        <el-button v-if="isEditable" type="primary" @click="saveQuotation">保存且更新报价</el-button>
        <el-button v-if="!isEditable" type="primary" @click="isEditable = true">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotationFee, updateSingleOtherFee, getProjectQuotationFeeCalculation, syncBasedataPrices, getProjectQuotationCustomerSupports, checkProjectQuotationEditPermission } from "@/api/quotation/projectQuotation"
import { getCategoryOptions, getParameterOptions, getMethodOptions } from "@/api/basedata/technicalManual"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置数据和编辑状态
    resetData()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 重置所有数据
function resetData() {
  // 重置编辑状态
  isEditable.value = false

  // 重置报价表单对象
  quotationForm.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    contractCode: '',
    serviceType: '',
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    // customerSupports字段已废弃，使用customerSupportList
    customerSupportIds: [], // 客服ID列表
    status: '',
    remark: '',
    items: [],
    otherFees: [],
    attachments: []
  }

  // 重置总费用
  totalFee.value = {
    discountRate: 0, // 默认折扣率0%
    discountedTestingFee: 0,
    otherFee: 0,
    totalFeeBeforeDiscount: 0,
    taxRate: 0, // 默认税率0%
    tax: 0,
    totalFeeAfterTax: 0,
    adjustmentAmount: 0,
    finalAmount: 0
  }
}

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 是否可编辑
const isEditable = ref(false)
// 折叠面板激活的项
const activeNames = ref(['1', '2', '3'])
// 报价表单对象
const quotationForm = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  customerSupportIds: [], // 客服ID列表
  items: [],
  otherFees: []
})
// 总费用
const totalFee = ref({
  discountRate: 95, // 折扣率95%
  discountedTestingFee: 0,
  otherFee: 0,
  totalFeeBeforeDiscount: 0,
  taxRate: 6, // 税率6%
  tax: 0,
  totalFeeAfterTax: 0,
  adjustmentAmount: 0,
  finalAmount: 0
})

// 检测类别选项
const categoryOptions = ref([])
// 检测参数选项映射
const parameterOptionsMap = ref({})
// 检测方法选项映射
const methodOptionsMap = ref({})

// 获取客服名称
function getCustomerSupportNames(customerSupportList) {
  if (!customerSupportList || !Array.isArray(customerSupportList)) {
    return ''
  }
  return customerSupportList.map(customerSupport => customerSupport.nickName || customerSupport.userName || '未知').join('、')
}

// 费用计算详情数据
const feeCalculationData = ref({
  samplingFee: 0,
  testingFee: 0,
  pretreatmentFee: 0,
  specialConsumablesFee: 0,
  totalFee: 0,
  calculationDetails: {
    details: [],
    summary: {}
  }
})

// 费用明细表格数据
const feeDetailTableData = ref([])

// 初始化
onMounted(() => {
  // 获取检测类别选项
  getCategoryOptions().then(response => {
    categoryOptions.value = response.data
  })
})

// 添加其他费用
function addOtherFee() {
  quotationForm.value.otherFees.push({
    feeName: '',
    quantity: 1,
    unitPrice: 0,
    totalPrice: 0,
    remark: '',
    editing: true // 新添加的行默认处于编辑状态
  })
}

// 移除其他费用
function removeOtherFee(index) {
  quotationForm.value.otherFees.splice(index, 1)
  calculateTotalFee()
}

// 编辑其他费用
function editOtherFee(row) {
  // 保存原始数据，以便取消编辑时恢复
  row._originalData = {
    feeName: row.feeName,
    quantity: row.quantity,
    unitPrice: row.unitPrice,
    remark: row.remark
  }
  row.editing = true
}

// 保存单行其他费用
function saveOtherFee(row, index) {
  // 验证数据
  if (!row.feeName) {
    ElMessage.warning('费用名称不能为空')
    return
  }

  // 计算总价
  calculateOtherFeeTotal(row)

  // 如果是已有数据，调用API保存
  if (row.id) {
    const saveData = {
      id: quotationForm.value.id,
      otherFeeId: row.id,
      otherFee: {
        id: row.id,
        feeName: row.feeName,
        quantity: row.quantity,
        unitPrice: row.unitPrice,
        totalPrice: row.totalPrice,
        remark: row.remark
      }
    }

    updateSingleOtherFee(saveData).then(() => {
      ElMessage.success('保存成功')
      row.editing = false
      delete row._originalData
      calculateTotalFee()
    }).catch(error => {
      console.error('保存失败:', error)
      // 恢复原始数据
      if (row._originalData) {
        row.feeName = row._originalData.feeName
        row.quantity = row._originalData.quantity
        row.unitPrice = row._originalData.unitPrice
        row.remark = row._originalData.remark
        calculateOtherFeeTotal(row)
      }
    })
  } else {
    // 新添加的行，直接标记为非编辑状态
    row.editing = false
    delete row._originalData
  }
}

// 计算其他费用总价
function calculateOtherFeeTotal(row) {
  row.totalPrice = (row.quantity || 1) * (row.unitPrice || 0)
  calculateTotalFee()
}

// 获取检测项目费用合计
function getTestingFeeTotal() {
  return totalFee.value.itemTestingFees
}

// 获取其他费用合计
function getOtherFeeTotal() {
  let total = 0
  quotationForm.value.otherFees.forEach(fee => {
    total += fee.totalPrice || 0
  })
  return total.toFixed(2)
}

// 获取状态标签
function getStatusLabel(status) {
  const statusMap = {
    '0': '草稿',
    '1': '待审核',
    '2': '审核完成',
    '3': '已撤回',
    '4': '已拒绝',
    '1-market': '待审核（市场）',
    '1-order_confirm': '待审核（项目成单确认）',
    '1-lab': '待审核（实验室）',
    '1-field': '待审核（现场）',
    '1-lab|field': '待审核（实验室|现场）'
  }
  return statusMap[status] || '未知'
}

// 格式化货币
function formatCurrency(value) {
  if (value === null || value === undefined) return '0.00';
  return parseFloat(value).toFixed(2);
}

// 同步基础价目表
function handleSyncBasedataPrices() {
  if (!quotationForm.value.id) {
    ElMessage.warning('请先保存项目报价')
    return
  }

  ElMessage.info('正在同步基础价目表...')
  syncBasedataPrices(quotationForm.value.id).then(() => {
    ElMessage.success('同步成功')
    // 重新获取费用计算详情
    getFeeCalculationDetail(quotationForm.value.id)
  }).catch(error => {
    console.error('同步失败:', error)
    ElMessage.error('同步失败，请重试')
  })
}

// 计算总费用
function calculateTotalFee() {
  // 检测项目折后费用
  console.log("totalFee before calculate", getTestingFeeTotal())
  totalFee.value.discountedTestingFee = (getTestingFeeTotal() * totalFee.value.discountRate / 100).toFixed(2)
  // 计算其他费用总额
  let otherFeeTotal = 0
  quotationForm.value.otherFees.forEach(fee => {
    otherFeeTotal += fee.totalPrice || 0
  })

  // 其他费用
  totalFee.value.otherFee = otherFeeTotal.toFixed(2)

  // 优惠前总费用
  totalFee.value.totalFeeBeforeDiscount = (parseFloat(totalFee.value.discountedTestingFee) + parseFloat(totalFee.value.otherFee)).toFixed(2)

  // 税费
  totalFee.value.tax = (totalFee.value.totalFeeBeforeDiscount * totalFee.value.taxRate / 100).toFixed(2)

  // 优惠前总费用(税后)
  totalFee.value.totalFeeAfterTax = (parseFloat(totalFee.value.totalFeeBeforeDiscount) + parseFloat(totalFee.value.tax)).toFixed(2)

  // 优惠后总金额
  totalFee.value.finalAmount = (parseFloat(totalFee.value.totalFeeAfterTax) + parseFloat(totalFee.value.adjustmentAmount || 0)).toFixed(2)
  console.log("totalFee detail", totalFee.value)
}

// 获取本地检测参数选项
function getLocalParameterOptions(category) {
  return parameterOptionsMap.value[category] || []
}

// 获取本地检测方法选项
function getLocalMethodOptions(category, parameter) {
  const key = `${category}-${parameter}`
  return methodOptionsMap.value[key] || []
}

// 添加检测项目
function addItem() {
  quotationForm.value.items.push({
    serviceType: '送样检测', // 默认值
    category: '',
    parameter: '',
    method: '',
    testCode: '',
    priceCode: '',
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1,
    samplingPrice: 0,
    testingPrice: 0,
    travelPrice: 0,
    totalPrice: 0,
    remark: ''
  })
}

// 移除检测项目
function removeItem(index) {
  quotationForm.value.items.splice(index, 1)
  calculateTotalFee()
}

// 处理检测类别变化
function handleCategoryChange(row) {
  row.parameter = ''
  row.method = ''
  row.testCode = ''
  row.priceCode = ''

  // 获取检测参数选项
  getParameterOptions({ category: row.category }).then(response => {
    parameterOptionsMap.value[row.category] = response.data
  })
}

// 处理检测参数变化
function handleParameterChange(row) {
  row.method = ''
  row.testCode = ''
  row.priceCode = ''

  // 获取检测方法选项
  getMethodOptions({ category: row.category, parameter: row.parameter }).then(response => {
    const key = `${row.category}-${row.parameter}`
    methodOptionsMap.value[key] = response.data
  })
}

// 处理检测方法变化
function handleMethodChange(row) {
  // 检查参数是否有效
  if (!row.category || !row.parameter || !row.method) {
    ElMessage.warning('检测类别、参数和方法不能为空')
    return
  }
}

// 计算项目明细总价
function calculateItemTotal(row) {
  // 确保所有数值都是数字类型
  const pointCount = Number(row.pointCount) || 1
  const cycleCount = Number(row.cycleCount) || 1
  const frequency = Number(row.frequency) || 1
  const sampleCount = Number(row.sampleCount) || 1

  const samplingPrice = Number(row.samplingPrice) || 0
  const testingPrice = Number(row.testingPrice) || 0
  const travelPrice = Number(row.travelPrice) || 0

  // 计算总价并保留两位小数
  const total = (samplingPrice + testingPrice + travelPrice) * pointCount * cycleCount * frequency * sampleCount
  row.totalPrice = parseFloat(total.toFixed(2))

  // 更新回原始对象
  row.pointCount = pointCount
  row.cycleCount = cycleCount
  row.frequency = frequency
  row.sampleCount = sampleCount
  row.samplingPrice = samplingPrice
  row.testingPrice = testingPrice
  row.travelPrice = travelPrice

  // 计算总金额
  calculateTotalFee()
}

// 保存报价
function saveQuotation() {
  calculateTotalFee()
  // 构建保存对象
  console.log("totalFee before save", totalFee.value)

  const saveData = {
    id: quotationForm.value.id,
    projectName: quotationForm.value.projectName,
    projectCode: quotationForm.value.projectCode,
    items: quotationForm.value.items,
    otherFees: quotationForm.value.otherFees,
    totalFee: {
      ...totalFee.value,
      // 不传递 discountedTestingFee，让后端计算
      discountedTestingFee: undefined
    }
  }

  // 调用保存接口
  updateProjectQuotationFee(saveData).then(() => {
    ElMessage.success('保存成功')
    isEditable.value = false

    // 重新获取项目报价详情，以获取后端计算的最新数据
    getProjectQuotation(quotationForm.value.id).then(response => {
      quotationForm.value = response.data

      // 确保customerSupportIds字段存在
      if (!quotationForm.value.customerSupportIds) {
        quotationForm.value.customerSupportIds = []
      }

      // 如果没有其他费用数据，初始化一个空数组
      if (!quotationForm.value.otherFees) {
        quotationForm.value.otherFees = []
      }

      // 如果有总费用数据，则使用后端数据
      if (quotationForm.value.totalFee) {
        totalFee.value = quotationForm.value.totalFee
      } else {
        // 否则计算总费用
        calculateTotalFee()
      }

      // 通知父组件刷新数据
      emit('refresh')
    })
  }).catch(error => {
    console.error('保存失败:', error)
  })
}

// 获取项目报价详情
function getQuotationDetail(id) {
  // 获取基本报价信息
  getProjectQuotation(id).then(response => {
    quotationForm.value = response.data

    // 确保customerSupportIds字段存在
    if (!quotationForm.value.customerSupportIds) {
      quotationForm.value.customerSupportIds = []
    }

    // 如果没有其他费用数据，初始化一个空数组
    if (!quotationForm.value.otherFees) {
      quotationForm.value.otherFees = []
    }

    // 如果有总费用数据，则使用后端数据
    if (quotationForm.value.totalFee) {
      totalFee.value = quotationForm.value.totalFee
    } else {
      // 否则计算总费用
      calculateTotalFee()
    }
    
    // 检查编辑权限
    checkEditPermission(id)
  })

  // 审核人信息已通过approverList字段获取，无需单独调用

  // 获取费用计算详情
  getFeeCalculationDetail(id)
}

// 获取审核人详细信息（已废弃，使用approverList字段）
// function getApproverDetails(id) {
//   getProjectQuotationApprovers(id).then(response => {
//     if (response.data && response.data.length > 0) {
//       // 提取审核人昵称并用逗号连接
//       const names = response.data.map(approver => approver.nick_name || approver.user_name).join('、')
//       approverNames.value = names
//     } else {
//       approverNames.value = '暂无审核人'
//     }
//   }).catch(error => {
//     console.error('获取审核人信息失败:', error)
//     approverNames.value = '获取失败'
//   })
// }

// 获取费用计算详情
function getFeeCalculationDetail(id) {
  getProjectQuotationFeeCalculation(id).then(response => {
    feeCalculationData.value = response.data

    // 处理费用明细表格数据
    if (response.data.calculationDetails && response.data.calculationDetails.details) {
      feeDetailTableData.value = processFeeDetailData(response.data.calculationDetails.details)
    }
  }).catch(error => {
    console.error('获取费用计算详情失败:', error)
    // 如果获取费用计算失败，使用空数据
    feeCalculationData.value = {
      samplingFee: 0,
      testingFee: 0,
      pretreatmentFee: 0,
      specialConsumablesFee: 0,
      totalFee: 0,
      calculationDetails: { details: [], summary: {} }
    }
    feeDetailTableData.value = []
  })
}

// 处理费用明细数据
function processFeeDetailData(details) {
  // 转换为数组并处理参数显示
  return Object.values(details).map(group => ({
    ...group,
    parameters: group.parameters.join('、'),
    parameterCount: group.parameters.length
  }))
}

// 检查编辑权限
async function checkEditPermission(id) {
  try {
    const response = await checkProjectQuotationEditPermission(id)
    if (response.code === 200) {
      isEditable.value = response.data.canEdit
    } else {
      isEditable.value = false
    }
  } catch (error) {
    console.warn('检查编辑权限失败:', error)
    isEditable.value = false
  }
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置所有数据
  resetData()
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}

.fee-summary-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.fee-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.fee-summary-item.total-fee {
  border-top: 1px solid #e4e7ed;
  padding-top: 10px;
  margin-top: 10px;
}

.fee-label {
  font-weight: 600;
  color: #303133;
}

.fee-value {
  font-weight: 600;
  color: #409eff;
}

.total-amount {
  font-size: 18px;
  color: #f56c6c;
}

.total-price {
  font-weight: bold;
  color: #f56c6c;
}

.cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.operation-bar {
  margin-bottom: 10px;
}

.negative-value {
  color: #f56c6c;
}

.final-amount {
  font-size: 18px;
  font-weight: bold;
  color: #67c23a;
}
</style>
