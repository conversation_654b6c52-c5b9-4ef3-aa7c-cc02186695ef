<template>
  <div>
    <el-form :model="upload" label-width="80px">
      <el-form-item label="选择文件">
        <el-upload
          ref="uploadRef"
          :limit="1"
          accept=".xlsx,.xls"
          :disabled="upload.isUploading"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip text-center">
              <!-- <div class="el-upload__tip">
                <el-checkbox v-model="upload.updateSupport" />
                是否更新已经存在的设备数据
              </div> -->
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <!-- 导入结果 -->
    <div v-if="importResult" style="margin-top: 20px;">
      <el-alert
        :title="`导入完成：成功 ${importResult.successCount} 条，失败 ${importResult.errorCount} 条`"
        :type="importResult.errorCount > 0 ? 'warning' : 'success'"
        show-icon
        :closable="false"
      />
      
      <!-- 错误信息 -->
      <div v-if="importResult.errors && importResult.errors.length > 0" style="margin-top: 10px;">
        <el-table :data="importResult.errors" style="width: 100%" max-height="300">
          <el-table-column prop="row" label="行号" width="80" />
          <el-table-column prop="field" label="字段" width="150" />
          <el-table-column prop="message" label="错误信息" />
        </el-table>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div slot="footer" class="dialog-footer" style="text-align: center; margin-top: 20px;">
      <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"
import { importEquipmentManagement, downloadEquipmentManagementTemplate } from "@/api/basedata/equipmentManagement"

const { proxy } = getCurrentInstance()
const emit = defineEmits(['success', 'close'])

const upload = reactive({
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的设备数据
  updateSupport: 0
})

const importResult = ref(null)

/** 下载模板操作 */
function importTemplate() {
  downloadEquipmentManagementTemplate().then(response => {
    // 创建blob对象
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '设备管理_导入模板.xlsx'
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
  }).catch(error => {
    console.error('下载模板失败:', error)
    proxy.$modal.msgError("下载模板失败")
  })
}



/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
  const fileList = proxy.$refs["uploadRef"].uploadFiles
  if (!fileList || fileList.length === 0) {
    proxy.$modal.msgError("请选择要上传的文件")
    return
  }

  const file = fileList[0].raw
  upload.isUploading = true

  // 使用API方式上传
  importEquipmentManagement(file).then(response => {
    upload.isUploading = false
    if (response.code === 200) {
      importResult.value = response.data
      if (response.data.errorCount === 0) {
        proxy.$modal.msgSuccess("导入成功")
        emit('success')
      } else {
        proxy.$modal.msgWarning(`导入完成，成功 ${response.data.successCount} 条，失败 ${response.data.errorCount} 条`)
      }
    } else {
      proxy.$modal.msgError(response.msg || "导入失败")
    }
    proxy.$refs["uploadRef"].clearFiles()
  }).catch(error => {
    upload.isUploading = false
    console.error('导入失败:', error)
    proxy.$modal.msgError("导入失败，请重试")
    proxy.$refs["uploadRef"].clearFiles()
  })
}

/** 关闭对话框 */
function close() {
  emit('close')
}
</script>

<style scoped>
.el-upload__tip {
  line-height: 1.5;
}
</style>
