"""
测试Excel模板导出功能
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.excel_template_engine import ExcelTemplateEngine


def test_template_export():
    """测试模板导出功能"""
    
    # 创建模板引擎
    template_engine = ExcelTemplateEngine()
    
    # 准备测试数据
    template_data = {
        # 基本信息
        "project_name": "测试项目",
        "project_code": "TEST001",
        "customer_name": "测试客户",
        "inspected_party": "被检方",
        "project_manager": "项目经理",
        "market_manager": "市场经理",
        "technical_manager": "技术经理",
        "commission_date": "2024-01-01",
        "contract_code": "CONTRACT001",
        "business_type": "环境检测",
        
        # 检测项目明细
        "items": [
            {
                "category": "水质",
                "parameter": "pH值",
                "method": "玻璃电极法",
                "point_name": "采样点1",
                "point_count": 1,
                "cycle_count": 1,
                "frequency": 1,
                "sample_count": 1,
                "sampling_price": 50.0,
                "sampling_fee": 50.0,
                "first_item_price": 100.0,
                "additional_item_price": 80.0,
                "testing_fee_limit": 500.0,
                "testing_fee": 100.0,
                "pretreatment_fee": 20.0,
                "remark": "测试备注"
            },
            {
                "category": "大气",
                "parameter": "PM2.5",
                "method": "重量法",
                "point_name": "采样点2",
                "point_count": 2,
                "cycle_count": 1,
                "frequency": 1,
                "sample_count": 2,
                "sampling_price": 80.0,
                "sampling_fee": 160.0,
                "first_item_price": 150.0,
                "additional_item_price": 120.0,
                "testing_fee_limit": 800.0,
                "testing_fee": 270.0,
                "pretreatment_fee": 40.0,
                "remark": ""
            }
        ],
        
        # 其他费用
        "other_fees": [
            {
                "fee_name": "差旅费",
                "total_price": 500.0,
                "remark": "往返交通费"
            },
            {
                "fee_name": "住宿费",
                "total_price": 300.0,
                "remark": "现场检测住宿"
            }
        ],
        
        # 费用汇总
        "total_sampling_fee": "210.00",
        "total_testing_fee": "370.00",
        "total_pretreatment_fee": "60.00",
        "special_consumables_fee": "0.00",
        "other_fees_amount": "800.00",
        "subtotal_amount": "1440.00",
        "discount_rate": "10.0",
        "discounted_amount": "1296.00",
        "tax_rate": "13.0",
        "tax_amount": "168.48",
        "adjustment_amount": "0.00",
        "final_amount": "1464.48",
    }
    
    try:
        # 渲染模板
        output = template_engine.render_template(template_data)
        
        # 保存测试文件
        test_file_path = "test_quotation_export.xlsx"
        with open(test_file_path, "wb") as f:
            f.write(output.getvalue())
        
        print(f"✅ 模板导出测试成功！文件已保存到: {test_file_path}")
        print(f"📊 文件大小: {len(output.getvalue())} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板导出测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始测试Excel模板导出功能...")
    success = test_template_export()
    
    if success:
        print("🎉 所有测试通过！")
    else:
        print("💥 测试失败，请检查错误信息")
