"""
Excel模板引擎
支持基于现有Excel模板文件的数据渲染
"""

import io
import os
import re
from typing import Dict, Any, List, Optional
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
from jinja2 import Template
import logging

logger = logging.getLogger(__name__)


class ExcelTemplateEngine:
    """Excel模板引擎"""

    def __init__(self, template_path: str = None):
        self.workbook = None
        self.worksheet = None
        # 获取当前文件的目录，然后构建模板路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        default_template_path = os.path.join(
            os.path.dirname(current_dir), "templates", "quotation_price_export_tpl.xlsx"
        )
        self.template_path = template_path or default_template_path

    def create_quotation_template(self) -> Workbook:
        """
        创建报价单模板

        :return: Excel工作簿对象
        """
        wb = Workbook()
        ws = wb.active
        ws.title = "项目报价单"

        # 设置列宽
        column_widths = {
            "A": 6,
            "B": 12,
            "C": 15,
            "D": 20,
            "E": 12,
            "F": 8,
            "G": 8,
            "H": 8,
            "I": 8,
            "J": 10,
            "K": 10,
            "L": 10,
        }
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width

        # 定义样式
        title_font = Font(name="宋体", size=20, bold=True)
        subtitle_font = Font(name="宋体", size=16, bold=True)
        header_font = Font(name="宋体", size=12, bold=True)
        normal_font = Font(name="宋体", size=10)

        center_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        left_alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
        right_alignment = Alignment(horizontal="right", vertical="center")

        thin_border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )

        # 创建模板内容
        current_row = 1

        # 1. 公司标题
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        company_title = ws[f"A{current_row}"]
        company_title.value = "北京东西分析股份有限公司"
        company_title.font = title_font
        company_title.alignment = center_alignment
        current_row += 2

        # 2. 报价单标题
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        quotation_title = ws[f"A{current_row}"]
        quotation_title.value = "项目报价单"
        quotation_title.font = subtitle_font
        quotation_title.alignment = center_alignment
        current_row += 2

        # 3. 项目基本信息表格
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        info_header = ws[f"A{current_row}"]
        info_header.value = "项目基本信息"
        info_header.font = header_font
        info_header.alignment = center_alignment
        info_header.border = thin_border
        current_row += 1

        # 基本信息模板行
        info_template_rows = [
            ["委托方", "{{customer_name}}", "受检方", "{{inspected_party}}"],
            ["地址", "{{customer_address}}", "地址", "{{customer_address}}"],
            ["联系人", "{{customer_contact}}", "联系人", "{{inspected_contact}}"],
            ["联系电话", "{{customer_phone}}", "联系电话", "{{inspected_phone}}"],
            ["项目名称", "{{project_name}}", "项目编号", "{{inspected_address}}"],
        ]

        for row_data in info_template_rows:
            self._create_info_row(
                ws, current_row, row_data, header_font, normal_font, center_alignment, left_alignment, thin_border
            )
            current_row += 1

        current_row += 1

        # 4. 检测项目明细表
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        detail_title = ws[f"A{current_row}"]
        detail_title.value = "检测项目明细"
        detail_title.font = header_font
        detail_title.alignment = center_alignment
        detail_title.border = thin_border
        current_row += 1

        # 明细表头
        detail_headers = [
            ("A", "样品类别"),
            ("B", "点位名称"),
            ("C", "检测项目"),
            # ("C", "检测方法"),
            ("D", "点位数"),
            ("E", "周期"),
            ("F", "频次"),
            ("G", "样品数"),
            ("H", "采样单价"),
            ("I", "检测首项价"),
            ("J", "前处理费"),
            ("K", "总价"),
            ("L", "备注"),  # （若样品来源为送样，备注处自动带入：送样，采样费用未计算）
        ]

        for col_letter, header_text in detail_headers:
            cell = ws[f"{col_letter}{current_row}"]
            cell.value = header_text
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
        current_row += 1

        # 明细数据模板行（标记行，用于循环）
        ws.cell(row=current_row, column=2).value = "$样品类别"  # 标记需要循环的行
        current_row += 1

        current_row += 1

        # 5. 其他费用表
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        other_fee_title = ws[f"A{current_row}"]
        other_fee_title.value = "其他费用明细"
        other_fee_title.font = header_font
        other_fee_title.alignment = center_alignment
        other_fee_title.border = thin_border
        current_row += 1

        # 其他费用表头
        other_fee_headers = ["序号", "费用名称", "费用金额", "备注"]
        for col_idx, header in enumerate(other_fee_headers, 1):
            cell = ws.cell(row=current_row, column=col_idx)
            cell.value = header
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
        current_row += 1

        # 其他费用数据模板行（标记行，用于循环）
        ws.cell(row=current_row, column=2).value = "$差旅费"  # 标记需要循环的行
        current_row += 1

        current_row += 1

        # 6. 费用汇总表
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        summary_title = ws[f"A{current_row}"]
        summary_title.value = "费用汇总"
        summary_title.font = header_font
        summary_title.alignment = center_alignment
        summary_title.border = thin_border
        current_row += 1

        # 费用汇总模板行
        summary_template_rows = [
            ["采样费用", "{{total_sampling_fee}}", "检测费用", "{{total_testing_fee}}"],
            ["前处理费用", "{{total_pretreatment_fee}}", "特殊耗材费", "{{special_consumables_fee}}"],
            ["其他费用", "{{other_fees}}", "检测项目小计", "{{subtotal_amount}}"],
            ["折扣率", "{{discount_rate}}%", "折后金额", "{{discounted_amount}}"],
            ["税率", "{{tax_rate}}%", "税额", "{{tax_amount}}"],
            ["整体调整", "{{adjustment_amount}}", "最终总价", "{{final_amount}}"],
        ]

        for row_data in summary_template_rows:
            self._create_info_row(
                ws, current_row, row_data, header_font, normal_font, center_alignment, right_alignment, thin_border
            )
            current_row += 1

        current_row += 2

        # 7. 签字区域
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        signature_title = ws[f"A{current_row}"]
        signature_title.value = "审批签字"
        signature_title.font = header_font
        signature_title.alignment = center_alignment
        signature_title.border = thin_border
        current_row += 1

        # 签字表格
        signature_headers = ["制表人", "审核人", "批准人"]
        for col_idx, header in enumerate(signature_headers):
            start_col = 1 + col_idx * 5
            end_col = min(start_col + 4, 17)
            if start_col <= 17:
                ws.merge_cells(f"{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}")
                cell = ws.cell(row=current_row, column=start_col)
                cell.value = f"{header}："
                cell.font = header_font
                cell.alignment = left_alignment
                cell.border = thin_border
        current_row += 1

        # 日期行
        for col_idx in range(3):
            start_col = 1 + col_idx * 5
            end_col = min(start_col + 4, 17)
            if start_col <= 17:
                ws.merge_cells(f"{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}")
                cell = ws.cell(row=current_row, column=start_col)
                cell.value = "日期："
                cell.font = header_font
                cell.alignment = left_alignment
                cell.border = thin_border

        return wb

    def _create_info_row(
        self, ws, current_row, row_data, header_font, normal_font, center_alignment, value_alignment, thin_border
    ):
        """创建信息行"""
        # 标签1
        ws.merge_cells(f"A{current_row}:B{current_row}")
        cell_a = ws.cell(row=current_row, column=2)
        cell_a.value = row_data[0]
        cell_a.font = header_font
        cell_a.alignment = center_alignment
        cell_a.border = thin_border

        # 值1 (合并C-F列)
        ws.merge_cells(f"C{current_row}:F{current_row}")
        cell_b = ws.cell(row=current_row, column=4)
        cell_b.value = row_data[1]
        cell_b.font = normal_font
        cell_b.alignment = value_alignment
        cell_b.border = thin_border

        # 标签2(G列) todo: 项目地省市（合并G-H列)、报价日期(K列)
        cell_i = ws.cell(row=current_row, column=1)
        cell_i.value = row_data[2]
        cell_i.font = header_font
        cell_i.alignment = center_alignment
        cell_i.border = thin_border

        # 值2 (合并H-L列) todo: 项目地省市（合并I-J列)、报价日期(L列)
        ws.merge_cells(f"H{current_row}:H{current_row}")
        cell_j = ws.cell(row=current_row, column=5)
        cell_j.value = row_data[3]
        cell_b.font = normal_font
        cell_b.alignment = value_alignment
        cell_b.border = thin_border

    def render_template(self, template_data: Dict[str, Any]) -> io.BytesIO:
        """
        渲染模板

        :param template_data: 模板数据
        :return: 渲染后的Excel文件字节流
        """
        # 加载现有模板文件
        if not os.path.exists(self.template_path):
            raise FileNotFoundError(f"模板文件不存在: {self.template_path}")

        wb = load_workbook(self.template_path)
        ws = wb.active

        # 渲染基本信息
        self._render_basic_info(ws, template_data)

        # 渲染检测项目明细
        self._render_detail_items(ws, template_data.get("items", []))

        # 渲染其他费用
        self._render_other_fees(ws, template_data.get("other_fees", []))

        # 渲染费用汇总
        self._render_fee_summary(ws, template_data.get("total_fee", {}))

        # 保存到字节流
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return output

    def _render_basic_info(self, ws, template_data: Dict[str, Any]):
        """渲染基本信息"""
        # 查找并替换基本信息模板变量
        for row in ws.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 使用Jinja2渲染模板变量
                    if "{{" in cell.value and "}}" in cell.value:
                        try:
                            template = Template(cell.value)
                            cell.value = template.render(**template_data)
                        except Exception as e:
                            logger.warning(f"模板渲染失败: {cell.value}, 错误: {e}")
                            # 如果渲染失败，保持原值或设置为空
                            cell.value = ""
                    # 处理简单的变量替换（如果模板中使用了简单的变量名）
                    elif cell.value in template_data:
                        cell.value = template_data[cell.value]

    def _render_detail_items(self, ws, items: List[Dict[str, Any]]):
        """渲染检测项目明细"""
        # 查找$样品类别标记行
        marker_row = None
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value == "$样品类别":
                    marker_row = row_idx
                    break
            if marker_row:
                break

        if not marker_row:
            logger.warning("未找到$样品类别标记")
            return

        if not items:
            # 如果没有数据，清空标记行但保留行结构
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=marker_row, column=col_idx)
                if cell.value == "$样品类别":
                    cell.value = ""
            return

        # 保存模板行的样式
        template_row_styles = self._save_row_styles(ws, marker_row)

        # 计算需要插入的行数（items数量 - 1，因为已有一行模板行）
        rows_to_insert = len(items) - 1

        # 先填充第一行（模板行）
        self._fill_detail_row(ws, marker_row, items[0])

        # 如果有更多数据，插入新行
        if rows_to_insert > 0:
            # 在模板行后插入新行
            for i in range(rows_to_insert):
                insert_row = marker_row + 1 + i
                ws.insert_rows(insert_row)

                # 复制样式到新行
                self._copy_row_styles(ws, template_row_styles, insert_row)

                # 填充数据
                self._fill_detail_row(ws, insert_row, items[i + 1])

    def _fill_detail_row(self, ws, row_idx: int, item: Dict[str, Any]):
        """填充单行检测项目数据"""
        # 根据模板分析，检测项目数据的列映射
        column_mapping = {
            1: item.get("category", ""),  # A列：样品类别
            2: item.get("pointName", ""),  # B列：点位名称
            3: ",".join(item.get("parameters", [])),  # C列：监测项目
            4: item.get("pointCount", ""),  # D列：监测点位（个）
            5: item.get("cycleCount", ""),  # E列：监测周期（天）
            6: item.get("frequency", ""),  # F列：监测频次（次/天）
            7: item.get("sampleCount", ""),  # G列：一个点位单次样品（个）
            8: f"{float(item.get('samplingPrice', 0)):.2f}",  # H列：采样单价
            9: f"{float(item.get('testingPrice', 0)):.2f}",  # I列：检测单价
            10: f"{float(item.get('pretreatmentPrice', 0)):.2f}",  # J列：前处理单价
            11: f"{float(item.get('totalPrice', 0)):.2f}",  # K列：总价(元)
            12: item.get("remark", ""),  # L列：备注
        }

        # 填充数据到对应列
        for col_idx, value in column_mapping.items():
            cell = ws.cell(row=row_idx, column=col_idx)
            cell.value = value

    def _save_row_styles(self, ws, row_idx: int) -> Dict[int, Dict]:
        """保存行的样式信息"""
        styles = {}
        for col_idx in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            styles[col_idx] = {
                "font": Font(
                    name=cell.font.name,
                    size=cell.font.size,
                    bold=cell.font.bold,
                    italic=cell.font.italic,
                    color=cell.font.color,
                ),
                "alignment": Alignment(
                    horizontal=cell.alignment.horizontal,
                    vertical=cell.alignment.vertical,
                    wrap_text=cell.alignment.wrap_text,
                ),
                "border": Border(
                    left=cell.border.left, right=cell.border.right, top=cell.border.top, bottom=cell.border.bottom
                ),
                "fill": (
                    PatternFill(
                        fill_type=cell.fill.fill_type, start_color=cell.fill.start_color, end_color=cell.fill.end_color
                    )
                    if cell.fill.fill_type
                    else None
                ),
                "number_format": cell.number_format,
            }
        return styles

    def _copy_row_styles(self, ws, styles: Dict[int, Dict], target_row: int):
        """复制样式到目标行"""
        for col_idx, style in styles.items():
            cell = ws.cell(row=target_row, column=col_idx)
            cell.font = style["font"]
            cell.alignment = style["alignment"]
            cell.border = style["border"]
            if style["fill"]:
                cell.fill = style["fill"]
            cell.number_format = style["number_format"]

    def _render_other_fees(self, ws, other_fees: List[Dict[str, Any]]):
        """渲染其他费用"""
        # 查找$差旅费标记行
        marker_row = None
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value == "$差旅费":
                    marker_row = row_idx
                    break
            if marker_row:
                break

        if not marker_row:
            logger.warning("未找到$差旅费标记")
            return

        if not other_fees:
            # 如果没有数据，清空标记行但保留行结构
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=marker_row, column=col_idx)
                if cell.value == "$差旅费":
                    cell.value = ""
            return

        # 保存模板行的样式和合并信息
        template_row_styles = self._save_row_styles(ws, marker_row)
        template_merged_ranges = self._get_merged_ranges_for_row(ws, marker_row)

        # 计算需要插入的行数（other_fees数量 - 1，因为已有一行模板行）
        rows_to_insert = len(other_fees) - 1

        # 先填充第一行（模板行）
        self._fill_other_fee_row(ws, marker_row, other_fees[0])

        # 如果有更多数据，插入新行
        if rows_to_insert > 0:
            # 在模板行后插入新行
            for i in range(rows_to_insert):
                insert_row = marker_row + 1 + i
                ws.insert_rows(insert_row)

                # 复制样式到新行
                self._copy_row_styles(ws, template_row_styles, insert_row)

                # 复制合并单元格到新行
                self._copy_merged_ranges(ws, template_merged_ranges, insert_row, marker_row)

                # 填充数据
                self._fill_other_fee_row(ws, insert_row, other_fees[i + 1])

    def _fill_other_fee_row(self, ws, row_idx: int, fee: Dict[str, Any]):
        """填充单行其他费用数据"""
        # 根据模板分析，其他费用数据的列映射
        # C列和D列是合并的费用名称，E-G列是合并的单价，H-J列是合并的数量，K列是总价，L列是备注

        # C列：费用名称（合并C13:D13）
        cell_c = ws.cell(row=row_idx, column=3)
        cell_c.value = fee.get("feeName", "")

        # E列：单价（合并E13:G13）
        cell_e = ws.cell(row=row_idx, column=5)
        cell_e.value = f"{float(fee.get('unitPrice', 0)):.2f}"

        # H列：数量（合并H13:J13）
        cell_h = ws.cell(row=row_idx, column=8)
        cell_h.value = fee.get("quantity", 1)

        # K列：总价
        cell_k = ws.cell(row=row_idx, column=11)
        cell_k.value = f"{float(fee.get('totalPrice', 0)):.2f}"

        # L列：备注
        cell_l = ws.cell(row=row_idx, column=12)
        cell_l.value = fee.get("remark", "")

    def _get_merged_ranges_for_row(self, ws, row_idx: int) -> List[Dict]:
        """获取指定行的合并单元格信息"""
        merged_ranges = []
        for merged_range in ws.merged_cells.ranges:
            if merged_range.min_row <= row_idx <= merged_range.max_row:
                merged_ranges.append(
                    {
                        "min_row": merged_range.min_row,
                        "max_row": merged_range.max_row,
                        "min_col": merged_range.min_col,
                        "max_col": merged_range.max_col,
                        "range_str": str(merged_range),
                    }
                )
        return merged_ranges

    def _copy_merged_ranges(self, ws, merged_ranges: List[Dict], target_row: int, source_row: int):
        """复制合并单元格到目标行"""
        for merged_range in merged_ranges:
            # 计算行偏移
            row_offset = target_row - source_row

            # 计算新的合并范围
            new_min_row = merged_range["min_row"] + row_offset
            new_max_row = merged_range["max_row"] + row_offset

            # 只有当合并范围完全在目标行时才创建合并
            if new_min_row == target_row and new_max_row == target_row:
                try:
                    # 创建新的合并单元格
                    ws.merge_cells(
                        start_row=new_min_row,
                        start_column=merged_range["min_col"],
                        end_row=new_max_row,
                        end_column=merged_range["max_col"],
                    )
                except Exception as e:
                    logger.warning(f"合并单元格失败: {e}")

    def _render_fee_summary(self, ws, template_data: Dict[str, Any]):
        """渲染费用汇总"""
        # 查找并替换费用汇总模板变量
        for row in ws.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 使用Jinja2渲染模板变量
                    if "{{" in cell.value and "}}" in cell.value:
                        try:
                            template = Template(cell.value)
                            cell.value = template.render(**template_data)
                        except Exception as e:
                            logger.warning(f"费用汇总模板渲染失败: {cell.value}, 错误: {e}")
                            # 如果渲染失败，保持原值或设置为空
                            cell.value = ""
