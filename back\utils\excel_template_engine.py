"""
Excel模板引擎
支持基于Jinja2语法的Excel模板渲染
"""
import io
import re
from typing import Dict, Any, List, Optional
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
from jinja2 import Template
import logging

logger = logging.getLogger(__name__)


class ExcelTemplateEngine:
    """Excel模板引擎"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
        
    def create_quotation_template(self) -> Workbook:
        """
        创建报价单模板
        
        :return: Excel工作簿对象
        """
        wb = Workbook()
        ws = wb.active
        ws.title = "项目报价单"
        
        # 设置列宽
        column_widths = {
            'A': 6, 'B': 12, 'C': 15, 'D': 20, 'E': 12, 'F': 8, 'G': 8, 'H': 8, 'I': 8,
            'J': 10, 'K': 10, 'L': 10, 'M': 10, 'N': 10, 'O': 10, 'P': 15, 'Q': 15
        }
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width
        
        # 定义样式
        title_font = Font(name='宋体', size=20, bold=True)
        subtitle_font = Font(name='宋体', size=16, bold=True)
        header_font = Font(name='宋体', size=12, bold=True)
        normal_font = Font(name='宋体', size=10)
        
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
        right_alignment = Alignment(horizontal='right', vertical='center')
        
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 创建模板内容
        current_row = 1
        
        # 1. 公司标题
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        company_title = ws[f'A{current_row}']
        company_title.value = "北京东西分析股份有限公司"
        company_title.font = title_font
        company_title.alignment = center_alignment
        current_row += 2
        
        # 2. 报价单标题
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        quotation_title = ws[f'A{current_row}']
        quotation_title.value = "项目报价单"
        quotation_title.font = subtitle_font
        quotation_title.alignment = center_alignment
        current_row += 2
        
        # 3. 项目基本信息表格
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        info_header = ws[f'A{current_row}']
        info_header.value = "项目基本信息"
        info_header.font = header_font
        info_header.alignment = center_alignment
        info_header.border = thin_border
        current_row += 1
        
        # 基本信息模板行
        info_template_rows = [
            ["项目名称", "{{project_name}}", "项目编号", "{{project_code}}"],
            ["客户名称", "{{customer_name}}", "受检方", "{{inspected_party}}"],
            ["项目负责人", "{{project_manager}}", "市场负责人", "{{market_manager}}"],
            ["技术负责人", "{{technical_manager}}", "委托日期", "{{commission_date}}"],
            ["合同编号", "{{contract_code}}", "业务类别", "{{business_type}}"],
        ]
        
        for row_data in info_template_rows:
            self._create_info_row(ws, current_row, row_data, header_font, normal_font, 
                                center_alignment, left_alignment, thin_border)
            current_row += 1
        
        current_row += 1
        
        # 4. 检测项目明细表
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        detail_title = ws[f'A{current_row}']
        detail_title.value = "检测项目明细"
        detail_title.font = header_font
        detail_title.alignment = center_alignment
        detail_title.border = thin_border
        current_row += 1
        
        # 明细表头
        detail_headers = [
            "序号", "样品类别", "检测参数", "检测方法", "点位名称", "点位数", "周期", "频次", "样品数",
            "采样单价", "采样费", "检测首项价", "检测增项价", "检测费上限", "检测费", "前处理费", "备注"
        ]
        
        for col_idx, header in enumerate(detail_headers, 1):
            cell = ws.cell(row=current_row, column=col_idx)
            cell.value = header
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
        current_row += 1
        
        # 明细数据模板行（标记行，用于循环）
        ws.cell(row=current_row, column=2).value = "$样品类别"  # 标记需要循环的行
        current_row += 1
        
        current_row += 1
        
        # 5. 其他费用表
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        other_fee_title = ws[f'A{current_row}']
        other_fee_title.value = "其他费用明细"
        other_fee_title.font = header_font
        other_fee_title.alignment = center_alignment
        other_fee_title.border = thin_border
        current_row += 1
        
        # 其他费用表头
        other_fee_headers = ["序号", "费用名称", "费用金额", "备注"]
        for col_idx, header in enumerate(other_fee_headers, 1):
            cell = ws.cell(row=current_row, column=col_idx)
            cell.value = header
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
        current_row += 1
        
        # 其他费用数据模板行（标记行，用于循环）
        ws.cell(row=current_row, column=2).value = "$差旅费"  # 标记需要循环的行
        current_row += 1
        
        current_row += 1
        
        # 6. 费用汇总表
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        summary_title = ws[f'A{current_row}']
        summary_title.value = "费用汇总"
        summary_title.font = header_font
        summary_title.alignment = center_alignment
        summary_title.border = thin_border
        current_row += 1
        
        # 费用汇总模板行
        summary_template_rows = [
            ["采样费用", "{{total_sampling_fee}}", "检测费用", "{{total_testing_fee}}"],
            ["前处理费用", "{{total_pretreatment_fee}}", "特殊耗材费", "{{special_consumables_fee}}"],
            ["其他费用", "{{other_fees}}", "检测项目小计", "{{subtotal_amount}}"],
            ["折扣率", "{{discount_rate}}%", "折后金额", "{{discounted_amount}}"],
            ["税率", "{{tax_rate}}%", "税额", "{{tax_amount}}"],
            ["整体调整", "{{adjustment_amount}}", "最终总价", "{{final_amount}}"]
        ]
        
        for row_data in summary_template_rows:
            self._create_info_row(ws, current_row, row_data, header_font, normal_font,
                                center_alignment, right_alignment, thin_border)
            current_row += 1
        
        current_row += 2
        
        # 7. 签字区域
        ws.merge_cells(f'A{current_row}:Q{current_row}')
        signature_title = ws[f'A{current_row}']
        signature_title.value = "审批签字"
        signature_title.font = header_font
        signature_title.alignment = center_alignment
        signature_title.border = thin_border
        current_row += 1
        
        # 签字表格
        signature_headers = ["制表人", "审核人", "批准人"]
        for col_idx, header in enumerate(signature_headers):
            start_col = 1 + col_idx * 5
            end_col = min(start_col + 4, 17)
            if start_col <= 17:
                ws.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
                cell = ws.cell(row=current_row, column=start_col)
                cell.value = f"{header}："
                cell.font = header_font
                cell.alignment = left_alignment
                cell.border = thin_border
        current_row += 1
        
        # 日期行
        for col_idx in range(3):
            start_col = 1 + col_idx * 5
            end_col = min(start_col + 4, 17)
            if start_col <= 17:
                ws.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
                cell = ws.cell(row=current_row, column=start_col)
                cell.value = "日期："
                cell.font = header_font
                cell.alignment = left_alignment
                cell.border = thin_border
        
        return wb
    
    def _create_info_row(self, ws, row, row_data, header_font, normal_font, 
                        center_alignment, value_alignment, thin_border):
        """创建信息行"""
        # 标签1
        cell_a = ws.cell(row=row, column=1)
        cell_a.value = row_data[0]
        cell_a.font = header_font
        cell_a.alignment = center_alignment
        cell_a.border = thin_border
        
        # 值1 (合并B-H列)
        ws.merge_cells(f'B{row}:H{row}')
        cell_b = ws.cell(row=row, column=2)
        cell_b.value = row_data[1]
        cell_b.font = normal_font
        cell_b.alignment = value_alignment
        cell_b.border = thin_border
        
        # 标签2
        cell_i = ws.cell(row=row, column=9)
        cell_i.value = row_data[2]
        cell_i.font = header_font
        cell_i.alignment = center_alignment
        cell_i.border = thin_border
        
        # 值2 (合并J-Q列)
        ws.merge_cells(f'J{row}:Q{row}')
        cell_j = ws.cell(row=row, column=10)
        cell_j.value = row_data[3]
        cell_j.font = normal_font
        cell_j.alignment = value_alignment
        cell_j.border = thin_border

    def render_template(self, template_data: Dict[str, Any]) -> io.BytesIO:
        """
        渲染模板

        :param template_data: 模板数据
        :return: 渲染后的Excel文件字节流
        """
        # 创建模板工作簿
        wb = self.create_quotation_template()
        ws = wb.active

        # 渲染基本信息
        self._render_basic_info(ws, template_data)

        # 渲染检测项目明细
        self._render_detail_items(ws, template_data.get('items', []))

        # 渲染其他费用
        self._render_other_fees(ws, template_data.get('other_fees', []))

        # 渲染费用汇总
        self._render_fee_summary(ws, template_data)

        # 保存到字节流
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return output

    def _render_basic_info(self, ws, template_data: Dict[str, Any]):
        """渲染基本信息"""
        # 查找并替换基本信息模板变量
        for row in ws.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 使用Jinja2渲染模板变量
                    if '{{' in cell.value and '}}' in cell.value:
                        try:
                            template = Template(cell.value)
                            cell.value = template.render(**template_data)
                        except Exception as e:
                            logger.warning(f"模板渲染失败: {cell.value}, 错误: {e}")
                            # 如果渲染失败，保持原值或设置为空
                            cell.value = ""

    def _render_detail_items(self, ws, items: List[Dict[str, Any]]):
        """渲染检测项目明细"""
        # 查找标记行
        marker_row = None
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for cell in row:
                if cell.value == "$样品类别":
                    marker_row = row_idx
                    break
            if marker_row:
                break

        if not marker_row or not items:
            return

        # 获取样式信息
        sample_cell = ws.cell(row=marker_row, column=1)
        cell_font = sample_cell.font
        cell_alignment = sample_cell.alignment
        cell_border = sample_cell.border

        # 删除标记行
        ws.delete_rows(marker_row)

        # 插入数据行
        for idx, item in enumerate(items):
            current_row = marker_row + idx

            # 插入新行
            ws.insert_rows(current_row)

            # 填充数据
            row_data = [
                idx + 1,  # 序号
                item.get("category", ""),  # 样品类别
                item.get("parameter", ""),  # 检测参数
                item.get("method", ""),  # 检测方法
                item.get("point_name", ""),  # 点位名称
                item.get("point_count", ""),  # 点位数
                item.get("cycle_count", ""),  # 周期
                item.get("frequency", ""),  # 频次
                item.get("sample_count", ""),  # 样品数
                f"{float(item.get('sampling_price', 0)):.2f}",  # 采样单价
                f"{float(item.get('sampling_fee', 0)):.2f}",  # 采样费
                f"{float(item.get('first_item_price', 0)):.2f}",  # 检测首项价
                f"{float(item.get('additional_item_price', 0)):.2f}",  # 检测增项价
                f"{float(item.get('testing_fee_limit', 0)):.2f}",  # 检测费上限
                f"{float(item.get('testing_fee', 0)):.2f}",  # 检测费
                f"{float(item.get('pretreatment_fee', 0)):.2f}",  # 前处理费
                item.get("remark", "")  # 备注
            ]

            # 设置单元格值和样式
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=current_row, column=col_idx)
                cell.value = value
                cell.font = cell_font
                cell.alignment = cell_alignment
                cell.border = cell_border

    def _render_other_fees(self, ws, other_fees: List[Dict[str, Any]]):
        """渲染其他费用"""
        # 查找标记行
        marker_row = None
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for cell in row:
                if cell.value == "$差旅费":
                    marker_row = row_idx
                    break
            if marker_row:
                break

        if not marker_row or not other_fees:
            # 如果没有其他费用，删除标记行
            if marker_row:
                ws.delete_rows(marker_row)
            return

        # 获取样式信息
        sample_cell = ws.cell(row=marker_row, column=1)
        cell_font = sample_cell.font
        cell_alignment = sample_cell.alignment
        cell_border = sample_cell.border

        # 删除标记行
        ws.delete_rows(marker_row)

        # 插入数据行
        for idx, fee in enumerate(other_fees):
            current_row = marker_row + idx

            # 插入新行
            ws.insert_rows(current_row)

            # 填充数据
            row_data = [
                idx + 1,  # 序号
                fee.get("fee_name", ""),  # 费用名称
                f"{float(fee.get('fee_amount', 0)):.2f}",  # 费用金额
                fee.get("remark", "")  # 备注
            ]

            # 设置单元格值和样式
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=current_row, column=col_idx)
                cell.value = value
                cell.font = cell_font
                cell.alignment = cell_alignment
                cell.border = cell_border

    def _render_fee_summary(self, ws, template_data: Dict[str, Any]):
        """渲染费用汇总"""
        # 查找并替换费用汇总模板变量
        for row in ws.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 使用Jinja2渲染模板变量
                    if '{{' in cell.value and '}}' in cell.value:
                        try:
                            template = Template(cell.value)
                            cell.value = template.render(**template_data)
                        except Exception as e:
                            logger.warning(f"费用汇总模板渲染失败: {cell.value}, 错误: {e}")
                            # 如果渲染失败，保持原值或设置为空
                            cell.value = ""
