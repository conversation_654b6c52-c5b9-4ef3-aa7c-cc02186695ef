"""
Excel模板引擎
支持基于现有Excel模板文件的数据渲染
"""

import io
import logging
import os
from decimal import Decimal
from typing import Dict, Any, List

from jinja2 import Template
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

logger = logging.getLogger(__name__)


class ExcelTemplateEngine:
    """Excel模板引擎"""

    def __init__(self, template_path: str = None):
        self.workbook = None
        self.worksheet = None
        # 获取当前文件的目录，然后构建模板路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        default_template_path = os.path.join(
            os.path.dirname(current_dir), "templates", "quotation_price_export_tpl_new.xlsx"
        )
        self.template_path = template_path or default_template_path

    def create_quotation_template(self) -> Workbook:
        """
        创建报价单模板

        :return: Excel工作簿对象
        """
        wb = Workbook()
        ws = wb.active
        ws.title = "项目报价单"

        # 设置列宽
        column_widths = {
            "A": 6,
            "B": 12,
            "C": 15,
            "D": 20,
            "E": 12,
            "F": 8,
            "G": 8,
            "H": 8,
            "I": 8,
            "J": 10,
            "K": 10,
            "L": 10,
        }
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width

        # 定义样式
        title_font = Font(name="宋体", size=20, bold=True)
        subtitle_font = Font(name="宋体", size=16, bold=True)
        header_font = Font(name="宋体", size=12, bold=True)
        normal_font = Font(name="宋体", size=10)

        center_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        left_alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
        right_alignment = Alignment(horizontal="right", vertical="center")

        thin_border = Border(
            left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
        )

        # 创建模板内容
        current_row = 1

        # 1. 公司标题
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        company_title = ws[f"A{current_row}"]
        company_title.value = "北京东西分析股份有限公司"
        company_title.font = title_font
        company_title.alignment = center_alignment
        current_row += 2

        # 2. 报价单标题
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        quotation_title = ws[f"A{current_row}"]
        quotation_title.value = "项目报价单"
        quotation_title.font = subtitle_font
        quotation_title.alignment = center_alignment
        current_row += 2

        # 3. 项目基本信息表格
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        info_header = ws[f"A{current_row}"]
        info_header.value = "项目基本信息"
        info_header.font = header_font
        info_header.alignment = center_alignment
        info_header.border = thin_border
        current_row += 1

        # 基本信息模板行
        info_template_rows = [
            ["委托方", "{{customer_name}}", "受检方", "{{inspected_party}}"],
            ["地址", "{{customer_address}}", "地址", "{{customer_address}}"],
            ["联系人", "{{customer_contact}}", "联系人", "{{inspected_contact}}"],
            ["联系电话", "{{customer_phone}}", "联系电话", "{{inspected_phone}}"],
            ["项目名称", "{{project_name}}", "项目编号", "{{inspected_address}}"],
        ]

        for row_data in info_template_rows:
            self._create_info_row(
                ws, current_row, row_data, header_font, normal_font, center_alignment, left_alignment, thin_border
            )
            current_row += 1

        current_row += 1

        # 4. 检测项目明细表
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        detail_title = ws[f"A{current_row}"]
        detail_title.value = "检测项目明细"
        detail_title.font = header_font
        detail_title.alignment = center_alignment
        detail_title.border = thin_border
        current_row += 1

        # 明细表头
        detail_headers = [
            ("A", "样品类别"),
            ("B", "点位名称"),
            ("C", "检测项目"),
            # ("C", "检测方法"),
            ("D", "点位数"),
            ("E", "周期"),
            ("F", "频次"),
            ("G", "样品数"),
            ("H", "采样单价"),
            ("I", "检测首项价"),
            ("J", "前处理费"),
            ("K", "总价"),
            ("L", "备注"),  # （若样品来源为送样，备注处自动带入：送样，采样费用未计算）
        ]

        for col_letter, header_text in detail_headers:
            cell = ws[f"{col_letter}{current_row}"]
            cell.value = header_text
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
        current_row += 1

        # 明细数据模板行（标记行，用于循环）
        ws.cell(row=current_row, column=2).value = "$样品类别"  # 标记需要循环的行
        current_row += 1

        current_row += 1

        # 5. 其他费用表
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        other_fee_title = ws[f"A{current_row}"]
        other_fee_title.value = "其他费用明细"
        other_fee_title.font = header_font
        other_fee_title.alignment = center_alignment
        other_fee_title.border = thin_border
        current_row += 1

        # 其他费用表头
        other_fee_headers = ["序号", "费用名称", "费用金额", "备注"]
        for col_idx, header in enumerate(other_fee_headers, 1):
            cell = ws.cell(row=current_row, column=col_idx)
            cell.value = header
            cell.font = header_font
            cell.alignment = center_alignment
            cell.border = thin_border
        current_row += 1

        # 其他费用数据模板行（标记行，用于循环）
        ws.cell(row=current_row, column=2).value = "$差旅费"  # 标记需要循环的行
        current_row += 1

        current_row += 1

        # 6. 费用汇总表
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        summary_title = ws[f"A{current_row}"]
        summary_title.value = "费用汇总"
        summary_title.font = header_font
        summary_title.alignment = center_alignment
        summary_title.border = thin_border
        current_row += 1

        # 费用汇总模板行
        summary_template_rows = [
            ["采样费用", "{{total_sampling_fee}}", "检测费用", "{{total_testing_fee}}"],
            ["前处理费用", "{{total_pretreatment_fee}}", "特殊耗材费", "{{special_consumables_fee}}"],
            ["其他费用", "{{other_fees}}", "检测项目小计", "{{subtotal_amount}}"],
            ["折扣率", "{{discount_rate}}%", "折后金额", "{{discounted_amount}}"],
            ["税率", "{{tax_rate}}%", "税额", "{{tax_amount}}"],
            ["整体调整", "{{adjustment_amount}}", "最终总价", "{{final_amount}}"],
        ]

        for row_data in summary_template_rows:
            self._create_info_row(
                ws, current_row, row_data, header_font, normal_font, center_alignment, right_alignment, thin_border
            )
            current_row += 1

        current_row += 2

        # 7. 签字区域
        ws.merge_cells(f"A{current_row}:Q{current_row}")
        signature_title = ws[f"A{current_row}"]
        signature_title.value = "审批签字"
        signature_title.font = header_font
        signature_title.alignment = center_alignment
        signature_title.border = thin_border
        current_row += 1

        # 签字表格
        signature_headers = ["制表人", "审核人", "批准人"]
        for col_idx, header in enumerate(signature_headers):
            start_col = 1 + col_idx * 5
            end_col = min(start_col + 4, 17)
            if start_col <= 17:
                ws.merge_cells(f"{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}")
                cell = ws.cell(row=current_row, column=start_col)
                cell.value = f"{header}："
                cell.font = header_font
                cell.alignment = left_alignment
                cell.border = thin_border
        current_row += 1

        # 日期行
        for col_idx in range(3):
            start_col = 1 + col_idx * 5
            end_col = min(start_col + 4, 17)
            if start_col <= 17:
                ws.merge_cells(f"{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}")
                cell = ws.cell(row=current_row, column=start_col)
                cell.value = "日期："
                cell.font = header_font
                cell.alignment = left_alignment
                cell.border = thin_border

        return wb

    def _create_info_row(
        self, ws, current_row, row_data, header_font, normal_font, center_alignment, value_alignment, thin_border
    ):
        """创建信息行"""
        # 标签1
        ws.merge_cells(f"A{current_row}:B{current_row}")
        cell_a = ws.cell(row=current_row, column=2)
        cell_a.value = row_data[0]
        cell_a.font = header_font
        cell_a.alignment = center_alignment
        cell_a.border = thin_border

        # 值1 (合并C-F列)
        ws.merge_cells(f"C{current_row}:F{current_row}")
        cell_b = ws.cell(row=current_row, column=4)
        cell_b.value = row_data[1]
        cell_b.font = normal_font
        cell_b.alignment = value_alignment
        cell_b.border = thin_border

        # 标签2(G列) todo: 项目地省市（合并G-H列)、报价日期(K列)
        cell_i = ws.cell(row=current_row, column=1)
        cell_i.value = row_data[2]
        cell_i.font = header_font
        cell_i.alignment = center_alignment
        cell_i.border = thin_border

        # 值2 (合并H-L列) todo: 项目地省市（合并I-J列)、报价日期(L列)
        ws.merge_cells(f"H{current_row}:H{current_row}")
        cell_j = ws.cell(row=current_row, column=5)
        cell_j.value = row_data[3]
        cell_b.font = normal_font
        cell_b.alignment = value_alignment
        cell_b.border = thin_border

    def render_template(self, template_data: Dict[str, Any]) -> io.BytesIO:
        """
        渲染模板

        :param template_data: 模板数据
        :return: 渲染后的Excel文件字节流
        """
        # 加载现有模板文件
        if not os.path.exists(self.template_path):
            raise FileNotFoundError(f"模板文件不存在: {self.template_path}")

        wb = load_workbook(self.template_path)

        # 确保选择第一个sheet（项目报价单）
        if "项目报价单" in wb.sheetnames:
            ws = wb["项目报价单"]
            logger.debug("选择了'项目报价单'工作表")
        else:
            ws = wb.active
            logger.debug(f"使用默认工作表: {ws.title}")

        # 在开始渲染前保存所有原始合并单元格信息
        self.original_merged_ranges = self._save_all_merged_ranges(ws)
        logger.debug(f"保存了 {len(self.original_merged_ranges)} 个原始合并单元格")

        # 渲染基本信息
        current_row = 6
        self._render_basic_info(ws, template_data, 1, current_row)

        # 渲染检测项目明细
        detail_items = template_data.get("items", [])
        self._render_detail_items(ws, detail_items)

        # 计算检测项目明细插入的行数，更新合并单元格
        detail_rows_inserted = max(0, len(detail_items) - 1)
        if detail_rows_inserted > 0:
            self._update_merged_ranges_after_detail_insert(ws, detail_rows_inserted)

        current_row += len(detail_items) + 1

        # 检测费用合计 itemTestingFees+discountedTestingFee
        self._render_basic_info(ws, template_data.get("totalFees", {}), current_row + 1, current_row + 2)
        current_row += 2

        # 渲染其他费用
        other_fees = template_data.get("otherFees", [])
        self._render_other_fees(ws, other_fees, current_row + 1)

        # 计算其他费用插入的行数，更新合并单元格
        other_rows_inserted = max(0, len(other_fees) - 1)
        if other_rows_inserted > 0:
            self._update_merged_ranges_after_other_insert(ws, other_rows_inserted)

        current_row += len(other_fees) + 1

        # 渲染费用汇总(共计5行) - 使用专门的费用汇总渲染函数
        self._render_fee_summary(ws, template_data.get("totalFees", {}), current_row + 1, current_row + 5)

        # 渲染方法-一览表sheet
        self._render_method_overview_sheet(wb, template_data)

        # 保存到字节流
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return output

    def _render_basic_info(self, ws, template_data: Dict[str, Any], start_row: int, end_row: int):
        """渲染基本信息"""
        # 查找并替换基本信息模板变量
        for row in ws.iter_rows(min_row=start_row, max_row=end_row):
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 使用Jinja2渲染模板变量
                    if "{{" in cell.value and "}}" in cell.value:
                        try:
                            template = Template(cell.value)
                            cell.value = template.render(**template_data)
                        except Exception as e:
                            logger.warning(f"模板渲染失败: {cell.value}, 错误: {e}")
                            # 如果渲染失败，保持原值或设置为空
                            # cell.value = ""
                    # 处理简单的变量替换（如果模板中使用了简单的变量名）
                    elif cell.value in template_data:
                        cell.value = template_data[cell.value]

    def _render_detail_items(self, ws, items: List[Dict[str, Any]]):
        """渲染检测项目明细"""
        # 查找$样品类别标记行和$itemEnd标记行
        start_marker_row = None
        end_marker_row = None

        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for col_idx, cell in enumerate(row, 1):
                if cell.value == "$样品类别":
                    start_marker_row = row_idx
                elif cell.value == "$itemEnd":
                    end_marker_row = row_idx
            if start_marker_row and end_marker_row:
                break

        if not start_marker_row:
            logger.warning("未找到$样品类别标记，尝试查找其他项目明细标记")
            # 尝试查找其他可能的标记
            for row_idx in range(1, ws.max_row + 1):
                for col_idx in range(1, ws.max_column + 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    if cell.value and isinstance(cell.value, str):
                        cell_text = str(cell.value).strip()
                        if any(keyword in cell_text for keyword in ["样品", "检测项目", "明细", "项目"]):
                            start_marker_row = row_idx
                            logger.debug(f"找到替代项目明细标记: 行{row_idx} = {cell_text}")
                            break
                if start_marker_row:
                    break

            if not start_marker_row:
                logger.warning("未找到任何项目明细标记，跳过项目明细渲染")
                return

        if not end_marker_row:
            logger.warning("未找到$itemEnd标记，使用默认结束位置")
            # 如果没有结束标记，使用开始标记后的合理位置
            end_marker_row = start_marker_row + max(1, len(items))

        if not items:
            # 如果没有数据，清空标记行并删除$itemEnd标记行
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=start_marker_row, column=col_idx)
                if cell.value and isinstance(cell.value, str) and cell.value.startswith("$"):
                    cell.value = ""
            # 删除$itemEnd标记行
            ws.delete_rows(end_marker_row)
            logger.debug(f"删除了$itemEnd标记行: {end_marker_row}")
            return

        # 保存模板行的样式和合并信息
        template_row_styles = self._save_row_styles(ws, start_marker_row)
        template_merged_ranges = self._get_merged_ranges_for_row(ws, start_marker_row)

        # 计算需要插入的行数
        rows_to_insert = len(items) - 1

        # 在$itemEnd标记行之前插入新行
        if rows_to_insert > 0:
            insert_position = start_marker_row + 1

            for i in range(rows_to_insert):
                # 在指定位置插入行
                ws.insert_rows(insert_position)
                # 复制样式和合并单元格到新插入的行
                self._copy_row_styles(ws, template_row_styles, insert_position)
                self._copy_merged_ranges(ws, template_merged_ranges, insert_position, start_marker_row)
                insert_position += 1

        # 填充所有数据行（从start_marker_row开始）
        for i, item in enumerate(items):
            self._fill_detail_row(ws, start_marker_row + i, item)

        # 删除所有$itemEnd标记行
        self._remove_all_markers(ws, "$itemEnd")
        logger.debug(f"渲染了 {len(items)} 个检测项目明细")

    def _fill_detail_row(self, ws, row_idx: int, item: Dict[str, Any]):
        """填充单行检测项目数据"""
        # 根据模板分析，检测项目数据的列映射
        column_mapping = {
            1: item.get("category", ""),  # A列：样品类别
            2: item.get("pointName", ""),  # B列：点位名称
            3: "、".join(item.get("parameters", [])),  # C列：监测项目
            4: item.get("pointCount", 1),  # D列：监测点位（个）
            5: item.get("cycleCount", 1),  # E列：监测周期（天）
            6: item.get("frequency", 1),  # F列：监测频次（次/天）
            7: item.get("sampleCount", 1),  # G列：一个点位单次样品（个）
            8: f"{float(item.get('samplingUnitPrice', 0)):.2f}",  # H列：采样单价
            9: f"{float(item.get('testingUnitPrice', 0)):.2f}",  # I列：检测单价
            10: f"{float(item.get('pretreatmentUnitPrice', 0)):.2f}",  # J列：前处理单价
            11: f"{float(item.get('itemTotalFee', 0)):.2f}",  # K列：总价(元)
            12: item.get("remark", ""),  # L列：备注
        }

        # 填充数据到对应列
        for col_idx, value in column_mapping.items():
            cell = ws.cell(row=row_idx, column=col_idx)
            # 清除标记并填充数据
            if cell.value == "$样品类别":
                cell.value = value
            else:
                cell.value = value

    def _save_row_styles(self, ws, row_idx: int) -> Dict[int, Dict]:
        """保存行的样式信息"""
        styles = {}
        for col_idx in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            styles[col_idx] = {
                "font": Font(
                    name=cell.font.name,
                    size=cell.font.size,
                    bold=cell.font.bold,
                    italic=cell.font.italic,
                    color=cell.font.color,
                ),
                "alignment": Alignment(
                    horizontal=cell.alignment.horizontal,
                    vertical=cell.alignment.vertical,
                    wrap_text=cell.alignment.wrap_text,
                ),
                "border": Border(
                    left=cell.border.left, right=cell.border.right, top=cell.border.top, bottom=cell.border.bottom
                ),
                "fill": (
                    PatternFill(
                        fill_type=cell.fill.fill_type, start_color=cell.fill.start_color, end_color=cell.fill.end_color
                    )
                    if cell.fill.fill_type
                    else None
                ),
                "number_format": cell.number_format,
                "value": cell.value,
            }
        return styles

    def _copy_row_styles(self, ws, styles: Dict[int, Dict], target_row: int):
        """复制样式到目标行"""
        for col_idx, style in styles.items():
            cell = ws.cell(row=target_row, column=col_idx)
            cell.font = style["font"]
            cell.alignment = style["alignment"]
            cell.border = style["border"]
            if style["fill"]:
                cell.fill = style["fill"]
            cell.number_format = style["number_format"]
            if style["value"] and "{{" not in style["value"]:
                cell.value = style["value"]

    def _render_other_fees(self, ws, other_fees: List[Dict[str, Any]], start_row: int):
        """渲染其他费用"""
        # 查找$差旅费标记行和$feeEnd标记行
        start_marker_row = None
        end_marker_row = None

        for row_idx, row in enumerate(ws.iter_rows(min_row=start_row), start_row):
            for col_idx, cell in enumerate(row, 1):
                if cell.value == "$差旅费":
                    start_marker_row = row_idx
                elif cell.value == "$feeEnd":
                    end_marker_row = row_idx
                if start_marker_row and end_marker_row:
                    break

        if not start_marker_row:
            logger.warning("未找到$差旅费标记，尝试查找其他费用标记")
            # 尝试查找其他可能的标记
            for row_idx in range(1, ws.max_row + 1):
                for col_idx in range(1, ws.max_column + 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    if cell.value and isinstance(cell.value, str):
                        cell_text = str(cell.value).strip()
                        if any(keyword in cell_text for keyword in ["其他费用", "差旅费", "费用", "其他"]):
                            start_marker_row = row_idx
                            logger.debug(f"找到替代其他费用标记: 行{row_idx} = {cell_text}")
                            break
                if start_marker_row:
                    break

            if not start_marker_row:
                logger.warning("未找到任何其他费用标记，跳过其他费用渲染")
                return

        if not end_marker_row:
            logger.warning("未找到$feeEnd标记，使用默认结束位置")
            # 如果没有结束标记，使用开始标记后的合理位置
            end_marker_row = start_marker_row + max(1, len(other_fees))

        if not other_fees:
            # 如果没有数据，清空所有相关标记行并删除
            logger.debug(f"其他费用为空，清理标记行: {start_marker_row} 到 {end_marker_row}")

            # 清空开始标记行的所有$标记
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=start_marker_row, column=col_idx)
                if cell.value and isinstance(cell.value, str) and cell.value.startswith("$"):
                    cell.value = ""
                    logger.debug(f"清空开始标记: 行{start_marker_row} 列{col_idx}")

            # 删除所有$feeEnd标记行（可能有多个）
            self._remove_all_markers(ws, "$feeEnd")

            # 如果开始行和结束行不同，也删除结束行
            if end_marker_row != start_marker_row:
                try:
                    # 检查结束行是否还存在$feeEnd标记
                    has_fee_end = False
                    for col_idx in range(1, ws.max_column + 1):
                        cell = ws.cell(row=end_marker_row, column=col_idx)
                        if cell.value and isinstance(cell.value, str) and "$feeEnd" in cell.value:
                            has_fee_end = True
                            break

                    if has_fee_end:
                        ws.delete_rows(end_marker_row)
                        logger.debug(f"删除$feeEnd标记行: {end_marker_row}")
                except Exception as e:
                    logger.warning(f"删除$feeEnd标记行失败: {e}")

            logger.debug("其他费用为空时的清理完成")
            return

        # 保存模板行的样式和合并信息
        template_row_styles = self._save_row_styles(ws, start_marker_row)
        template_merged_ranges = self._get_merged_ranges_for_row(ws, start_marker_row)

        # 计算需要插入的行数
        rows_to_insert = len(other_fees) - 1

        # 在$feeEnd标记行之前插入新行
        if rows_to_insert > 0:
            insert_position = start_marker_row + 1

            for i in range(rows_to_insert):
                # 在指定位置插入行
                ws.insert_rows(insert_position)
                # 复制样式和合并单元格到新插入的行
                self._copy_row_styles(ws, template_row_styles, insert_position)
                self._copy_merged_ranges(ws, template_merged_ranges, insert_position, start_marker_row)
                insert_position += 1

        # 填充所有数据行（从start_marker_row开始）
        for i, fee in enumerate(other_fees):
            self._fill_other_fee_row(ws, start_marker_row + i, fee)

        # 处理"二、其他费用"标题的动态合并
        self._process_other_fees_title_merge(ws, len(other_fees), start_marker_row)

        # 删除所有$feeEnd标记行
        self._remove_all_markers(ws, "$feeEnd")
        logger.debug(f"渲染了 {len(other_fees)} 个其他费用项目")

    def _remove_all_markers(self, ws, marker_text: str):
        """删除所有指定的标记行"""
        rows_to_delete = []
        max_attempts = 3  # 最多尝试3次，防止无限循环

        for _ in range(max_attempts):
            # 收集所有包含标记的行号
            current_rows_to_delete = []
            for row_idx in range(1, ws.max_row + 1):
                row_has_marker = False
                for col_idx in range(1, ws.max_column + 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    if cell.value and isinstance(cell.value, str) and marker_text in cell.value:
                        row_has_marker = True
                        logger.debug(f"找到标记 {marker_text} 在行{row_idx}列{col_idx}: {cell.value}")
                        break
                if row_has_marker:
                    current_rows_to_delete.append(row_idx)

            if not current_rows_to_delete:
                break  # 没有更多标记行需要删除

            # 从后往前删除行（避免行号变化影响）
            for row_idx in sorted(current_rows_to_delete, reverse=True):
                try:
                    # 先清空标记内容
                    for col_idx in range(1, ws.max_column + 1):
                        cell = ws.cell(row=row_idx, column=col_idx)
                        if cell.value and isinstance(cell.value, str) and marker_text in cell.value:
                            cell.value = ""

                    # 然后删除行
                    ws.delete_rows(row_idx)
                    logger.debug(f"删除标记行: {row_idx}")
                except Exception as e:
                    logger.warning(f"删除标记行 {row_idx} 失败: {e}")

            rows_to_delete.extend(current_rows_to_delete)

        logger.debug(f"删除了 {len(rows_to_delete)} 个包含 {marker_text} 的标记行")

    def _render_other_fees_legacy(self, ws, other_fees: List[Dict[str, Any]], marker_row: int):
        """传统的其他费用渲染方式（当没有$feeEnd标记时使用）"""
        # 保存模板行的样式和合并信息
        template_row_styles = self._save_row_styles(ws, marker_row)
        template_merged_ranges = self._get_merged_ranges_for_row(ws, marker_row)

        # 计算需要插入的行数
        rows_to_insert = len(other_fees) - 1

        # 在模板行后插入空行
        if rows_to_insert > 0:
            for i in range(rows_to_insert):
                ws.insert_rows(marker_row + 1)

        # 复制样式和合并单元格到所有数据行
        for i in range(len(other_fees)):
            if i > 0:  # 第一行已经有样式了
                self._copy_row_styles(ws, template_row_styles, marker_row + i)
                self._copy_merged_ranges(ws, template_merged_ranges, marker_row + i, marker_row)

        # 填充所有数据行
        for i, fee in enumerate(other_fees):
            self._fill_other_fee_row(ws, marker_row + i, fee)

    def _fill_other_fee_row(self, ws, row_idx: int, fee: Dict[str, Any]):
        """填充单行其他费用数据"""
        # 根据模板分析，其他费用数据的列映射
        # C列和D列是合并的费用名称，E-G列是合并的单价，H-J列是合并的数量，K列是总价，L列是备注

        try:
            # 先确保所有需要的合并单元格存在
            self._ensure_other_fee_merge(ws, row_idx, 3, 4, "费用名称")  # C:D列合并
            self._ensure_other_fee_merge(ws, row_idx, 5, 7, "单价")  # E:G列合并
            self._ensure_other_fee_merge(ws, row_idx, 8, 10, "数量")  # H:J列合并

            # C列：费用名称（合并C:D）- 只操作合并区域的左上角单元格
            cell_c = ws.cell(row=row_idx, column=3)
            # 清除$差旅费标记
            if cell_c.value == "$差旅费":
                cell_c.value = fee.get("fee_name", fee.get("feeName", ""))
            else:
                cell_c.value = fee.get("fee_name", fee.get("feeName", ""))

            # E列：单价（合并E:G）- 只操作合并区域的左上角单元格
            cell_e = ws.cell(row=row_idx, column=5)
            unit_price = fee.get("unit_price", fee.get("unitPrice", 0))
            cell_e.value = f"{float(unit_price):.2f}" if unit_price else "0.00"

            # H列：数量（合并H:J）- 只操作合并区域的左上角单元格
            cell_h = ws.cell(row=row_idx, column=8)
            cell_h.value = fee.get("quantity", 1)

            # K列：总价（单独列，不合并）
            cell_k = ws.cell(row=row_idx, column=11)
            total_price = fee.get("total_price", fee.get("totalPrice", 0))
            cell_k.value = f"{float(total_price):.2f}" if total_price else "0.00"

            # L列：备注（单独列，不合并）
            cell_l = ws.cell(row=row_idx, column=12)
            cell_l.value = fee.get("remark", "")

            logger.debug(f"填充其他费用行 {row_idx}: {fee.get('fee_name', fee.get('feeName', ''))}")

        except Exception as e:
            logger.error(f"填充其他费用行 {row_idx} 失败: {e}")
            # 如果合并单元格操作失败，尝试直接填充数据
            try:
                ws.cell(row=row_idx, column=3).value = fee.get("fee_name", fee.get("feeName", ""))
                ws.cell(row=row_idx, column=5).value = f"{float(fee.get('unit_price', fee.get('unitPrice', 0))):.2f}"
                ws.cell(row=row_idx, column=8).value = fee.get("quantity", 1)
                ws.cell(row=row_idx, column=11).value = f"{float(fee.get('total_price', fee.get('totalPrice', 0))):.2f}"
                ws.cell(row=row_idx, column=12).value = fee.get("remark", "")
            except Exception as fallback_e:
                logger.error(f"其他费用行 {row_idx} 回退填充也失败: {fallback_e}")

    def _ensure_other_fee_merge(self, ws, row_idx: int, start_col: int, end_col: int, field_name: str):
        """确保其他费用的合并单元格存在"""
        # 如果开始列和结束列相同，不需要合并
        if start_col == end_col:
            return

        # 检查是否已经合并
        is_already_merged = False
        for merged_range in ws.merged_cells.ranges:
            if (
                merged_range.min_row == row_idx
                and merged_range.max_row == row_idx
                and merged_range.min_col == start_col
                and merged_range.max_col == end_col
            ):
                is_already_merged = True
                break

        if not is_already_merged:
            try:
                # 检查目标区域是否有其他合并单元格冲突
                conflicting_ranges = []
                for merged_range in list(ws.merged_cells.ranges):
                    if (
                        merged_range.min_row == row_idx
                        and merged_range.max_row == row_idx
                        and not (merged_range.max_col < start_col or merged_range.min_col > end_col)
                    ):
                        conflicting_ranges.append(merged_range)

                # 删除冲突的合并单元格
                for conflicting_range in conflicting_ranges:
                    ws.unmerge_cells(str(conflicting_range))
                    logger.debug(f"删除冲突的合并单元格: {conflicting_range}")

                # 创建新的合并单元格
                ws.merge_cells(start_row=row_idx, start_column=start_col, end_row=row_idx, end_column=end_col)
                logger.debug(f"创建其他费用合并单元格: {field_name} 行{row_idx} 列{start_col}-{end_col}")
            except Exception as e:
                logger.warning(f"创建其他费用合并单元格失败 {field_name}: {e}")

    def _get_merged_ranges_for_row(self, ws, row_idx: int) -> List[Dict]:
        """获取指定行的合并单元格信息"""
        merged_ranges = []
        for merged_range in ws.merged_cells.ranges:
            if merged_range.min_row <= row_idx <= merged_range.max_row:
                merged_ranges.append(
                    {
                        "min_row": merged_range.min_row,
                        "max_row": merged_range.max_row,
                        "min_col": merged_range.min_col,
                        "max_col": merged_range.max_col,
                        "range_str": str(merged_range),
                    }
                )
        return merged_ranges

    def _copy_merged_ranges(self, ws, merged_ranges: List[Dict], target_row: int, source_row: int):
        """复制合并单元格到目标行"""
        for merged_range in merged_ranges:
            # 计算行偏移
            row_offset = target_row - source_row

            # 计算新的合并范围
            new_min_row = merged_range["min_row"] + row_offset
            new_max_row = merged_range["max_row"] + row_offset

            # 只复制单行的合并单元格（跨行的合并单元格不复制）
            if merged_range["min_row"] == merged_range["max_row"] and new_min_row == target_row:
                # 检查是否已经存在相同的合并单元格
                range_exists = False
                for existing_range in ws.merged_cells.ranges:
                    if (
                        existing_range.min_row == new_min_row
                        and existing_range.max_row == new_max_row
                        and existing_range.min_col == merged_range["min_col"]
                        and existing_range.max_col == merged_range["max_col"]
                    ):
                        range_exists = True
                        break

                if not range_exists:
                    try:
                        # 创建新的合并单元格
                        ws.merge_cells(
                            start_row=new_min_row,
                            start_column=merged_range["min_col"],
                            end_row=new_max_row,
                            end_column=merged_range["max_col"],
                        )
                        logger.debug(
                            f"复制合并单元格: 行{new_min_row} 列{merged_range['min_col']}-{merged_range['max_col']}"
                        )
                    except Exception as e:
                        logger.warning(f"合并单元格失败: {e}")
                else:
                    logger.debug(
                        f"合并单元格已存在，跳过: 行{new_min_row} 列{merged_range['min_col']}-{merged_range['max_col']}"
                    )

    def _render_fee_summary(self, ws, template_data: Dict[str, Any], start_row: int, end_row: int):
        """渲染费用汇总"""

        # 先渲染基本信息（使用原有的基本信息渲染逻辑）
        self._render_basic_info(ws, template_data, start_row, end_row)

        # 然后恢复和修正特定的合并单元格（如监测费小计、优惠后小计等）
        self._restore_fee_summary_merged_cells(ws, template_data, 8, end_row)

        # # 查找并替换费用汇总模板变量
        # for row in ws.iter_rows():
        #     for cell in row:
        #         if cell.value and isinstance(cell.value, str):
        #             # 使用Jinja2渲染模板变量
        #             if "{{" in cell.value and "}}" in cell.value:
        #                 try:
        #                     template = Template(cell.value)
        #                     cell.value = template.render(**template_data)
        #                 except Exception as e:
        #                     logger.warning(f"费用汇总模板渲染失败: {cell.value}, 错误: {e}")
        #                     # 如果渲染失败，保持原值或设置为空
        #                     cell.value = ""
        #
        # for row_idx, row in enumerate(ws.iter_rows(), 1):
        #     if row_idx != marker_row:

    def _save_all_merged_ranges(self, ws) -> List[Dict]:
        """保存工作表中所有的合并单元格信息"""
        merged_ranges = []
        for merged_range in ws.merged_cells.ranges:
            merged_ranges.append(
                {
                    "min_row": merged_range.min_row,
                    "max_row": merged_range.max_row,
                    "min_col": merged_range.min_col,
                    "max_col": merged_range.max_col,
                    "range_str": str(merged_range),
                }
            )
        return merged_ranges

    def _restore_fee_summary_merged_cells(self, ws, template_data: Dict[str, Any], start_row: int, end_row: int):
        """恢复费用汇总区域的特定合并单元格"""
        # 定义需要特别处理的合并单元格（根据您的需求）
        special_merges = [
            {
                "keywords": ["监测费小计", "检测费小计", "检测费用合计"],
                "cols": 10,
                "alignment": "center",
            },  # 占用10列，居中
            {"keywords": ["优惠后小计"], "cols": 9, "alignment": "center"},  # 占用10列，居中
            {"keywords": ["费用名称"], "cols": 2, "alignment": "center"},  # 占用2列，居中
            {"keywords": ["单价", "数量"], "cols": 3, "alignment": "center"},  # 占用3列，居中
            {"keywords": ["税率:"], "cols": 8, "alignment": "center"},  # 税率占用8列，居中
            {"keywords": ["合计（含税）"], "cols": 10, "alignment": "center"},  # 总计相关，居中
            {"keywords": ["优惠后合计：", "汇款帐号："], "cols": 2, "alignment": "left"},  # 左对齐
            {"keywords": ["优惠后总金额（大写）"], "cols": 8, "alignment": "center"},  # 大写金额占用8列，居中
            {"keywords": ["浙江求实环境监测有限公司"], "cols": 10, "alignment": "center"},  # 居中
            {"keywords": ["本报价单的有效期为60天，从报价之日起计算。"], "cols": 12, "alignment": "center"},  # 左对齐
        ]

        # 处理大写金额替换
        self._process_chinese_currency_amount(ws, template_data, start_row, end_row)

        # 遍历费用汇总区域的所有行
        for row_idx in range(start_row, end_row + 1):
            # 检查每一行是否包含需要特殊处理的文本
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                if cell.value and isinstance(cell.value, str):
                    cell_text = str(cell.value).strip()

                    # 检查是否匹配特殊合并规则
                    for special_merge in special_merges:
                        for keyword in special_merge["keywords"]:
                            if keyword in cell_text:
                                # 检查是否已经合并
                                is_already_merged = False
                                for merged_range in ws.merged_cells.ranges:
                                    if (
                                        merged_range.min_row == row_idx
                                        and merged_range.min_col <= col_idx <= merged_range.max_col
                                    ):
                                        is_already_merged = True
                                        break

                                if not is_already_merged:
                                    # 创建特定的合并单元格
                                    try:
                                        end_col = min(col_idx + special_merge["cols"] - 1, ws.max_column)
                                        if end_col > col_idx:  # 确保有多列需要合并
                                            ws.merge_cells(
                                                start_row=row_idx,
                                                start_column=col_idx,
                                                end_row=row_idx,
                                                end_column=end_col,
                                            )

                                            # 设置对齐方式
                                            alignment_type = special_merge.get("alignment", "center")
                                            from openpyxl.styles import Alignment

                                            if alignment_type == "center":
                                                cell.alignment = Alignment(horizontal="center", vertical="center")
                                            elif alignment_type == "left":
                                                cell.alignment = Alignment(horizontal="left", vertical="center")

                                            logger.debug(
                                                f"恢复特殊合并单元格: '{keyword}' 行{row_idx} 列{col_idx}-{end_col}, 对齐: {alignment_type}"
                                            )
                                    except Exception as e:
                                        logger.warning(f"恢复特殊合并单元格失败 '{keyword}': {e}")
                                break
                        else:
                            continue
                        break  # 找到匹配的关键词后跳出循环

        logger.debug(f"费用汇总区域 行{start_row}-{end_row} 的特定合并单元格恢复完成")

    def _update_merged_ranges_after_detail_insert(self, ws, rows_inserted: int):
        """在检测项目明细插入行后更新合并单元格"""
        if rows_inserted <= 0:
            return

        # 找到$样品类别标记的位置，这是插入的起始位置
        detail_start_row = None
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for cell in row:
                if cell.value == "$样品类别":
                    detail_start_row = row_idx
                    break
            if detail_start_row:
                break

        if not detail_start_row:
            logger.warning("未找到$样品类别标记，无法更新合并单元格")
            return

        # 更新所有在插入点之后的合并单元格
        self._shift_merged_ranges_down(ws, detail_start_row, rows_inserted)
        logger.debug(f"检测项目明细插入 {rows_inserted} 行后，更新了合并单元格位置")

    def _update_merged_ranges_after_other_insert(self, ws, rows_inserted: int):
        """在其他费用插入行后更新合并单元格"""
        if rows_inserted <= 0:
            return

        # 找到$差旅费标记的位置，这是插入的起始位置
        other_start_row = None
        for row_idx, row in enumerate(ws.iter_rows(), 1):
            for cell in row:
                if cell.value == "$差旅费":
                    other_start_row = row_idx
                    break
            if other_start_row:
                break

        if not other_start_row:
            logger.warning("未找到$差旅费标记，无法更新合并单元格")
            return

        # 更新所有在插入点之后的合并单元格
        self._shift_merged_ranges_down(ws, other_start_row, rows_inserted)
        logger.debug(f"其他费用插入 {rows_inserted} 行后，更新了合并单元格位置")

    def _shift_merged_ranges_down(self, ws, insert_after_row: int, rows_to_shift: int):
        """将指定行之后的所有合并单元格向下移动"""
        # 收集需要更新的合并单元格
        ranges_to_update = []
        ranges_to_remove = []

        for merged_range in list(ws.merged_cells.ranges):
            if merged_range.min_row > insert_after_row:
                # 需要向下移动的合并单元格
                ranges_to_update.append(
                    {
                        "old_range": merged_range,
                        "new_min_row": merged_range.min_row + rows_to_shift,
                        "new_max_row": merged_range.max_row + rows_to_shift,
                        "min_col": merged_range.min_col,
                        "max_col": merged_range.max_col,
                    }
                )
                ranges_to_remove.append(merged_range)

        # 移除旧的合并单元格
        for merged_range in ranges_to_remove:
            try:
                ws.unmerge_cells(str(merged_range))
            except Exception as e:
                logger.warning(f"取消合并单元格失败: {e}")

        # 创建新的合并单元格
        for update_info in ranges_to_update:
            try:
                ws.merge_cells(
                    start_row=update_info["new_min_row"],
                    start_column=update_info["min_col"],
                    end_row=update_info["new_max_row"],
                    end_column=update_info["max_col"],
                )
                logger.debug(
                    f"移动合并单元格: 从行{update_info['old_range'].min_row}-{update_info['old_range'].max_row} 到行{update_info['new_min_row']}-{update_info['new_max_row']}"
                )
            except Exception as e:
                logger.warning(f"重新创建合并单元格失败: {e}")

    def _render_method_overview_sheet(self, wb, template_data: Dict[str, Any]):
        """
        渲染方法-一览表sheet

        :param wb: 工作簿对象
        :param template_data: 模板数据
        """
        # 检查是否存在"方法-一览表"sheet
        method_sheet_name = "方法-一览表"
        if method_sheet_name not in wb.sheetnames:
            logger.warning(f"模板中不存在'{method_sheet_name}'工作表，跳过渲染")
            return

        # 获取方法-一览表工作表
        method_ws = wb[method_sheet_name]
        logger.debug(f"开始渲染'{method_sheet_name}'工作表")

        # 获取报价明细数据
        items = template_data.get("items", [])
        if not items:
            logger.warning("没有报价明细数据，跳过方法-一览表渲染")
            return

        # 准备方法一览表数据
        method_overview_data = self._prepare_method_overview_data(items)

        # 渲染表头-不需要-模版中已经有了
        # 渲染数据行（从第2行开始）
        for row_idx, method_data in enumerate(method_overview_data, 2):
            for col_idx, value in enumerate(
                [
                    method_data["序号"],
                    method_data["检测类别"],
                    method_data["检测项目"],
                    method_data["检测方法"],
                    method_data["是否盖章"],
                    method_data["是否分包"],
                ],
                1,
            ):
                cell = method_ws.cell(row=row_idx, column=col_idx)
                cell.value = value
                # 设置数据行样式
                cell.font = Font(name="宋体", size=11)
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = Border(
                    left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
                )

        logger.debug(f"方法-一览表渲染完成，共渲染 {len(method_overview_data)} 行数据")

    def _number_to_chinese_currency(self, amount: float) -> str:
        """
        将数字转换为中文大写金额

        :param amount: 金额数字
        :return: 中文大写金额字符串
        """
        if amount == 0:
            return "零元整"

        # 中文数字
        chinese_numbers = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"]
        # 单位
        units = ["", "拾", "佰", "仟"]
        big_units = ["", "万", "亿"]

        # 处理负数
        if amount < 0:
            return "负" + self._number_to_chinese_currency(-amount)

        # 分离整数和小数部分
        yuan = int(amount)
        jiao = int((amount - yuan) * 10)
        fen = int(((amount - yuan) * 100) % 10)

        result = ""

        # 处理元的部分
        if yuan == 0:
            result = "零元"
        else:
            yuan_str = str(yuan)
            yuan_len = len(yuan_str)

            for i, digit in enumerate(yuan_str):
                digit_val = int(digit)
                pos = yuan_len - i - 1  # 从右往左的位置

                if digit_val != 0:
                    result += chinese_numbers[digit_val]
                    if pos % 4 != 0:  # 不是万、亿位
                        result += units[pos % 4]
                elif pos % 4 == 0 and pos > 0:  # 万、亿位为0
                    if result and result[-1] != "零":
                        result += "零"
                else:  # 其他位为0
                    if result and result[-1] != "零":
                        result += "零"

                # 添加万、亿单位
                if pos % 4 == 0 and pos > 0:
                    big_unit_index = pos // 4
                    if big_unit_index < len(big_units):
                        result += big_units[big_unit_index]

            # 去除末尾的零
            result = result.rstrip("零") + "元"

        # 处理角分
        if jiao == 0 and fen == 0:
            result += "整"
        else:
            if jiao != 0:
                result += chinese_numbers[jiao] + "角"
            if fen != 0:
                if jiao == 0:
                    result += "零"
                result += chinese_numbers[fen] + "分"

        return result

    def _process_chinese_currency_amount(self, ws, template_data: Dict[str, Any], start_row: int, end_row: int):
        """
        处理大写金额替换

        :param ws: 工作表对象
        :param template_data: 模板数据
        :param start_row: 开始行
        :param end_row: 结束行
        """
        # 获取最终金额
        final_amount = template_data.get("finalAmount", 0)
        if isinstance(final_amount, str):
            try:
                final_amount = float(final_amount)
            except (ValueError, TypeError):
                final_amount = 0

        # 转换为中文大写
        chinese_amount = self._number_to_chinese_currency(final_amount)

        # 在费用汇总区域查找并替换大写金额
        for row_idx in range(start_row, end_row + 1):
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                if cell.value and isinstance(cell.value, str):
                    cell_text = str(cell.value).strip()

                    # 检查是否包含大写金额相关的关键词
                    if "优惠后总金额（大写）" in cell_text or "大写金额" in cell_text:
                        logger.debug(f"找到大写金额标题: 行{row_idx} 列{col_idx} = {cell_text}")

                        # 策略1：尝试在标题后面创建8列合并单元格
                        success = self._try_set_chinese_amount_after_title(ws, chinese_amount, row_idx, col_idx)

                        if not success:
                            # 策略2：尝试在同行其他位置设置
                            success = self._try_set_chinese_amount_same_row(ws, chinese_amount, row_idx)

                        if not success:
                            # 策略3：使用回退方案
                            logger.warning("无法在标题附近设置大写金额，使用回退方案")
                            self._fallback_set_chinese_amount(ws, chinese_amount, row_idx, end_row)

                        break

                    # 也可以直接替换包含finalAmount的模板变量
                    if "{{finalAmount}}" in cell_text:
                        try:
                            # 检查当前单元格是否是合并单元格的一部分
                            if self._is_merged_cell_main(ws, row_idx, col_idx):
                                cell.value = cell_text.replace("{{finalAmount}}", chinese_amount)
                                logger.debug(f"替换finalAmount模板变量: 行{row_idx} 列{col_idx} = {cell.value}")
                            else:
                                logger.warning(f"跳过非主合并单元格的finalAmount替换: 行{row_idx} 列{col_idx}")
                        except Exception as e:
                            logger.warning(f"替换finalAmount模板变量失败: {e}")

        logger.debug(f"大写金额处理完成: {final_amount} -> {chinese_amount}")

    def _clear_conflicting_merges(self, ws, row_idx: int, start_col: int, end_col: int):
        """清除指定区域内冲突的合并单元格"""
        conflicting_ranges = []
        for merged_range in list(ws.merged_cells.ranges):
            if (
                merged_range.min_row == row_idx
                and merged_range.max_row == row_idx
                and not (merged_range.max_col < start_col or merged_range.min_col > end_col)
            ):
                conflicting_ranges.append(merged_range)

        # 删除冲突的合并单元格
        for conflicting_range in conflicting_ranges:
            ws.unmerge_cells(str(conflicting_range))
            logger.debug(f"清除冲突的合并单元格: {conflicting_range}")

    def _is_merged_cell_main(self, ws, row_idx: int, col_idx: int) -> bool:
        """检查单元格是否是合并单元格的主单元格（左上角）"""
        for merged_range in ws.merged_cells.ranges:
            if (
                merged_range.min_row <= row_idx <= merged_range.max_row
                and merged_range.min_col <= col_idx <= merged_range.max_col
            ):
                # 如果是合并单元格的左上角，则是主单元格
                return merged_range.min_row == row_idx and merged_range.min_col == col_idx
        # 如果不在任何合并单元格中，则是普通单元格，可以写入
        return True

    def _is_cell_writable(self, ws, row_idx: int, col_idx: int) -> bool:
        """检查单元格是否可写入（不是合并单元格的非主单元格）"""
        for merged_range in ws.merged_cells.ranges:
            if (
                merged_range.min_row <= row_idx <= merged_range.max_row
                and merged_range.min_col <= col_idx <= merged_range.max_col
            ):
                # 如果是合并单元格的左上角，则可以写入
                return merged_range.min_row == row_idx and merged_range.min_col == col_idx
        # 如果不在任何合并单元格中，则是普通单元格，可以写入
        return True

    def _fallback_set_chinese_amount(self, ws, chinese_amount: str, start_row: int, end_row: int):
        """回退方案：在指定区域查找可写入的单元格设置大写金额"""
        logger.debug(f"使用回退方案设置大写金额，搜索区域: 行{start_row}-{end_row}")

        # 策略1：查找完全空白的可写入单元格
        for row_idx in range(start_row, end_row + 1):
            for col_idx in range(1, min(ws.max_column + 1, 20)):  # 扩大搜索范围到20列
                cell = ws.cell(row=row_idx, column=col_idx)
                if not cell.value or str(cell.value).strip() == "":
                    if self._is_cell_writable(ws, row_idx, col_idx):
                        try:
                            cell.value = f"大写金额: {chinese_amount}"
                            from openpyxl.styles import Alignment

                            cell.alignment = Alignment(horizontal="left", vertical="center")
                            logger.debug(f"回退设置大写金额(策略1): 行{row_idx} 列{col_idx} = {chinese_amount}")
                            return
                        except Exception as e:
                            logger.debug(f"回退设置失败(策略1): 行{row_idx} 列{col_idx}, 错误: {e}")
                            continue

        # 策略2：在指定区域后面查找空行
        for row_idx in range(end_row + 1, min(end_row + 10, ws.max_row + 1)):
            for col_idx in range(1, 11):  # 前10列
                cell = ws.cell(row=row_idx, column=col_idx)
                if not cell.value or str(cell.value).strip() == "":
                    if self._is_cell_writable(ws, row_idx, col_idx):
                        try:
                            cell.value = f"大写金额: {chinese_amount}"
                            from openpyxl.styles import Alignment

                            cell.alignment = Alignment(horizontal="left", vertical="center")
                            logger.debug(f"回退设置大写金额(策略2): 行{row_idx} 列{col_idx} = {chinese_amount}")
                            return
                        except Exception as e:
                            logger.debug(f"回退设置失败(策略2): 行{row_idx} 列{col_idx}, 错误: {e}")
                            continue

        # 策略3：强制在新行创建
        try:
            new_row = end_row + 1
            if new_row <= ws.max_row:
                cell = ws.cell(row=new_row, column=1)
                cell.value = f"大写金额: {chinese_amount}"
                from openpyxl.styles import Alignment

                cell.alignment = Alignment(horizontal="left", vertical="center")
                logger.debug(f"回退设置大写金额(策略3): 行{new_row} 列1 = {chinese_amount}")
                return
        except Exception as e:
            logger.warning(f"回退设置失败(策略3): {e}")

        logger.error("所有回退策略都失败，无法设置大写金额")

    def _try_set_chinese_amount_after_title(self, ws, chinese_amount: str, row_idx: int, title_col_idx: int) -> bool:
        """尝试在标题后面设置大写金额（8列合并）"""
        target_start_col = title_col_idx + 1
        target_end_col = min(target_start_col + 6, ws.max_column)  # 占用8列

        # 检查目标区域是否都可写入
        for check_col in range(target_start_col, target_end_col + 1):
            if not self._is_cell_writable(ws, row_idx, check_col):
                logger.debug(f"目标区域不可写入: 行{row_idx} 列{check_col}")
                return False

        try:
            # 确保目标区域没有冲突的合并单元格
            self._clear_conflicting_merges(ws, row_idx, target_start_col, target_end_col)

            # 创建8列合并单元格
            if target_end_col > target_start_col:
                ws.merge_cells(
                    start_row=row_idx,
                    start_column=target_start_col,
                    end_row=row_idx,
                    end_column=target_end_col,
                )
                logger.debug(f"创建大写金额合并单元格: 行{row_idx} 列{target_start_col}-{target_end_col}")

            # 设置大写金额到合并区域的主单元格
            target_cell = ws.cell(row=row_idx, column=target_start_col)
            target_cell.value = chinese_amount

            # 设置居中对齐
            from openpyxl.styles import Alignment

            target_cell.alignment = Alignment(horizontal="center", vertical="center")

            logger.debug(f"成功设置大写金额: 行{row_idx} 列{target_start_col} = {chinese_amount}")
            return True

        except Exception as e:
            logger.warning(f"在标题后设置大写金额失败: {e}")
            return False

    def _try_set_chinese_amount_same_row(self, ws, chinese_amount: str, row_idx: int) -> bool:
        """尝试在同行其他位置设置大写金额"""
        # 从右往左查找可写入的位置
        for col_idx in range(ws.max_column, 0, -1):
            if self._is_cell_writable(ws, row_idx, col_idx):
                cell = ws.cell(row=row_idx, column=col_idx)
                if not cell.value or str(cell.value).strip() == "":
                    try:
                        cell.value = f"大写金额: {chinese_amount}"
                        from openpyxl.styles import Alignment

                        cell.alignment = Alignment(horizontal="left", vertical="center")
                        logger.debug(f"在同行设置大写金额: 行{row_idx} 列{col_idx} = {chinese_amount}")
                        return True
                    except Exception as e:
                        logger.debug(f"在同行设置大写金额失败: 行{row_idx} 列{col_idx}, 错误: {e}")
                        continue
        return False

    def _process_other_fees_title_merge(self, ws, fee_count: int, start_row: int):
        """
        处理"二、其他费用"标题的动态合并

        :param ws: 工作表对象
        :param fee_count: 其他费用的数量
        :param start_row: 其他费用开始行
        """
        if fee_count <= 0:
            return

        # 查找"二、其他费用"标题
        title_row = None
        title_col = None

        # 在其他费用区域之前查找标题
        search_start_row = max(1, start_row - 10)  # 向前搜索10行
        search_end_row = start_row

        for row_idx in range(search_start_row, search_end_row + 1):
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                if cell.value and isinstance(cell.value, str):
                    cell_text = str(cell.value).strip()
                    if "二、其他费用" in cell_text or "其他费用" in cell_text:
                        title_row = row_idx
                        title_col = col_idx
                        logger.debug(f"找到其他费用标题: 行{title_row} 列{title_col} = {cell_text}")
                        break
            if title_row:
                break

        if not title_row or not title_col:
            logger.warning("未找到'二、其他费用'标题，跳过动态合并")
            return

        # 计算需要合并的行数（标题行 + 费用行数）
        merge_rows = fee_count
        end_row = title_row + merge_rows - 1

        # 确保不超出工作表范围
        if end_row > ws.max_row:
            end_row = ws.max_row
            merge_rows = end_row - title_row + 1

        # 清除可能存在的冲突合并单元格
        conflicting_ranges = []
        for merged_range in list(ws.merged_cells.ranges):
            if merged_range.min_col == title_col and not (
                merged_range.max_row < title_row or merged_range.min_row > end_row
            ):
                conflicting_ranges.append(merged_range)

        # 删除冲突的合并单元格
        for conflicting_range in conflicting_ranges:
            ws.unmerge_cells(str(conflicting_range))
            logger.debug(f"删除冲突的其他费用标题合并单元格: {conflicting_range}")

        # 创建新的合并单元格（垂直合并）
        if merge_rows > 1:
            try:
                ws.merge_cells(start_row=title_row, start_column=title_col, end_row=end_row + 1, end_column=title_col)

                # 设置垂直居中对齐
                title_cell = ws.cell(row=title_row, column=title_col)
                from openpyxl.styles import Alignment

                title_cell.alignment = Alignment(horizontal="center", vertical="center")

                logger.debug(f"创建其他费用标题合并单元格: 行{title_row}-{end_row} 列{title_col} (共{merge_rows}行)")

            except Exception as e:
                logger.warning(f"创建其他费用标题合并单元格失败: {e}")
        else:
            logger.debug(f"其他费用只有1行，无需合并标题单元格")

    def _prepare_method_overview_data(self, items: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """
        准备方法-一览表数据

        :param items: 报价明细数据
        :return: 方法一览表数据列表
        """
        method_overview_data = []
        seen_methods = set()  # 用于去重

        for item in items:
            # 构造唯一标识，用于去重
            method_key = (item.get("category", ""), item.get("parameter", ""), item.get("method", ""))

            # 如果已经存在相同的方法，跳过
            if method_key in seen_methods:
                continue

            seen_methods.add(method_key)

            # 判断是否盖章（从模板数据中获取资质信息）
            qualification_code = item.get("qualification_code", "")
            is_stamped = self._check_qualification_status(qualification_code, item)

            # 判断是否分包
            is_subcontract = item.get("is_subcontract", "0")
            is_subcontract_text = "是" if is_subcontract == "1" else "否"

            method_data = {
                "序号": str(len(method_overview_data) + 1),
                "检测类别": item.get("category", ""),
                "检测项目": "、".join(item.get("parameters", [])),
                "检测方法": item.get("method", ""),
                "是否盖章": is_stamped,
                "是否分包": is_subcontract_text,
            }

            method_overview_data.append(method_data)

        return method_overview_data

    def _check_qualification_status(self, qualification_code: str, item: Dict[str, Any]) -> str:
        """
        检查资质状态，判断是否盖章

        :param qualification_code: 资质唯一编号
        :param item: 报价明细项目数据
        :return: "是" 或 "否"
        """
        if not qualification_code or qualification_code.strip() == "":
            return "否"

        # 从item中获取预先查询好的资质信息
        has_qualification = item.get("has_qualification", "1")  # 默认为"1"（无资质）

        # has_qualification: "0"=有资质，"1"=无资质
        return "是" if has_qualification == "0" else "否"
