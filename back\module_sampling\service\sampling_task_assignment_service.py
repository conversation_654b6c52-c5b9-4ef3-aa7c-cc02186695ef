#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样任务分配服务
"""

import time
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
from module_sampling.entity.vo.sampling_task_assignment_vo import (
    SamplingTaskAssignmentModel,
    ProjectQuotationItemCycleModel,
    CycleItemModel,
    SamplingTaskAssignmentResponseModel
)
from utils.common_util import CamelCaseUtil
from exceptions.exception import ServiceException


class SamplingTaskAssignmentService:
    """
    采样任务分配服务
    """

    def __init__(self, db: AsyncSession):
        """
        初始化

        :param db: 数据库会话
        """
        self.db = db

    async def get_quotation_cycle_items(self, quotation_id: int) -> List[Dict[str, Any]]:
        """
        获取项目报价的检测周期条目，按检测项目分组

        :param quotation_id: 项目报价ID
        :return: 检测周期条目列表
        """
        # 查询项目报价明细
        items_stmt = (
            select(ProjectQuotationItem)
            .where(ProjectQuotationItem.project_quotation_id == quotation_id)
            .order_by(ProjectQuotationItem.id)
        )
        items_result = await self.db.execute(items_stmt)
        quotation_items = items_result.scalars().all()

        result = []
        for item in quotation_items:
            # 查询该明细的检测周期条目
            cycle_items_stmt = (
                select(DetectionCycleItem)
                .where(DetectionCycleItem.project_quotation_item_id == item.id)
                .order_by(DetectionCycleItem.cycle_number)
            )
            cycle_items_result = await self.db.execute(cycle_items_stmt)
            cycle_items = cycle_items_result.scalars().all()

            # 构建周期条目列表
            cycle_item_models = []
            for i, cycle_item in enumerate(cycle_items):
                # 判断是否可选择：未分配状态且前面的周期都已分配
                is_selectable = False
                if cycle_item.status == 0:  # 未分配
                    # 检查前面的周期是否都已分配
                    if i == 0:  # 第一个周期总是可选
                        is_selectable = True
                    else:
                        # 检查前面的周期是否都已分配
                        prev_cycles_assigned = all(
                            prev_cycle.status >= 1 for prev_cycle in cycle_items[:i]
                        )
                        is_selectable = prev_cycles_assigned

                cycle_item_models.append(CycleItemModel(
                    id=cycle_item.id,
                    cycle_number=cycle_item.cycle_number,
                    status=cycle_item.status,
                    status_label=cycle_item.status_label,
                    is_selectable=is_selectable
                ))

            # 构建项目明细模型
            # 处理空的cycle_type，提供默认值
            cycle_type = item.cycle_type if item.cycle_type and item.cycle_type.strip() else "常规"
            
            item_model = ProjectQuotationItemCycleModel(
                item_id=item.id,
                item_code=item.item_code,
                category=item.category,
                parameter=item.parameter,
                method=item.method,
                cycle_type=cycle_type,
                cycle_count=item.cycle_count,
                cycle_items=cycle_item_models
            )
            result.append(item_model.dict())

        return CamelCaseUtil.transform_result(result)

    async def create_sampling_task(
        self, 
        assignment_data: SamplingTaskAssignmentModel, 
        user_id: int
    ) -> SamplingTaskAssignmentResponseModel:
        """
        创建采样任务

        :param assignment_data: 采样任务分配数据
        :param user_id: 当前用户ID
        :return: 创建结果
        """
        # 验证选中的检测周期条目
        await self._validate_cycle_items(assignment_data.selected_cycle_item_ids)

        # 生成任务编号
        task_number = await self._generate_task_number()

        # 创建采样任务
        sampling_task = SamplingTask(
            task_code=task_number,
            task_name=assignment_data.task_name,
            project_quotation_id=assignment_data.project_quotation_id,
            assigned_user_id=assignment_data.assigned_user_id,
            status=0,  # 0-待执行
            planned_start_date=assignment_data.planned_start_time.date() if assignment_data.planned_start_time else None,
            planned_end_date=assignment_data.planned_end_time.date() if assignment_data.planned_end_time else None,
            description=assignment_data.task_description,
            create_by=user_id,
            create_time=datetime.now(),
            update_by=user_id,
            update_time=datetime.now()
        )

        self.db.add(sampling_task)
        await self.db.flush()  # 获取生成的ID

        # 创建采样任务与检测周期条目的关联
        for cycle_item_id in assignment_data.selected_cycle_item_ids:
            task_cycle_relation = SamplingTaskCycleItem(
                sampling_task_id=sampling_task.id,
                detection_cycle_item_id=cycle_item_id,
                create_by=user_id,
                create_time=datetime.now()
            )
            self.db.add(task_cycle_relation)

        # 更新检测周期条目状态为已分配
        cycle_items_stmt = (
            select(DetectionCycleItem)
            .where(DetectionCycleItem.id.in_(assignment_data.selected_cycle_item_ids))
        )
        cycle_items_result = await self.db.execute(cycle_items_stmt)
        cycle_items = cycle_items_result.scalars().all()

        for cycle_item in cycle_items:
            cycle_item.status = 1  # 已分配
            cycle_item.update_by = user_id
            cycle_item.update_time = datetime.now()

        await self.db.commit()

        return SamplingTaskAssignmentResponseModel(
            task_id=sampling_task.id,
            task_number=task_number,
            task_name=assignment_data.task_name,
            cycle_item_count=len(assignment_data.selected_cycle_item_ids),
            message=f"成功创建采样任务，关联了{len(assignment_data.selected_cycle_item_ids)}个检测周期条目"
        )

    async def _validate_cycle_items(self, cycle_item_ids: List[int]):
        """
        验证选中的检测周期条目

        :param cycle_item_ids: 检测周期条目ID列表
        """
        if not cycle_item_ids:
            raise ServiceException(message="请至少选择一个检测周期条目")

        # 查询检测周期条目
        cycle_items_stmt = (
            select(DetectionCycleItem)
            .where(DetectionCycleItem.id.in_(cycle_item_ids))
            .order_by(DetectionCycleItem.project_quotation_item_id, DetectionCycleItem.cycle_number)
        )
        cycle_items_result = await self.db.execute(cycle_items_stmt)
        cycle_items = cycle_items_result.scalars().all()

        if len(cycle_items) != len(cycle_item_ids):
            raise ServiceException(message="部分检测周期条目不存在")

        # 验证状态和顺序
        item_cycles = {}
        for cycle_item in cycle_items:
            if cycle_item.status != 0:
                raise ServiceException(message=f"检测周期条目{cycle_item.id}已被分配，无法重复分配")

            item_id = cycle_item.project_quotation_item_id
            if item_id not in item_cycles:
                item_cycles[item_id] = []
            item_cycles[item_id].append(cycle_item.cycle_number)

        # 验证每个检测项目的周期选择是否连续
        for item_id, cycles in item_cycles.items():
            cycles.sort()
            # 检查是否从1开始且连续
            if cycles[0] != 1:
                raise ServiceException(message=f"检测项目{item_id}的周期必须从第1周期开始选择")
            
            for i in range(1, len(cycles)):
                if cycles[i] != cycles[i-1] + 1:
                    raise ServiceException(message=f"检测项目{item_id}的周期选择必须连续，不能跳过周期")

    async def _generate_task_number(self) -> str:
        """
        生成任务编号

        :return: 任务编号
        """
        # 获取当前日期
        now = datetime.now()
        date_str = now.strftime("%Y%m%d")
        
        # 查询当天已有的任务数量
        count_stmt = (
            select(SamplingTask)
            .where(SamplingTask.task_code.like(f"ST{date_str}%"))
        )
        count_result = await self.db.execute(count_stmt)
        count = len(count_result.scalars().all())
        
        # 生成序号
        sequence = str(count + 1).zfill(3)
        
        return f"ST{date_str}{sequence}"