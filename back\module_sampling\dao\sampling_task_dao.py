from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, asc, select, func
from datetime import datetime, date

from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_sampling.entity.do.detection_cycle_item_do import Detection<PERSON><PERSON>Item
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTask<PERSON>ycleItem
from module_sampling.entity.do.sampling_task_sequence_do import SamplingTaskSequence
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_admin.entity.do.user_do import SysUser


class SamplingTaskDao:
    """采样任务数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_sampling_task(self, sampling_task: SamplingTask) -> SamplingTask:
        """创建采样任务"""
        self.db.add(sampling_task)
        await self.db.flush()
        return sampling_task
    
    async def get_sampling_task_by_id(self, task_id: int) -> Optional[SamplingTask]:
        """根据ID获取采样任务"""
        result = await self.db.execute(
            select(SamplingTask).options(
                joinedload(SamplingTask.project_quotation),
                joinedload(SamplingTask.responsible_user)
            ).filter(SamplingTask.id == task_id)
        )
        return result.scalars().first()
    
    async def get_sampling_task_by_code(self, task_code: str) -> Optional[SamplingTask]:
        """根据任务编号获取采样任务"""
        result = await self.db.execute(
            select(SamplingTask).filter(SamplingTask.task_code == task_code)
        )
        return result.scalars().first()
    
    async def update_sampling_task(self, sampling_task: SamplingTask) -> SamplingTask:
        """更新采样任务"""
        await self.db.merge(sampling_task)
        await self.db.flush()
        return sampling_task
    
    async def delete_sampling_task(self, task_id: int) -> bool:
        """删除采样任务"""
        task = await self.get_sampling_task_by_id(task_id)
        if task:
            await self.db.delete(task)
            await self.db.flush()
            return True
        return False
    
    async def page_sampling_tasks(self, 
                               page: int = 1, 
                               size: int = 10,
                               task_name: Optional[str] = None,
                               task_code: Optional[str] = None,
                               project_quotation_id: Optional[int] = None,
                               responsible_user_id: Optional[int] = None,
                               status: Optional[int] = None,
                               planned_start_date_from: Optional[date] = None,
                               planned_start_date_to: Optional[date] = None,
                               planned_end_date_from: Optional[date] = None,
                               planned_end_date_to: Optional[date] = None) -> Tuple[List[SamplingTask], int]:
        """分页查询采样任务（只显示审批通过的项目报价）"""
        query = select(SamplingTask).options(
            joinedload(SamplingTask.project_quotation),
            joinedload(SamplingTask.responsible_user)
        ).join(ProjectQuotation, SamplingTask.project_quotation_id == ProjectQuotation.id)
        
        # 构建查询条件
        conditions = [
            # 只显示审批通过的项目报价（状态为2）
            ProjectQuotation.status == "2"
        ]
        
        if task_name:
            conditions.append(SamplingTask.task_name.like(f"%{task_name}%"))
        
        if task_code:
            conditions.append(SamplingTask.task_code.like(f"%{task_code}%"))
        
        if project_quotation_id:
            conditions.append(SamplingTask.project_quotation_id == project_quotation_id)
        
        if responsible_user_id:
            conditions.append(SamplingTask.responsible_user_id == responsible_user_id)
        
        if status is not None:
            conditions.append(SamplingTask.status == status)
        
        if planned_start_date_from:
            conditions.append(SamplingTask.planned_start_date >= planned_start_date_from)
        
        if planned_start_date_to:
            conditions.append(SamplingTask.planned_start_date <= planned_start_date_to)
        
        if planned_end_date_from:
            conditions.append(SamplingTask.planned_end_date >= planned_end_date_from)
        
        if planned_end_date_to:
            conditions.append(SamplingTask.planned_end_date <= planned_end_date_to)
        
        query = query.filter(and_(*conditions))
        
        # 获取总数
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar_one()
        
        # 分页查询
        tasks_query = query.order_by(desc(SamplingTask.create_time)).offset((page - 1) * size).limit(size)
        tasks_result = await self.db.execute(tasks_query)
        tasks = tasks_result.scalars().unique().all()
        
        return tasks, total
    
    async def get_tasks_by_project_quotation_id(self, project_quotation_id: int) -> List[SamplingTask]:
        """根据项目报价ID获取采样任务列表"""
        result = await self.db.execute(
            select(SamplingTask).options(
                joinedload(SamplingTask.responsible_user)
            ).filter(SamplingTask.project_quotation_id == project_quotation_id)
        )
        return result.scalars().all()
    
    async def get_tasks_by_responsible_user_id(self, responsible_user_id: int) -> List[SamplingTask]:
        """根据负责人用户ID获取采样任务列表"""
        result = await self.db.execute(
            select(SamplingTask).options(
                joinedload(SamplingTask.project_quotation)
            ).filter(SamplingTask.responsible_user_id == responsible_user_id)
        )
        return result.scalars().all()
    
    async def create_task_cycle_relation(self, sampling_task_id: int, detection_cycle_item_id: int, create_by: int) -> SamplingTaskCycleItem:
        """创建采样任务与检测周期关联"""
        relation = SamplingTaskCycleItem(
            sampling_task_id=sampling_task_id,
            detection_cycle_item_id=detection_cycle_item_id,
            create_by=create_by
        )
        self.db.add(relation)
        await self.db.flush()
        return relation
    
    async def delete_task_cycle_relations(self, sampling_task_id: int) -> bool:
        """删除采样任务的所有周期关联"""
        relations_result = await self.db.execute(
            select(SamplingTaskCycleItem).filter(
                SamplingTaskCycleItem.sampling_task_id == sampling_task_id
            )
        )
        relations = relations_result.scalars().all()
        
        for relation in relations:
            await self.db.delete(relation)
        
        await self.db.flush()
        return True
    
    async def get_task_cycle_relations(self, sampling_task_id: int) -> List[SamplingTaskCycleItem]:
        """获取采样任务的周期关联"""
        result = await self.db.execute(
            select(SamplingTaskCycleItem).options(
                joinedload(SamplingTaskCycleItem.detection_cycle_item)
            ).filter(SamplingTaskCycleItem.sampling_task_id == sampling_task_id)
        )
        return result.scalars().all()
    
    async def generate_task_code(self) -> str:
        """生成任务编号
        格式：YYMMXXXX（8位数字）
        YY：年份后两位
        MM：月份两位
        XXXX：四位流水号（每月从0001开始）
        """
        now = datetime.now()
        year_month = now.strftime('%y%m')  # 年份后两位 + 月份两位
        
        # 获取或创建序列记录
        sequence_result = await self.db.execute(
            select(SamplingTaskSequence).filter(
                SamplingTaskSequence.year_month == year_month
            )
        )
        sequence = sequence_result.scalars().first()
        
        if not sequence:
            sequence = SamplingTaskSequence(year_month=year_month, sequence_number=0)
            self.db.add(sequence)
            await self.db.flush()
        
        # 递增序列号
        sequence.sequence_number += 1
        await self.db.merge(sequence)
        await self.db.flush()
        
        # 生成任务编号：YYMMXXXX（8位数字）
        task_code = f"{year_month}{sequence.sequence_number:04d}"
        return task_code