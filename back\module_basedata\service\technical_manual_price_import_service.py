"""
技术价格批量导入服务
"""

import io
from datetime import datetime
from typing import Optional, Any

import pandas as pd
from fastapi import UploadFile
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.do.technical_manual_price_do import TechnicalManualPrice
from module_basedata.entity.vo.technical_manual_price_import_vo import (
    TechnicalManualPriceImportModel,
    TechnicalManualPriceImportErrorModel,
    TechnicalManualPriceImportResultModel,
)
from utils.log_util import logger


class TechnicalManualPriceImportService:
    """
    技术价格批量导入服务
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def import_from_excel(
        self, file: UploadFile, current_user: CurrentUserModel
    ) -> TechnicalManualPriceImportResultModel:
        """
        从Excel文件导入技术价格数据

        :param file: 上传的Excel文件
        :param current_user: 当前用户
        :return: 导入结果
        """
        try:
            # 读取Excel文件
            content = await file.read()
            df = pd.read_excel(io.BytesIO(content))

            # 验证Excel格式
            required_columns = [
                "检测方法",
                "检测类别",
                "检测首项单价",
                "检测增项单价",
                "检测费上限",
                "采样单价",
                "前处理单价",
            ]

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return TechnicalManualPriceImportResultModel(
                    success=False,
                    total_count=0,
                    success_count=0,
                    error_count=1,
                    errors=[
                        TechnicalManualPriceImportErrorModel(
                            row_number=0,
                            error_message=f"Excel模板格式错误，缺少必填列：{', '.join(missing_columns)}",
                            method=None,
                            category=None,
                            first_item_price=None,
                            additional_item_price=None,
                            testing_fee_limit=None,
                            sampling_price=None,
                            pretreatment_price=None,
                        )
                    ],
                )

            # 处理数据
            total_count = len(df)
            success_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    # 验证和转换数据
                    import_data = await self._validate_and_convert_row(row, index + 2)

                    # 检查是否已存在价格记录
                    category_code, existing_price = await self._check_existing_price(
                        method=import_data.method,
                        category=import_data.category,
                    )
                    import_data.category_code = category_code
                    if existing_price:
                        # 更新现有记录
                        self._update_price_record(existing_price, import_data, current_user)
                    else:
                        # 创建新记录
                        price_record = self._create_price_record(import_data, current_user)
                        self.db.add(price_record)

                    success_count += 1

                except Exception as e:
                    errors.append(
                        TechnicalManualPriceImportErrorModel(
                            row_number=index + 2,
                            error_message=str(e),
                            method=str(row.get("检测方法", "")),
                            category=str(row.get("检测类别", "")),
                            first_item_price=self._safe_float(row.get("首项单价")),
                            additional_item_price=self._safe_float(row.get("增项单价")),
                            testing_fee_limit=self._safe_float(row.get("检测费上限")),
                            sampling_price=self._safe_float(row.get("采样单价")),
                            pretreatment_price=self._safe_float(row.get("前处理单价")),
                        )
                    )
                    logger.info("import from excel error: %s", str(e))

            # 如果有成功的记录，提交事务
            if success_count > 0:
                await self.db.commit()

            return TechnicalManualPriceImportResultModel(
                success=len(errors) == 0,
                total_count=total_count,
                success_count=success_count,
                error_count=len(errors),
                errors=errors,
            )

        except Exception as e:
            await self.db.rollback()
            return TechnicalManualPriceImportResultModel(
                success=False,
                total_count=0,
                success_count=0,
                error_count=1,
                errors=[
                    TechnicalManualPriceImportErrorModel(
                        row_number=0,
                        error_message=f"文件处理失败：{str(e)}",
                        method=None,
                        category=None,
                        first_item_price=None,
                        additional_item_price=None,
                        testing_fee_limit=None,
                        sampling_price=None,
                        pretreatment_price=None,
                    )
                ],
            )

    def _safe_float(self, value: Any) -> Optional[float]:
        """
        安全转换为浮点数

        :param value: 输入值
        :return: 浮点数或None
        """
        if pd.isna(value):
            return None
        try:
            float_value = float(value)
            return float_value if float_value >= 0 else None
        except (ValueError, TypeError):
            return None

    async def _validate_and_convert_row(self, row: pd.Series, row_number: int) -> TechnicalManualPriceImportModel:
        """
        验证和转换行数据

        :param row: 行数据
        :param row_number: 行号
        :return: 转换后的数据模型
        """
        # 必填字段验证
        if pd.isna(row["检测方法"]) or str(row["检测方法"]).strip() == "":
            raise ValueError("检测方法不能为空")

        if pd.isna(row["检测类别"]) or str(row["检测类别"]).strip() == "":
            raise ValueError("检测类别不能为空")

        # 转换价格字段
        price_fields = {
            "first_item_price": ("首项单价", "首项单价必须是非负数"),
            "additional_item_price": ("增项单价", "增项单价必须是非负数"),
            "testing_fee_limit": ("检测费上限", "检测费上限必须是非负数"),
            "sampling_price": ("采样单价", "采样单价必须是非负数"),
            "pretreatment_price": ("前处理单价", "前处理单价必须是非负数"),
        }

        price_values = {}
        for field, (column, error_msg) in price_fields.items():
            value = row.get(column)
            if not pd.isna(value):
                try:
                    float_value = float(value)
                    if float_value < 0:
                        raise ValueError(error_msg)
                    price_values[field] = float_value
                except ValueError as e:
                    if str(e) == error_msg:
                        raise
                    raise ValueError(f"{column}必须是有效的数字")

        return TechnicalManualPriceImportModel(
            method=str(row["检测方法"]).strip(), category=str(row["检测类别"]).strip(), **price_values
        )

    async def _check_existing_price(self, method: str, category: str) -> (str, Optional[TechnicalManualPrice]):
        """
        检查是否存在价格记录

        :param method: 检测方法
        :param category: 检测类别
        :return: 价格记录
        """
        # 只有检测类别，获取所有分类下的该检测类别
        category_stmt = select(TechnicalManualCategory.category_code).where(
            TechnicalManualCategory.category == category
        )
        category_result = await self.db.execute(category_stmt)
        category_codes = category_result.scalars().all()
        if not category_codes:
            raise ValueError(f"检测类别: {category} 不存在！, 请先在技术手册中维护！")
        # 检查方法是否存在
        category_code = category_codes[0]

        stmt = select(TechnicalManual).where(
            func.json_contains(TechnicalManual.category_codes, f'"{category_code}"'),
            TechnicalManual.method == method,
        )
        result = await self.db.execute(stmt)
        if not bool(result.first()):
            raise ValueError(f"检测类别: {category}, 检测方法: {method}, 不存在！, 请先在技术手册中维护！")

        stmt = select(TechnicalManualPrice).where(
            TechnicalManualPrice.method == method,
            TechnicalManualPrice.category_code.in_(category_codes),
        )
        result = await self.db.execute(stmt)
        return category_code, result.scalar_one_or_none()

    def _update_price_record(
        self,
        price_record: TechnicalManualPrice,
        import_data: TechnicalManualPriceImportModel,
        current_user: CurrentUserModel,
    ) -> None:
        """
        更新价格记录

        :param price_record: 现有价格记录
        :param import_data: 导入数据
        :param current_user: 当前用户
        """
        price_record.first_item_price = import_data.first_item_price
        price_record.additional_item_price = import_data.additional_item_price
        price_record.testing_fee_limit = import_data.testing_fee_limit
        price_record.sampling_price = import_data.sampling_price
        price_record.pretreatment_price = import_data.pretreatment_price
        price_record.update_by = current_user.user.user_name
        price_record.update_time = datetime.now()

    def _create_price_record(
        self, import_data: TechnicalManualPriceImportModel, current_user: CurrentUserModel
    ) -> TechnicalManualPrice:
        """
        创建新的价格记录

        :param import_data: 导入数据
        :param current_user: 当前用户
        :return: 新的价格记录
        """
        return TechnicalManualPrice(
            method=import_data.method,
            category_code=import_data.category_code,
            first_item_price=import_data.first_item_price,
            additional_item_price=import_data.additional_item_price,
            testing_fee_limit=import_data.testing_fee_limit,
            sampling_price=import_data.sampling_price,
            pretreatment_price=import_data.pretreatment_price,
            create_by=current_user.user.user_name,
            create_time=datetime.now(),
            update_by=current_user.user.user_name,
            update_time=datetime.now(),
        )

    async def generate_template(self) -> bytes:
        """
        生成Excel导入模板

        :return: Excel文件字节数据
        """
        # 创建示例数据
        template_data = {
            "检测方法": ["重量法", "玻璃电极法", "原子吸收分光光度法"],
            "检测类别": ["环境空气和废气", "水和废水", "土壤和沉积物"],
            "检测首项单价": [100.00, 50.00, 200.00],
            "检测增项单价": [80.00, 40.00, 150.00],
            "检测费上限": [500.00, 300.00, 1000.00],
            "采样单价": [30.00, 20.00, 50.00],
            "前处理单价": [20.00, 15.00, 40.00],
        }

        df = pd.DataFrame(template_data)

        # 保存到字节流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="技术价格导入模板", index=False)

        output.seek(0)
        return output.getvalue()
