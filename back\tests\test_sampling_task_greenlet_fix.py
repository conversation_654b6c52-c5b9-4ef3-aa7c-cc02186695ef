import pytest
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from httpx import AsyncClient
from fastapi import Fast<PERSON><PERSON>
from config.get_db import get_db
from module_sampling.dao.sampling_task_dao import SamplingTaskDao
from module_sampling.service.sampling_task_service import SamplingTaskService
from utils.test_user_factory import TestUserFactory


class TestSamplingTaskGreenletFix:
    """测试采样任务 MissingGreenlet 错误修复"""
    
    @pytest.mark.asyncio
    async def test_sampling_task_page_no_greenlet_error(self, db_session: AsyncSession):
        """测试采样任务分页查询不会出现 MissingGreenlet 错误"""
        # 创建测试用户
        test_user = TestUserFactory.create_test_user()
        
        # 创建 DAO 和 Service
        sampling_task_dao = SamplingTaskDao(db_session)
        sampling_task_service = SamplingTaskService(db_session)
        
        try:
            # 执行分页查询，这应该不会抛出 MissingGreenlet 错误
            tasks, total = await sampling_task_dao.page_sampling_tasks(
                page=1,
                size=10,
                project_quotation_id=None,
                assigned_user_id=None,
                status=None
            )
            
            # 验证查询成功
            assert isinstance(tasks, list)
            assert isinstance(total, int)
            assert total >= 0
            
        except Exception as e:
            # 如果出现 MissingGreenlet 错误，测试失败
            if "MissingGreenlet" in str(e):
                pytest.fail(f"MissingGreenlet error occurred: {e}")
            else:
                # 其他错误可能是正常的（比如数据不存在等）
                pass
    
    @pytest.mark.asyncio
    async def test_sampling_task_get_by_id_no_greenlet_error(self, db_session: AsyncSession):
        """测试根据ID获取采样任务不会出现 MissingGreenlet 错误"""
        # 创建测试用户
        test_user = TestUserFactory.create_test_user()
        
        # 创建 DAO
        sampling_task_dao = SamplingTaskDao(db_session)
        
        try:
            # 尝试获取一个可能不存在的任务，这应该不会抛出 MissingGreenlet 错误
            task = await sampling_task_dao.get_sampling_task_by_id(999999)
            
            # 验证查询成功（可能返回 None）
            assert task is None or hasattr(task, 'id')
            
        except Exception as e:
            # 如果出现 MissingGreenlet 错误，测试失败
            if "MissingGreenlet" in str(e):
                pytest.fail(f"MissingGreenlet error occurred: {e}")
            else:
                # 其他错误可能是正常的
                pass
    
    @pytest.mark.asyncio
    async def test_sampling_task_cycle_item_relations_no_greenlet_error(self, db_session: AsyncSession):
        """测试采样任务周期关联查询不会出现 MissingGreenlet 错误"""
        # 创建测试用户
        test_user = TestUserFactory.create_test_user()
        
        # 创建 DAO
        sampling_task_dao = SamplingTaskDao(db_session)
        
        try:
            # 执行周期关联查询，这应该不会抛出 MissingGreenlet 错误
            relations = await sampling_task_dao.get_task_cycle_relations(999999)
            
            # 验证查询成功
            assert isinstance(relations, list)
            
        except Exception as e:
            # 如果出现 MissingGreenlet 错误，测试失败
            if "MissingGreenlet" in str(e):
                pytest.fail(f"MissingGreenlet error occurred: {e}")
            else:
                # 其他错误可能是正常的
                pass


@pytest.mark.asyncio
async def test_sampling_task_api_endpoint_no_greenlet_error(client):
    """测试采样任务 API 端点不会出现 MissingGreenlet 错误"""
    try:
        # 测试分页查询 API
        response = client.get(
            "/sampling/task/page",
            params={"page": 1, "size": 10},
            headers={"Authorization": "Bearer test_token"}
        )
        
        # 验证响应状态码不是 500（内部服务器错误）
        assert response.status_code != 500, f"API returned 500 error: {response.text}"
        
        # 如果返回 200，验证响应格式
        if response.status_code == 200:
            data = response.json()
            assert "success" in data
            assert "data" in data
        
    except Exception as e:
        # 如果出现 MissingGreenlet 错误，测试失败
        if "MissingGreenlet" in str(e):
            pytest.fail(f"MissingGreenlet error occurred in API: {e}")
        else:
            # 其他错误可能是正常的
            pass