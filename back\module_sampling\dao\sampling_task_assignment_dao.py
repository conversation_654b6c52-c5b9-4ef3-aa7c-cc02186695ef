"""
采样任务执行人指派DAO
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from sqlalchemy.orm import selectinload

from module_sampling.entity.do.sampling_task_assignment_do import SamplingTaskAssignment


class SamplingTaskAssignmentDAO:
    """采样任务执行人指派DAO"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_assignment(self, assignment: SamplingTaskAssignment) -> SamplingTaskAssignment:
        """创建执行人指派"""
        self.db.add(assignment)
        await self.db.flush()
        await self.db.refresh(assignment)
        return assignment
    
    async def get_assignment_by_id(self, assignment_id: int) -> Optional[SamplingTaskAssignment]:
        """根据ID获取执行人指派"""
        stmt = select(SamplingTaskAssignment).where(SamplingTaskAssignment.id == assignment_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_assignments_by_task_id(self, task_id: int) -> List[SamplingTaskAssignment]:
        """根据任务ID获取所有指派"""
        stmt = select(SamplingTaskAssignment).where(
            SamplingTaskAssignment.sampling_task_id == task_id
        ).order_by(
            SamplingTaskAssignment.cycle_number,
            SamplingTaskAssignment.cycle_type,
            SamplingTaskAssignment.detection_category,
            SamplingTaskAssignment.point_name
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_assignment_by_group_key(
        self, 
        task_id: int, 
        cycle_number: int, 
        cycle_type: Optional[str], 
        detection_category: Optional[str], 
        point_name: Optional[str]
    ) -> Optional[SamplingTaskAssignment]:
        """根据分组键获取任务指派"""
        stmt = select(SamplingTaskAssignment).where(
            SamplingTaskAssignment.sampling_task_id == task_id,
            SamplingTaskAssignment.cycle_number == cycle_number,
            SamplingTaskAssignment.cycle_type == cycle_type,
            SamplingTaskAssignment.detection_category == detection_category,
            SamplingTaskAssignment.point_name == point_name
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_assignment(self, assignment: SamplingTaskAssignment) -> SamplingTaskAssignment:
        """更新任务指派"""
        await self.db.flush()
        await self.db.refresh(assignment)
        return assignment
    
    async def delete_assignment(self, assignment_id: int) -> bool:
        """删除任务指派"""
        stmt = delete(SamplingTaskAssignment).where(SamplingTaskAssignment.id == assignment_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_assignments_by_task_id(self, task_id: int) -> bool:
        """删除任务的所有指派"""
        stmt = delete(SamplingTaskAssignment).where(SamplingTaskAssignment.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def get_assignments_by_user_id(self, user_id: int) -> List[SamplingTaskAssignment]:
        """根据用户ID获取分配给该用户的任务指派"""
        stmt = select(SamplingTaskAssignment).options(
            selectinload(SamplingTaskAssignment.sampling_task)
        ).where(
            SamplingTaskAssignment.assigned_user_ids.like(f'%{user_id}%')
        )
        result = await self.db.execute(stmt)
        assignments = result.scalars().all()
        
        # 过滤出真正包含该用户ID的记录
        import json
        filtered_assignments = []
        for assignment in assignments:
            if assignment.assigned_user_ids:
                try:
                    user_ids = json.loads(assignment.assigned_user_ids)
                    if user_id in user_ids:
                        filtered_assignments.append(assignment)
                except:
                    continue
        
        return filtered_assignments
