import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from module_sampling.dao.detection_cycle_item_dao import DetectionCycleItemDao
from module_sampling.service.detection_cycle_item_service import DetectionCycleItemService
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from datetime import datetime


@pytest.mark.asyncio
class TestDetectionCycleItemMissingGreenletFix:
    """测试检测周期条目的MissingGreenlet错误修复"""

    async def test_get_items_by_project_quotation_id_no_missing_greenlet(self, db_session: AsyncSession):
        """测试根据项目报价ID获取检测周期条目不会产生MissingGreenlet错误"""
        # 创建测试数据
        quotation = ProjectQuotation(
            project_name="测试项目",
            project_code="TEST001",
            customer_name="测试客户",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            detection_qualification="CMA",
            detection_classification="环境检测",
            detection_category="水质",
            detection_parameter="pH",
            detection_method="玻璃电极法",
            sample_source="地表水",
            point_name="采样点1",
            cycle_type="月度",
            cycle_count=12,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试DAO方法
        dao = DetectionCycleItemDao(db_session)
        items = await dao.get_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(items) == 1
        item = items[0]
        
        # 访问关系属性不应该产生MissingGreenlet错误
        assert item.project_quotation is not None
        assert item.project_quotation.project_name == "测试项目"
        assert item.project_quotation_item is not None
        assert item.project_quotation_item.detection_parameter == "pH"

    async def test_get_unassigned_items_no_missing_greenlet(self, db_session: AsyncSession):
        """测试获取未分配检测周期条目不会产生MissingGreenlet错误"""
        # 创建测试数据
        quotation = ProjectQuotation(
            project_name="测试项目2",
            project_code="TEST002",
            customer_name="测试客户2",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            detection_qualification="CMA",
            detection_classification="环境检测",
            detection_category="大气",
            detection_parameter="PM2.5",
            detection_method="重量法",
            sample_source="环境空气",
            point_name="监测点1",
            cycle_type="日",
            cycle_count=30,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,  # 未分配状态
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试DAO方法
        dao = DetectionCycleItemDao(db_session)
        items = await dao.get_unassigned_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(items) == 1
        item = items[0]
        
        # 访问关系属性不应该产生MissingGreenlet错误
        assert item.project_quotation is not None
        assert item.project_quotation.project_name == "测试项目2"
        assert item.project_quotation_item is not None
        assert item.project_quotation_item.detection_parameter == "PM2.5"

    async def test_service_convert_to_dto_no_missing_greenlet(self, db_session: AsyncSession):
        """测试服务层转换DTO不会产生MissingGreenlet错误"""
        # 创建测试数据
        quotation = ProjectQuotation(
            project_name="测试项目3",
            project_code="TEST003",
            customer_name="测试客户3",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            detection_qualification="CMA",
            detection_classification="环境检测",
            detection_category="土壤",
            detection_parameter="重金属",
            detection_method="原子吸收法",
            sample_source="农田土壤",
            point_name="采样点A",
            cycle_type="季度",
            cycle_count=4,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试服务层方法
        service = DetectionCycleItemService(db_session)
        result = await service.get_cycle_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(result) == 1
        dto = result[0]
        
        # 验证DTO包含正确的数据（这些数据来自关系访问）
        assert dto.project_name == "测试项目3"
        assert dto.detection_parameter == "重金属"
        assert dto.detection_method == "原子吸收法"
        assert dto.sample_source == "农田土壤"