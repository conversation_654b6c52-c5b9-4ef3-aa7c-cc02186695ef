# 技术手册批量导入功能实现文档

## 功能概述

本文档描述了基础数据-技术手册模块新增的批量导入功能实现，包括新增"是否有资质"字段和完整的批量导入流程。

## 新增功能

### 1. 新增字段：是否有资质
- **字段名**: `has_qualification`
- **类型**: VARCHAR(1)
- **取值**: 0=有，1=无
- **默认值**: '0'（有资质）

### 2. 批量导入功能
- 支持Excel文件拖拽上传
- 提供标准Excel模板下载
- 支持多种日期格式解析
- 详细的错误信息反馈

## 技术实现

### 后端实现

#### 1. 数据模型更新

**DO模型更新**：
```python
# technical_manual_do.py
has_qualification = Column(String(1), default='0', comment='是否有资质(0=有，1=无)')
```

**VO模型更新**：
```python
# technical_manual_vo.py
has_qualification: Optional[Literal["0", "1"]] = Field(default="0", description="是否有资质(0=有，1=无)")

# 新增导入相关模型
class TechnicalManualImportModel(BaseModel):
    # 导入数据模型
    
class TechnicalManualImportErrorModel(BaseModel):
    # 导入错误模型
    
class TechnicalManualImportResultModel(BaseModel):
    # 导入结果模型
```

#### 2. 导入服务实现

**TechnicalManualImportService**：
```python
class TechnicalManualImportService:
    async def import_from_excel(self, file: UploadFile, current_user: CurrentUserModel):
        # Excel文件解析
        # 数据验证
        # 批量插入
        # 错误处理
        
    async def generate_template(self):
        # 生成Excel模板
        
    def _parse_date(self, date_str: Optional[str]):
        # 支持多种日期格式解析
```

**支持的日期格式**：
- 2025/1/2
- 2025-01-02
- 2025年1月2日
- 2025.1.2
- 等多种格式

#### 3. 控制器接口

```python
@router.get('/import/template', summary='下载批量导入模板')
async def download_import_template():
    # 下载Excel模板

@router.post('/import', response_model=TechnicalManualImportResultModel, summary='批量导入技术手册')
async def import_technical_manual(file: UploadFile = File(...)):
    # 批量导入处理
```

### 前端实现

#### 1. API接口

```javascript
// 下载批量导入模板
export function downloadImportTemplate() {
  return request({
    url: '/basedata/technical-manual/import/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导入技术手册
export function importTechnicalManual(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/basedata/technical-manual/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

#### 2. 批量导入组件

**TechnicalManualImport组件特性**：
- 拖拽上传支持
- 文件类型和大小验证
- 模板下载功能
- 详细的字段说明
- 错误信息展示

**组件结构**：
```vue
<template>
  <!-- 批量导入按钮 -->
  <el-button type="primary" @click="openImportDialog">
    批量导入
  </el-button>

  <!-- 批量导入对话框 -->
  <el-dialog v-model="importDialogVisible" title="批量导入技术手册">
    <!-- 文件上传区域 -->
    <el-upload drag :auto-upload="false" :on-change="handleFileChange">
      <!-- 拖拽上传界面 -->
    </el-upload>
    
    <!-- 模板下载区域 -->
    <el-button @click="downloadTemplate">下载模板</el-button>
    
    <!-- 字段说明 -->
    <div class="template-fields">
      <!-- 详细的字段说明 -->
    </div>
  </el-dialog>

  <!-- 错误详情对话框 -->
  <el-dialog v-model="errorDialogVisible" title="导入错误详情">
    <!-- 错误信息表格 -->
    <el-table :data="importResult.errors">
      <!-- 错误详情列 -->
    </el-table>
  </el-dialog>
</template>
```

#### 3. 主页面集成

**技术手册管理页面更新**：
- 添加"是否有资质"列显示
- 集成批量导入组件
- 表单中添加"是否有资质"字段

## Excel模板格式

### 模板列结构

| 列名 | 说明 | 是否必填 | 示例值 |
|------|------|----------|--------|
| 分类 | 检测分类 | 否 | 气、水、土壤 |
| 检测类别 | 具体检测类别 | 是 | 环境空气和废气 |
| 检测参数 | 检测参数名称 | 是 | PM2.5 |
| 检测方法 | 检测方法描述 | 是 | 重量法 |
| 资质编号 | 资质编号 | 否 | ZZ001 |
| 限制范围 | 检测限制范围 | 否 | 0-500μg/m³ |
| 常用别名 | 参数的常用别名 | 否 | 细颗粒物 |
| 是否资质 | 是否有资质 | 否 | 有/无 或 0/1 |
| 取得资质时间 | 取得资质时间 | 否 | 2025/1/2 |

### 示例数据

```
分类    检测类别        检测参数    检测方法        资质编号    限制范围        常用别名    是否资质    取得资质时间
气      环境空气和废气  PM2.5       重量法          ZZ001       0-500μg/m³      细颗粒物    有          2025/1/2
水      水和废水        pH值        玻璃电极法      ZZ002       6.0-9.0         酸碱度      有          2025-01-03
土壤    土壤和沉积物    重金属      原子吸收法      ZZ003       0-100mg/kg      重金属含量  无          2025.1.4
```

## 错误处理

### 错误类型

1. **文件格式错误**：
   - 不支持的文件类型
   - 文件大小超限
   - Excel格式损坏

2. **模板格式错误**：
   - 缺少必要列
   - 列名不匹配

3. **数据验证错误**：
   - 必填字段为空
   - 数据格式不正确
   - 日期格式无效
   - 记录重复

### 错误信息展示

**错误详情表格**：
```
错误行 | 错误原因 | 资质编号 | 检测类别 | 检测参数 | 检测方法
2      | 检测类别不能为空 | ZZ001 | | PM2.5 | 重量法
3      | 记录已存在 | ZZ002 | 水和废水 | pH值 | 玻璃电极法
4      | 日期格式无效 | ZZ003 | 土壤 | 重金属 | 原子吸收法
```

## 数据库迁移

### 迁移脚本

```sql
-- 添加"是否有资质"字段
ALTER TABLE technical_manual 
ADD COLUMN has_qualification VARCHAR(1) DEFAULT '0' COMMENT '是否有资质(0=有，1=无)' AFTER qualification_date;

-- 添加索引
CREATE INDEX idx_technical_manual_has_qualification ON technical_manual(has_qualification);
```

## 测试用例

### 功能测试

1. **模板下载测试**：
   - 验证模板文件下载
   - 检查模板格式正确性

2. **文件上传测试**：
   - 拖拽上传功能
   - 文件类型验证
   - 文件大小限制

3. **数据导入测试**：
   - 正常数据导入
   - 错误数据处理
   - 重复数据检测

4. **日期解析测试**：
   - 多种日期格式支持
   - 无效日期处理

### 边界测试

1. **大文件测试**：
   - 10MB文件上传
   - 大量数据导入

2. **异常数据测试**：
   - 空文件处理
   - 格式错误文件
   - 网络中断处理

## 部署说明

### 后端部署

1. **数据库迁移**：
   ```bash
   # 执行迁移脚本
   mysql -u username -p database_name < add_has_qualification_field_to_technical_manual.sql
   ```

2. **依赖安装**：
   ```bash
   pip install pandas openpyxl
   ```

### 前端部署

1. **组件注册**：
   - 确保TechnicalManualImport组件正确导入
   - 验证Element Plus图标组件

2. **权限配置**：
   - 添加批量导入权限控制
   - 配置文件上传限制

## 使用说明

### 操作流程

1. **下载模板**：
   - 点击"批量导入"按钮
   - 点击"下载模板"获取Excel模板

2. **填写数据**：
   - 按照模板格式填写数据
   - 注意必填字段和数据格式

3. **上传导入**：
   - 拖拽或选择Excel文件
   - 点击"开始导入"

4. **查看结果**：
   - 查看导入成功数量
   - 如有错误，查看错误详情

### 注意事项

1. **数据准备**：
   - 确保必填字段完整
   - 检查数据格式正确性
   - 避免重复记录

2. **文件要求**：
   - 仅支持.xlsx和.xls格式
   - 文件大小不超过10MB
   - 使用提供的模板格式

3. **错误处理**：
   - 仔细查看错误信息
   - 修正错误后重新导入
   - 可以部分导入成功的记录

## 总结

技术手册批量导入功能提供了：
- ✅ 完整的批量导入流程
- ✅ 友好的用户界面
- ✅ 详细的错误反馈
- ✅ 多种日期格式支持
- ✅ 数据验证和去重
- ✅ 新增"是否有资质"字段管理

该功能大大提高了技术手册数据录入的效率，为用户提供了便捷的批量数据管理能力。
