#!/usr/bin/env python3
"""
测试执行人指派信息获取
"""
import asyncio
import json
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from module_sampling.dao.sampling_task_assignment_dao import SamplingTaskAssignmentDAO
from module_admin.dao.user_dao import UserDao

async def test_executor_assignment():
    """测试执行人指派信息获取"""
    # 使用MySQL数据库连接
    engine = create_async_engine('mysql+aiomysql://root:lims-user@127.0.0.1:3306/lims-Root1')
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        # 测试获取执行人指派
        assignment_dao = SamplingTaskAssignmentDAO(session)
        user_dao = UserDao(session)
        
        print("=== 测试执行人指派信息获取 ===")
        
        # 获取任务10的执行人指派
        assignments = await assignment_dao.get_assignments_by_task_id(10)
        print(f"任务10的执行人指派数量: {len(assignments)}")
        
        for assignment in assignments:
            print(f"\n指派ID: {assignment.id}")
            print(f"周期序号: {assignment.cycle_number}")
            print(f"周期类型: {assignment.cycle_type}")
            print(f"检测类别: {assignment.detection_category}")
            print(f"点位名称: {assignment.point_name}")
            print(f"执行人IDs (原始): {assignment.assigned_user_ids}")
            
            # 解析执行人ID列表
            assigned_user_ids = []
            assigned_user_names = []
            if assignment.assigned_user_ids:
                try:
                    assigned_user_ids = json.loads(assignment.assigned_user_ids)
                    print(f"解析后的执行人IDs: {assigned_user_ids}")
                    
                    # 获取用户名称
                    for user_id in assigned_user_ids:
                        print(f"  查询用户ID: {user_id}")
                        user = await user_dao.get_user_detail_by_id(user_id)
                        if user:
                            user_name = user.nick_name or user.user_name
                            assigned_user_names.append(user_name)
                            print(f"    找到用户: {user.user_name}, 昵称: {user.nick_name}, 使用名称: {user_name}")
                        else:
                            print(f"    用户ID {user_id} 不存在")
                except Exception as e:
                    print(f"解析执行人IDs时出错: {e}")
            
            print(f"最终执行人名称列表: {assigned_user_names}")

if __name__ == "__main__":
    asyncio.run(test_executor_assignment())
