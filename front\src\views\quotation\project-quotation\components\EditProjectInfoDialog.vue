<template>
  <!-- 修改项目信息弹框 -->
  <el-dialog :title="'修改项目信息 - ' + form.projectName" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-tabs v-model="activeTab">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="业务类别" prop="businessType">
            <el-select v-model="form.businessType" placeholder="请选择业务类别" style="width: 100%">
              <el-option label="一般采样" value="sampling" />
              <el-option label="送样" value="sample" />
            </el-select>
          </el-form-item>
          <el-form-item label="合同编号" prop="contractCode">
            <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
          </el-form-item>
          <el-divider content-position="left">客户信息</el-divider>
          <el-form-item label="客户名称" prop="customerName">
            <el-autocomplete
              v-model="form.customerName"
              :fetch-suggestions="queryCustomers"
              placeholder="请输入客户名称"
              @select="handleCustomerSelect"
            />
          </el-form-item>
          <el-form-item label="客户地址">
            <el-input v-model="form.customerAddress" placeholder="客户地址" />
          </el-form-item>
          <el-form-item label="客户联系人">
            <el-input v-model="form.customerContact" placeholder="客户联系人" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.customerPhone" placeholder="联系电话" />
          </el-form-item>
          <el-divider content-position="left">受检方信息</el-divider>
          <el-form-item label="受检方企业名称" prop="inspectedParty">
            <el-autocomplete
              v-model="form.inspectedParty"
              :fetch-suggestions="queryCustomers"
              placeholder="请输入受检方企业名称"
              @select="handleInpectedSelect"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="受检方联系人" prop="inspectedContact">
            <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
          </el-form-item>
          <el-form-item label="受检方联系电话" prop="inspectedPhone">
            <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
          </el-form-item>
          <el-form-item label="受检方详细地址" prop="inspectedAddress">
            <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
          </el-form-item>
          <el-divider content-position="left">项目信息</el-divider>
          <el-form-item label="项目负责人" prop="projectManager">
            <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
          </el-form-item>
          <el-form-item label="市场负责人" prop="marketManager">
            <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
          </el-form-item>
          <el-form-item label="项目技术人" prop="technicalManager">
            <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
          </el-form-item>
          <el-form-item label="委托日期" prop="commissionDate">
            <el-date-picker
              v-model="form.commissionDate"
              type="date"
              placeholder="选择委托日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="项目所在地" prop="projectLocation">
            <el-cascader
              v-model="regionValue"
              :options="regionOptions"
              placeholder="请选择项目所在地"
              style="width: 100%"
              clearable
              filterable
              @change="handleRegionChange"
            />
          </el-form-item>
          <el-form-item label="项目客服" prop="customerSupportIds">
          <el-select
            v-model="form.customerSupportIds"
            multiple
            placeholder="请选择项目客服"
            style="width: 100%"
          >
            <el-option
              v-for="item in customerSupportOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 检测项目 -->
      <el-tab-pane label="检测项目" name="items">
        <!-- 替换为 QuotationDetailTable 组件 -->
        <QuotationDetailTable  v-model="quotationDetailData" />

      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotation, searchCustomer } from "@/api/quotation/projectQuotation"
import { getCustomer, listCustomer } from "@/api/customer/customer"
import { getCategoryOptions, getParameterOptions, getMethodOptions } from "@/api/basedata/technicalManual"
import { getCustomerSupportOptionsByKey } from "@/api/common/userRole"
import { provinceAndCityData } from 'element-china-area-data'
// import QuotationDetail from '@/components/QuotationDetail/index.vue'
// 导入 QuotationDetailTable 组件
import QuotationDetailTable from './QuotationDetailTable.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前激活的标签页
const activeTab = ref('basic')
// 表单引用
const basicFormRef = ref(null)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置数据
    resetData()
    // 获取客服选项
    loadCustomerSupportOptions()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  console.log('Dialog visible changed to:', newVal)
})


// 客服选项
const customerSupportOptions = ref([])

// 地址选择器数据和配置
const regionOptions = ref(provinceAndCityData)
const regionValue = ref([])

// 检测类别选项
const categoryOptions = ref([])
// 检测参数选项映射
const parameterOptionsMap = ref({})
// 检测方法选项映射
const methodOptionsMap = ref({})

// 检测明细项目相关数据
const quotationDetailData = ref([])

// 表单对象
const form = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  businessType: 'sampling', // 默认为一般采样
  contractCode: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  projectProvince: '', // 项目所在省
  projectCity: '', // 项目所在市
  customerSupportIds: [],
  status: '',
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
  businessType: [
    { required: true, message: '业务类别不能为空', trigger: 'change' }
  ]
}

// 重置数据
function resetData() {
  // 重置表单对象
  form.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    businessType: 'sampling', // 重置为默认值
    contractCode: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    projectProvince: '', // 重置项目所在省
    projectCity: '', // 重置项目所在市
    customerSupportIds: [],
    status: '',
    remark: '',
    items: [],
    attachments: []
  }

  // 重置检测明细项目数据
  quotationDetailData.value = []

  // 重置地区选择器
  regionValue.value = []
}

// 获取客服选项
function loadCustomerSupportOptions() {
  // 使用权限key获取客服选项函数
  getCustomerSupportOptionsByKey('customer-support').then(options => {
    customerSupportOptions.value = options
  }).catch(error => {
    console.error('获取客服列表失败:', error)
  })
}

// 获取项目报价详情
function getQuotationDetail(id) {
  getProjectQuotation(id).then(response => {
    const data = response.data

    // 填充表单数据
    form.value = {
      id: data.id,
      projectName: data.projectName,
      projectCode: data.projectCode,
      businessType: data.businessType || 'sampling', // 设置业务类别
      contractCode: data.contractCode,
      customerId: data.customerId,
      customerName: data.customerName,
      customerAddress: data.customerAddress,
      customerContact: data.customerContact,
      customerPhone: data.customerPhone,
      inspectedParty: data.inspectedParty,
      inspectedContact: data.inspectedContact,
      inspectedPhone: data.inspectedPhone,
      inspectedAddress: data.inspectedAddress,
      projectManager: data.projectManager,
      marketManager: data.marketManager,
      technicalManager: data.technicalManager,
      commissionDate: data.commissionDate,
      projectProvince: data.projectProvince || '', // 项目所在省
      projectCity: data.projectCity || '', // 项目所在市
      customerSupportIds: data.customerSupportIds || [],
      status: data.status,
      remark: data.remark,
      items: data.items || [],
      attachments: data.attachments || []
    }

    // 转换现有的items数据为QuotationDetail组件所需的格式
    if (data.items && data.items.length > 0) {
      quotationDetailData.value = data.items.map(item => ({
        id: item.id || '',
        itemUniqueId: item.category || '' + "_" + item.method || '' + "_" + item.parameter || '',
        classification: item.classification || '',
        category: item.category || '',
        qualificationCode: item.qualificationCode || '',
        parameter: item.parameter || '',
        method: item.method || '',
        limitationScope: item.limitationScope || '',
        sampleSource: item.sampleSource || '',
        pointName: item.pointName || '',
        pointCount: item.pointCount || 0,
        cycleType: item.cycleType || '',
        cycleCount: item.cycleCount || 0,
        frequency: item.frequency || 0,
        sampleCount: item.sampleCount || 0,
        isSubcontract: item.isSubcontract,
        remark: item.remark || ''
      }))
    }

    // 设置地区选择器的值
    if (data.projectProvince && data.projectCity) {
      // 根据省市名称找到对应的代码
      console.log("data.projectProvince：",data.projectProvince)
      const province = regionOptions.value.find(item => item.label === data.projectProvince)
      if (province) {
        const city = province.children?.find(item => item.label === data.projectCity)
        if (city) {
          regionValue.value = [province.value, city.value]
        } else {
          regionValue.value = [province.value]
        }
      }
    }

    // 获取检测类别选项
    getCategoryOptions().then(response => {
      categoryOptions.value = response.data

      // 预加载检测参数和方法选项
      form.value.items.forEach(item => {
        if (item.category) {
          getParameterOptions({ category: item.category }).then(response => {
            parameterOptionsMap.value[item.category] = response.data
          })

          if (item.parameter) {
            const key = `${item.category}-${item.parameter}`
            getMethodOptions({ category: item.category, parameter: item.parameter }).then(response => {
              methodOptionsMap.value[key] = response.data
            })
          }
        }
      })
    })
  })
}

let searchTimer = null;
// 查询客户列表
function queryCustomers(queryString, cb) {
  if (!queryString) {
    cb([])
    return
  }
  
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    // 使用客户管理模块的API接口
    searchCustomer({ keyword: queryString }).then(response => {
      const customers = response.data.map(item => {
        return {
          value: item.name,
          id: item.id,
          data: item
        }
      })
      console.log("customers", customers)
      cb(customers)
    }).catch(error => {
      console.error('查询客户失败:', error)
      cb([])
    })
  }, 100)
 
}

// 地区选择变更处理
function handleRegionChange(val) {
  if (val && val.length > 0) {
    // 根据选择的地区代码获取地区名称
    const province = regionOptions.value.find(item => item.value === val[0])
    form.value.projectProvince = province?.label

    if (val.length > 1) {
      const city = province?.children.find(item => item.value === val[1])
      form.value.projectCity = city?.label
    } else {
      form.value.projectCity = ''
    }
  } else {
    form.value.projectProvince = ''
    form.value.projectCity = ''
  }
}

// 选择客户
function handleCustomerSelect(item) {
  form.value.customerId = item.id
  form.value.customerAddress = item.data.address
  form.value.customerContact = item.data.contactName
  form.value.customerPhone = item.data.contactPhone
  console.log("handleCustomerSelect", item, "form.customerContact:", form.customerContact)
  // getCustomer(item.id).then(response => {
  //   const customer = response.data
  //   form.customerAddress = item.data.address || customer.address
  //   form.customerContact = item.data.contactName ||customer.contactName
  //   form.customerPhone = item.data.contactPhone || customer.contactPhone
  // })
}

function handleInpectedSelect(item) {
  form.value.inspectedAddress = item.data.address
  form.value.inspectedContact = item.data.contactName
  form.value.inspectedPhone = item.data.contactPhone
  console.log("handleInspectedSelect", item)
}


// 关闭对话框
function closeDialog() {
  console.log('关闭对话框，当前 dialogVisible:', dialogVisible.value)
  dialogVisible.value = false
  console.log('关闭对话框后，dialogVisible 设置为:', dialogVisible.value)
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 提交表单
function submitForm() {
  basicFormRef.value.validate(valid => {
    if (valid) {
      // 验证检测项目
      if (quotationDetailData.value.length === 0) {
        ElMessage.warning('请配置检测明细项目')
        activeTab.value = 'items'
        return
      }

      // 验证每个检测项目的必填字段
      let itemsValid = true
      quotationDetailData.value.forEach((item, index) => {
        if (!item.category || !item.parameter || !item.method) {
          ElMessage.warning(`第 ${index + 1} 行检测项目信息不完整，缺少必要的类别、参数或方法`)
          itemsValid = false
        }
      })

      if (!itemsValid) {
        activeTab.value = 'items'
        return
      }
      // 转换数据格式
      const formData = { ...form.value }

      // approvers字段已废弃，使用customerSupportIds
      formData.items = quotationDetailData.value.map(item => ({
        ...item,
        isSubcontract: item.isSubcontract
      }))
      // 调用保存接口
      updateProjectQuotation(formData).then(() => {
        ElMessage.success('保存成功')
        console.log('保存成功，准备关闭对话框')
        dialogVisible.value = false
        emit('update:visible', false)
        emit('refresh')
      }).catch(error => {
        console.error('保存失败:', error)
        if (error.response && error.response.data) {
          ElMessage.error(`保存失败: ${error.response.data.message || '未知错误'}`)
        } else {
          ElMessage.error('保存失败，请检查表单数据')
        }
      })
    }
  })
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}
</style>