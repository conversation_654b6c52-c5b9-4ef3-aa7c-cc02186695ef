<template>
  <!-- 修改项目信息弹框 -->
  <el-dialog :title="'修改项目信息 - ' + form.projectName" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-tabs v-model="activeTab">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="业务类别" prop="businessType">
            <el-select v-model="form.businessType" placeholder="请选择业务类别" style="width: 100%">
              <el-option label="一般采样" value="sampling" />
              <el-option label="送样" value="sample" />
            </el-select>
          </el-form-item>
          <el-form-item label="合同编号" prop="contractCode">
            <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
          </el-form-item>
          <el-divider content-position="left">客户信息</el-divider>
          <el-form-item label="客户名称" prop="customerName">
            <el-autocomplete
              v-model="form.customerName"
              :fetch-suggestions="queryCustomers"
              placeholder="请输入客户名称"
              @select="handleCustomerSelect"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="客户地址">
            <el-input v-model="form.customerAddress" placeholder="客户地址" />
          </el-form-item>
          <el-form-item label="客户联系人">
            <el-input v-model="form.customerContact" placeholder="客户联系人" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.customerPhone" placeholder="联系电话" />
          </el-form-item>
          <el-divider content-position="left">受检方信息</el-divider>
          <el-form-item label="受检方企业名称" prop="inspectedParty">
            <el-input v-model="form.inspectedParty" placeholder="请输入受检方企业名称" />
          </el-form-item>
          <el-form-item label="受检方联系人" prop="inspectedContact">
            <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
          </el-form-item>
          <el-form-item label="受检方联系电话" prop="inspectedPhone">
            <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
          </el-form-item>
          <el-form-item label="受检方详细地址" prop="inspectedAddress">
            <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
          </el-form-item>
          <el-divider content-position="left">项目信息</el-divider>
          <el-form-item label="项目负责人" prop="projectManager">
            <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
          </el-form-item>
          <el-form-item label="市场负责人" prop="marketManager">
            <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
          </el-form-item>
          <el-form-item label="项目技术人" prop="technicalManager">
            <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
          </el-form-item>
          <el-form-item label="委托日期" prop="commissionDate">
            <el-date-picker
              v-model="form.commissionDate"
              type="date"
              placeholder="选择委托日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="项目所在地" prop="projectLocation">
            <el-cascader
              v-model="form.projectLocation"
              :options="locationOptions"
              :props="locationProps"
              placeholder="请选择项目所在地"
              style="width: 100%"
              clearable
              filterable
            />
          </el-form-item>
          <el-form-item label="项目客服" prop="customerSupportIds">
          <el-select
            v-model="form.customerSupportIds"
            multiple
            placeholder="请选择项目客服"
            style="width: 100%"
          >
            <el-option
              v-for="item in customerSupportOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 检测项目 -->
      <el-tab-pane label="检测项目" name="items">
        <!-- 替换为 QuotationDetailTable 组件 -->
        <QuotationDetailTable  v-model="quotationDetailData" />

      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotation } from "@/api/quotation/projectQuotation"
import { getCustomer, listCustomer } from "@/api/customer/customer"
import { getCategoryOptions, getParameterOptions, getMethodOptions } from "@/api/basedata/technicalManual"
import { getCustomerSupportOptionsByKey } from "@/api/common/userRole"
import QuotationDetail from '@/components/QuotationDetail/index.vue'
// 导入 QuotationDetailTable 组件
import QuotationDetailTable from './QuotationDetailTable.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前激活的标签页
const activeTab = ref('basic')
// 表单引用
const basicFormRef = ref(null)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置数据
    resetData()
    // 获取客服选项
    loadCustomerSupportOptions()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  console.log('Dialog visible changed to:', newVal)
})


// 客服选项
const customerSupportOptions = ref([])

// 地址选择器配置
const locationProps = {
  value: 'code',
  label: 'name',
  children: 'children',
  expandTrigger: 'hover'
}

// 地址选择器数据（省市数据）
const locationOptions = ref([
  {
    code: '110000',
    name: '北京市',
    children: [
      { code: '110100', name: '北京市' }
    ]
  },
  {
    code: '120000',
    name: '天津市',
    children: [
      { code: '120100', name: '天津市' }
    ]
  },
  {
    code: '130000',
    name: '河北省',
    children: [
      { code: '130100', name: '石家庄市' },
      { code: '130200', name: '唐山市' },
      { code: '130300', name: '秦皇岛市' },
      { code: '130400', name: '邯郸市' },
      { code: '130500', name: '邢台市' },
      { code: '130600', name: '保定市' },
      { code: '130700', name: '张家口市' },
      { code: '130800', name: '承德市' },
      { code: '130900', name: '沧州市' },
      { code: '131000', name: '廊坊市' },
      { code: '131100', name: '衡水市' }
    ]
  },
  {
    code: '140000',
    name: '山西省',
    children: [
      { code: '140100', name: '太原市' },
      { code: '140200', name: '大同市' },
      { code: '140300', name: '阳泉市' },
      { code: '140400', name: '长治市' },
      { code: '140500', name: '晋城市' },
      { code: '140600', name: '朔州市' },
      { code: '140700', name: '晋中市' },
      { code: '140800', name: '运城市' },
      { code: '140900', name: '忻州市' },
      { code: '141000', name: '临汾市' },
      { code: '141100', name: '吕梁市' }
    ]
  },
  {
    code: '210000',
    name: '辽宁省',
    children: [
      { code: '210100', name: '沈阳市' },
      { code: '210200', name: '大连市' },
      { code: '210300', name: '鞍山市' },
      { code: '210400', name: '抚顺市' },
      { code: '210500', name: '本溪市' },
      { code: '210600', name: '丹东市' },
      { code: '210700', name: '锦州市' },
      { code: '210800', name: '营口市' },
      { code: '210900', name: '阜新市' },
      { code: '211000', name: '辽阳市' },
      { code: '211100', name: '盘锦市' },
      { code: '211200', name: '铁岭市' },
      { code: '211300', name: '朝阳市' },
      { code: '211400', name: '葫芦岛市' }
    ]
  },
  {
    code: '220000',
    name: '吉林省',
    children: [
      { code: '220100', name: '长春市' },
      { code: '220200', name: '吉林市' },
      { code: '220300', name: '四平市' },
      { code: '220400', name: '辽源市' },
      { code: '220500', name: '通化市' },
      { code: '220600', name: '白山市' },
      { code: '220700', name: '松原市' },
      { code: '220800', name: '白城市' },
      { code: '222400', name: '延边朝鲜族自治州' }
    ]
  },
  {
    code: '230000',
    name: '黑龙江省',
    children: [
      { code: '230100', name: '哈尔滨市' },
      { code: '230200', name: '齐齐哈尔市' },
      { code: '230300', name: '鸡西市' },
      { code: '230400', name: '鹤岗市' },
      { code: '230500', name: '双鸭山市' },
      { code: '230600', name: '大庆市' },
      { code: '230700', name: '伊春市' },
      { code: '230800', name: '佳木斯市' },
      { code: '230900', name: '七台河市' },
      { code: '231000', name: '牡丹江市' },
      { code: '231100', name: '黑河市' },
      { code: '231200', name: '绥化市' },
      { code: '232700', name: '大兴安岭地区' }
    ]
  },
  {
    code: '310000',
    name: '上海市',
    children: [
      { code: '310100', name: '上海市' }
    ]
  },
  {
    code: '320000',
    name: '江苏省',
    children: [
      { code: '320100', name: '南京市' },
      { code: '320200', name: '无锡市' },
      { code: '320300', name: '徐州市' },
      { code: '320400', name: '常州市' },
      { code: '320500', name: '苏州市' },
      { code: '320600', name: '南通市' },
      { code: '320700', name: '连云港市' },
      { code: '320800', name: '淮安市' },
      { code: '320900', name: '盐城市' },
      { code: '321000', name: '扬州市' },
      { code: '321100', name: '镇江市' },
      { code: '321200', name: '泰州市' },
      { code: '321300', name: '宿迁市' }
    ]
  },
  {
    code: '330000',
    name: '浙江省',
    children: [
      { code: '330100', name: '杭州市' },
      { code: '330200', name: '宁波市' },
      { code: '330300', name: '温州市' },
      { code: '330400', name: '嘉兴市' },
      { code: '330500', name: '湖州市' },
      { code: '330600', name: '绍兴市' },
      { code: '330700', name: '金华市' },
      { code: '330800', name: '衢州市' },
      { code: '330900', name: '舟山市' },
      { code: '331000', name: '台州市' },
      { code: '331100', name: '丽水市' }
    ]
  },
  {
    code: '340000',
    name: '安徽省',
    children: [
      { code: '340100', name: '合肥市' },
      { code: '340200', name: '芜湖市' },
      { code: '340300', name: '蚌埠市' },
      { code: '340400', name: '淮南市' },
      { code: '340500', name: '马鞍山市' },
      { code: '340600', name: '淮北市' },
      { code: '340700', name: '铜陵市' },
      { code: '340800', name: '安庆市' },
      { code: '341000', name: '黄山市' },
      { code: '341100', name: '滁州市' },
      { code: '341200', name: '阜阳市' },
      { code: '341300', name: '宿州市' },
      { code: '341500', name: '六安市' },
      { code: '341600', name: '亳州市' },
      { code: '341700', name: '池州市' },
      { code: '341800', name: '宣城市' }
    ]
  }
  // 可以继续添加更多省市数据...
])

// 检测类别选项
const categoryOptions = ref([])
// 检测参数选项映射
const parameterOptionsMap = ref({})
// 检测方法选项映射
const methodOptionsMap = ref({})

// 检测明细项目相关数据
const quotationDetailData = ref([])

// 表单对象
const form = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  businessType: 'sampling', // 默认为一般采样
  contractCode: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  projectLocation: [], // 项目所在地，格式：[省, 市]
  customerSupportIds: [],
  status: '',
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
  businessType: [
    { required: true, message: '业务类别不能为空', trigger: 'change' }
  ]
}

// 重置数据
function resetData() {
  // 重置表单对象
  form.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    businessType: 'sampling', // 重置为默认值
    contractCode: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    projectLocation: [], // 重置项目所在地
    customerSupportIds: [],
    status: '',
    remark: '',
    items: [],
    attachments: []
  }

  // 重置检测明细项目数据
  quotationDetailData.value = []
}

// 获取客服选项
function loadCustomerSupportOptions() {
  // 使用权限key获取客服选项函数
  getCustomerSupportOptionsByKey('customer-support').then(options => {
    customerSupportOptions.value = options
  }).catch(error => {
    console.error('获取客服列表失败:', error)
  })
}

// 获取项目报价详情
function getQuotationDetail(id) {
  getProjectQuotation(id).then(response => {
    const data = response.data

    // 填充表单数据
    form.value = {
      id: data.id,
      projectName: data.projectName,
      projectCode: data.projectCode,
      businessType: data.businessType || 'sampling', // 设置业务类别
      contractCode: data.contractCode,
      customerId: data.customerId,
      customerName: data.customerName,
      customerAddress: data.customerAddress,
      customerContact: data.customerContact,
      customerPhone: data.customerPhone,
      inspectedParty: data.inspectedParty,
      inspectedContact: data.inspectedContact,
      inspectedPhone: data.inspectedPhone,
      inspectedAddress: data.inspectedAddress,
      projectManager: data.projectManager,
      marketManager: data.marketManager,
      technicalManager: data.technicalManager,
      commissionDate: data.commissionDate,
      projectLocation: data.projectLocation ? data.projectLocation.split(',') : [], // 项目所在地，转换字符串为数组
      customerSupportIds: data.customerSupportIds || [],
      status: data.status,
      remark: data.remark,
      items: data.items || [],
      attachments: data.attachments || []
    }

    // 转换现有的items数据为QuotationDetail组件所需的格式
    if (data.items && data.items.length > 0) {
      quotationDetailData.value = data.items.map(item => ({
        id: item.id || '',
        itemUniqueId: item.category || '' + "_" + item.method || '' + "_" + item.parameter || '',
        classification: item.classification || '',
        category: item.category || '',
        qualificationCode: item.qualificationCode || '',
        parameter: item.parameter || '',
        method: item.method || '',
        limitationScope: item.limitationScope || '',
        sampleSource: item.sampleSource || '',
        pointName: item.pointName || '',
        pointCount: item.pointCount || 0,
        cycleType: item.cycleType || '',
        cycleCount: item.cycleCount || 0,
        frequency: item.frequency || 0,
        sampleCount: item.sampleCount || 0,
        isSubcontract: item.isSubcontract,
        remark: item.remark || ''
      }))
    }

    // 获取检测类别选项
    getCategoryOptions().then(response => {
      categoryOptions.value = response.data

      // 预加载检测参数和方法选项
      form.value.items.forEach(item => {
        if (item.category) {
          getParameterOptions({ category: item.category }).then(response => {
            parameterOptionsMap.value[item.category] = response.data
          })

          if (item.parameter) {
            const key = `${item.category}-${item.parameter}`
            getMethodOptions({ category: item.category, parameter: item.parameter }).then(response => {
              methodOptionsMap.value[key] = response.data
            })
          }
        }
      })
    })
  })
}


// 查询客户列表
function queryCustomers(queryString, cb) {
  if (!queryString) {
    cb([])
    return
  }

  // 使用客户管理模块的API接口
  listCustomer({
    name: queryString,
    pageNum: 1,
    pageSize: 20
  }).then(response => {
    const customers = response.rows.map(item => {
      return {
        value: item.name,
        id: item.id,
        data: item
      }
    })
    console.log("customers", customers)
    cb(customers)
  }).catch(error => {
    console.error('查询客户失败:', error)
    cb([])
  })
}

// 选择客户
function handleCustomerSelect(item) {
  form.customerId = item.id
  form.customerAddress = item.data.address
  form.customerContact = item.data.contactName
  form.customerPhone = item.data.contactPhone
  // getCustomer(item.id).then(response => {
  //   const customer = response.data
  //   form.customerAddress = item.data.address || customer.address
  //   form.customerContact = item.data.contactName ||customer.contactName
  //   form.customerPhone = item.data.contactPhone || customer.contactPhone
  // })
}



// 关闭对话框
function closeDialog() {
  console.log('关闭对话框，当前 dialogVisible:', dialogVisible.value)
  dialogVisible.value = false
  console.log('关闭对话框后，dialogVisible 设置为:', dialogVisible.value)
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 提交表单
function submitForm() {
  basicFormRef.value.validate(valid => {
    if (valid) {
      // 验证检测项目
      if (quotationDetailData.value.length === 0) {
        ElMessage.warning('请配置检测明细项目')
        activeTab.value = 'items'
        return
      }

      // 验证每个检测项目的必填字段
      let itemsValid = true
      quotationDetailData.value.forEach((item, index) => {
        if (!item.category || !item.parameter || !item.method) {
          ElMessage.warning(`第 ${index + 1} 行检测项目信息不完整，缺少必要的类别、参数或方法`)
          itemsValid = false
        }
      })

      if (!itemsValid) {
        activeTab.value = 'items'
        return
      }
      // 转换数据格式
      const formData = { ...form.value }

      // 转换项目所在地数组为字符串格式
      if (formData.projectLocation && Array.isArray(formData.projectLocation)) {
        formData.projectLocation = formData.projectLocation.join(',')
      }

      // approvers字段已废弃，使用customerSupportIds
      formData.items = quotationDetailData.value.map(item => ({
        ...item,
        isSubcontract: item.isSubcontract
      }))
      // 调用保存接口
      updateProjectQuotation(formData).then(() => {
        ElMessage.success('保存成功')
        console.log('保存成功，准备关闭对话框')
        dialogVisible.value = false
        emit('update:visible', false)
        emit('refresh')
      }).catch(error => {
        console.error('保存失败:', error)
        if (error.response && error.response.data) {
          ElMessage.error(`保存失败: ${error.response.data.message || '未知错误'}`)
        } else {
          ElMessage.error('保存失败，请检查表单数据')
        }
      })
    }
  })
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}
</style>