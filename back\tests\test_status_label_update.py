#!/usr/bin/env python3
"""
测试状态标签更新
"""

import sys
sys.path.insert(0, '.')

def test_status_labels():
    """
    测试状态标签更新
    """
    print("测试状态标签更新...")
    
    try:
        # 导入项目报价服务
        from module_quotation.service.project_quotation_service import ProjectQuotationService
        
        # 创建服务实例（不需要数据库连接来测试标签方法）
        service = ProjectQuotationService(None)
        
        # 测试基础状态标签
        print("\n📋 测试基础状态标签:")
        status_tests = [
            ("0", "草稿"),
            ("1", "待审核"),
            ("2", "审核完成"),  # 这里应该是"审核完成"而不是"已审核"
            ("3", "已撤回"),
            ("4", "已拒绝")
        ]
        
        for status, expected_label in status_tests:
            actual_label = service.get_status_label(status)
            if actual_label == expected_label:
                print(f"  ✅ 状态 {status}: {actual_label}")
            else:
                print(f"  ❌ 状态 {status}: 期望 '{expected_label}', 实际 '{actual_label}'")
        
        # 测试详细状态标签
        print("\n📋 测试详细状态标签:")
        detailed_status_tests = [
            ("0", "草稿"),
            ("1", "待审核"),
            ("2", "审核完成"),  # 这里应该是"审核完成"而不是"已审核"
            ("3", "已撤回"),
            ("4", "已拒绝"),
            ("1-market", "待审核（市场）"),
            ("1-lab", "待审核（实验室）"),
            ("1-field", "待审核（现场）"),
            ("1-lab|field", "待审核（实验室|现场）")
        ]
        
        for detailed_status, expected_label in detailed_status_tests:
            actual_label = service.get_detailed_status_label(detailed_status)
            if actual_label == expected_label:
                print(f"  ✅ 详细状态 {detailed_status}: {actual_label}")
            else:
                print(f"  ❌ 详细状态 {detailed_status}: 期望 '{expected_label}', 实际 '{actual_label}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_frontend_status_mapping():
    """
    测试前端状态映射（模拟）
    """
    print("\n📋 测试前端状态映射:")
    
    # 模拟前端的状态映射
    frontend_status_map = {
        '0': '草稿',
        '1': '待审核',
        '2': '审核完成',  # 这里应该是"审核完成"
        '3': '已撤回',
        '4': '已拒绝'
    }
    
    expected_mappings = [
        ('0', '草稿'),
        ('1', '待审核'),
        ('2', '审核完成'),
        ('3', '已撤回'),
        ('4', '已拒绝')
    ]
    
    for status, expected_label in expected_mappings:
        actual_label = frontend_status_map.get(status, '未知')
        if actual_label == expected_label:
            print(f"  ✅ 前端状态 {status}: {actual_label}")
        else:
            print(f"  ❌ 前端状态 {status}: 期望 '{expected_label}', 实际 '{actual_label}'")
    
    return True


def main():
    """
    主函数
    """
    print("开始测试状态标签更新\n")
    
    success = True
    
    # 测试后端状态标签
    if not test_status_labels():
        success = False
    
    # 测试前端状态映射
    if not test_frontend_status_mapping():
        success = False
    
    if success:
        print("\n🎉 状态标签更新测试通过！")
        print("\n✅ 更新内容:")
        print("  • 后端服务状态标签: '已审核' → '审核完成'")
        print("  • 前端状态选项: '已审核' → '审核完成'")
        print("  • 前端状态标签方法: '已审核' → '审核完成'")
        print("  • 组件状态标签: '已审核' → '审核完成'")
        print("  • 文档状态映射: '已审核' → '审核完成'")
        
        print("\n📊 状态标签对照表:")
        print("  | 状态码 | 状态标签 | 说明 |")
        print("  |--------|----------|------|")
        print("  | 0 | 草稿 | 项目创建后的初始状态 |")
        print("  | 1 | 待审核 | 已提交审批，等待处理 |")
        print("  | 2 | 审核完成 | 所有必需审批都已通过 |")
        print("  | 3 | 已撤回 | 项目已被撤回 |")
        print("  | 4 | 已拒绝 | 审批被拒绝 |")
        
        print("\n🚀 状态标签已统一更新完成！")
    else:
        print("\n❌ 状态标签更新测试失败")
    
    return success


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
