1. 前端采用Vue3、Element Plus，基于RuoYi-Vue3前端项目修改。
2. 后端采用FastAPI、sqlalchemy[asyncio]、MySQL、Redis、OAuth2 & Jwt。
3. 权限认证使用OAuth2 & Jwt，支持多终端认证系统。
4. 支持加载动态权限菜单，多方式轻松权限控制。
5. 前端变量命名为驼峰命名法。
6. 后端变量命名为下划线命名法，数据库字段命名为下划线命名法，通过CamelCaseUtil中的方法进行转换。
7. 所有表中的操作人，创建人，更新人都是系统用户表的外键，关联系统用户表的id。
8. 所有功能的实现和修改，都要考虑前后端的变动。
9. 如果有数据库结构变更，生成表变更sql文件，放在back/migrations目录下。
10. 尽量生成单元测试，后端单元测试在back/tests 目录下编写。
11. 可以使用 MySQL Server MCP 提供的工具查看和操作数据库中的表结构和数据。
12. 文档目录在docs目录下，生成的文档也放在该目录下。
13. 通过单元测试，测试功能的完整性和正确性，后端单元测试在back/tests 目录下。
14. 假设后端服务已经启动，后端运行地址为http://127.0.0.1:9099，后端代码会热更新，不用重新启动服务，测试日志在back/logs目录下。
15. 需要保证运行环境在back/venv虚拟环境中。
16. 进行接口测试时，header中添加"Authorization: Bearer test_token"可以跳过认证，接口的路径，不需要加上dev-api前缀，接口测试也通过生成测试代码完成，不要直接在终端中执行命令。
