# 项目报价明细批量导入功能实现文档

## 功能概述

本文档描述了项目报价模块新增的批量导入功能实现，允许用户通过Excel文件批量导入项目报价明细数据。

## 功能特性

### 1. 批量导入功能
- 支持Excel文件拖拽上传
- 提供标准Excel模板下载
- 支持数据验证和错误反馈
- 详细的错误信息展示

### 2. Excel模板格式
```
分类 | 二级分类 | 指标 | 方法 | 样品来源 | 点位名称 | 点位数 | 周期类型 | 检测周期数 | 检测频次数 | 样品数 | 是否分包 | 备注
```

## 技术实现

### 后端实现

#### 1. 数据模型

**导入模型**：
```python
class ProjectQuotationItemImportModel(BaseModel):
    classification: Optional[str] = Field(default=None, description="分类")
    category: str = Field(..., description="二级分类")
    parameter: str = Field(..., description="指标")
    method: str = Field(..., description="方法")
    sample_source: Optional[str] = Field(default=None, description="样品来源")
    point_name: Optional[str] = Field(default=None, description="点位名称")
    point_count: Optional[str] = Field(default="1", description="点位数")
    cycle_type: Optional[str] = Field(default=None, description="周期类型")
    cycle_count: Optional[str] = Field(default="1", description="检测周期数")
    frequency: Optional[str] = Field(default="1", description="检测频次数")
    sample_count: Optional[str] = Field(default="1", description="样品数")
    is_subcontract: Optional[str] = Field(default="否", description="是否分包")
    remark: Optional[str] = Field(default=None, description="备注")
```

**错误模型**：
```python
class ProjectQuotationItemImportErrorModel(BaseModel):
    row_number: int = Field(..., description="错误行号")
    error_message: str = Field(..., description="错误信息")
    classification: Optional[str] = Field(default=None, description="分类")
    category: Optional[str] = Field(default=None, description="二级分类")
    parameter: Optional[str] = Field(default=None, description="指标")
    method: Optional[str] = Field(default=None, description="方法")
    # ... 其他字段
```

**结果模型**：
```python
class ProjectQuotationItemImportResultModel(BaseModel):
    success: bool = Field(..., description="是否成功")
    total_count: int = Field(..., description="总记录数")
    success_count: int = Field(..., description="成功记录数")
    error_count: int = Field(..., description="错误记录数")
    errors: List[ProjectQuotationItemImportErrorModel] = Field(default=[], description="错误列表")
    data: List[ProjectQuotationItemModel] = Field(default=[], description="成功导入的数据")
```

#### 2. 导入服务

**ProjectQuotationItemImportService**：
```python
class ProjectQuotationItemImportService:
    async def import_from_excel(self, file: UploadFile, current_user: CurrentUserModel):
        # Excel文件解析
        # 数据验证
        # 格式转换
        # 错误处理
        
    async def generate_template(self):
        # 生成Excel模板
        
    async def _validate_and_convert_row(self, row: pd.Series, row_number: int):
        # 验证和转换行数据
```

**数据验证规则**：
- 必填字段：二级分类、指标、方法
- 数值字段：点位数、检测周期数、检测频次数、样品数
- 枚举字段：是否分包（是/否 或 0/1）

#### 3. API接口

```python
@router.get('/items/import/template', summary='下载项目报价明细批量导入模板')
async def download_import_template():
    # 下载Excel模板

@router.post('/items/import', response_model=ProjectQuotationItemImportResultModel, summary='批量导入项目报价明细')
async def import_project_quotation_items(file: UploadFile = File(...)):
    # 批量导入处理
```

### 前端实现

#### 1. API接口

```javascript
// 下载项目报价明细批量导入模板
export function downloadProjectQuotationItemsImportTemplate() {
  return request({
    url: '/quotation/project-quotation/items/import/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导入项目报价明细
export function importProjectQuotationItems(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/quotation/project-quotation/items/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

#### 2. 批量导入组件

**ProjectQuotationItemImport组件特性**：
- 拖拽上传支持
- 文件类型和大小验证
- 模板下载功能
- 详细的字段说明
- 错误信息展示
- 成功数据确认导入

**组件结构**：
```vue
<template>
  <!-- 批量导入按钮 -->
  <el-button type="success" @click="openImportDialog">
    批量导入
  </el-button>

  <!-- 批量导入对话框 -->
  <el-dialog v-model="importDialogVisible" title="批量导入项目报价明细">
    <!-- 文件上传区域 -->
    <el-upload drag :auto-upload="false" :on-change="handleFileChange">
      <!-- 拖拽上传界面 -->
    </el-upload>
    
    <!-- 模板下载区域 -->
    <el-button @click="downloadTemplate">下载模板</el-button>
    
    <!-- 字段说明 -->
    <div class="template-fields">
      <!-- 详细的字段说明 -->
    </div>
  </el-dialog>

  <!-- 错误详情对话框 -->
  <el-dialog v-model="errorDialogVisible" title="导入错误详情">
    <!-- 错误信息表格 -->
    <el-table :data="importResult.errors">
      <!-- 错误详情列 -->
    </el-table>
  </el-dialog>
</template>
```

#### 3. 主页面集成

**QuotationDetailTable.vue更新**：
- 在"配置检测明细项目"按钮旁边添加批量导入按钮
- 集成ProjectQuotationItemImport组件
- 处理导入成功事件

```vue
<template>
  <div style="margin-bottom: 20px">
    <el-button type="primary" @click="openQuotationDetail">配置检测明细项目</el-button>
    <project-quotation-item-import @import-success="handleImportSuccess" style="margin-left: 10px;" />
  </div>
</template>

<script setup>
// 处理批量导入成功
function handleImportSuccess(importedData) {
  // 将导入的数据添加到现有数据中
  const newData = [...quotationDetailData.value, ...importedData]
  quotationDetailData.value = newData
  
  // 更新组件状态
  quotationDetailState.value = convertToCurrentState(newData)
  
  // 通知父组件
  emit('update:modelValue', newData)
  
  ElMessage.success(`成功导入 ${importedData.length} 条检测明细项目`)
}
</script>
```

## Excel模板格式

### 模板列结构

| 列名 | 说明 | 是否必填 | 示例值 |
|------|------|----------|--------|
| 分类 | 检测分类 | 否 | 气、水、土壤 |
| 二级分类 | 具体检测类别 | 是 | 环境空气和废气 |
| 指标 | 检测指标名称 | 是 | PM2.5 |
| 方法 | 检测方法描述 | 是 | 重量法 |
| 样品来源 | 样品来源描述 | 否 | 环境空气 |
| 点位名称 | 采样点位名称 | 否 | 监测点1 |
| 点位数 | 点位数量 | 否 | 1 |
| 周期类型 | 检测周期类型 | 否 | 天、月、季度 |
| 检测周期数 | 检测周期数量 | 否 | 1 |
| 检测频次数 | 检测频次数量 | 否 | 1 |
| 样品数 | 样品数量 | 否 | 1 |
| 是否分包 | 是否分包 | 否 | 是/否 |
| 备注 | 备注信息 | 否 | 常规监测 |

### 示例数据

```
分类    二级分类        指标      方法            样品来源    点位名称    点位数  周期类型  检测周期数  检测频次数  样品数  是否分包  备注
气      环境空气和废气  PM2.5     重量法          环境空气    监测点1     1       天        1           1           1       否        常规监测
水      水和废水        pH值      玻璃电极法      地表水      监测点2     2       月        1           2           1       否        重点监测
土壤    土壤和沉积物    重金属    原子吸收法      土壤样品    监测点3     1       季度      1           1           1       是        特殊监测
```

## 错误处理

### 错误类型

1. **文件格式错误**：
   - 不支持的文件类型
   - 文件大小超限
   - Excel格式损坏

2. **模板格式错误**：
   - 缺少必要列
   - 列名不匹配

3. **数据验证错误**：
   - 必填字段为空
   - 数据格式不正确
   - 数值字段非数字

### 错误信息展示

**错误详情表格**：
```
错误行 | 错误信息 | 分类 | 二级分类 | 指标 | 方法 | 样品来源 | 点位名称 | 点位数 | 周期类型 | 检测周期数 | 检测频次数 | 样品数 | 是否分包 | 备注
2      | 二级分类不能为空 | 气 | | PM2.5 | 重量法 | 环境空气 | 监测点1 | 1 | 天 | 1 | 1 | 1 | 否 | 常规监测
3      | 点位数必须为正整数 | 水 | 水和废水 | pH值 | 玻璃电极法 | 地表水 | 监测点2 | abc | 月 | 1 | 2 | 1 | 否 | 重点监测
```

## 功能流程

### 1. 用户操作流程

1. **下载模板**：
   - 点击"批量导入"按钮
   - 点击"下载模板"获取Excel模板

2. **填写数据**：
   - 按照模板格式填写数据
   - 注意必填字段和数据格式

3. **上传导入**：
   - 拖拽或选择Excel文件
   - 点击"开始导入"

4. **查看结果**：
   - 查看导入成功数量
   - 如有错误，查看错误详情
   - 确认导入成功数据

### 2. 系统处理流程

```
文件上传 → 格式验证 → 数据解析 → 逐行验证 → 数据转换 → 返回结果 → 前端处理 → 更新界面
```

## 部署说明

### 后端部署

1. **依赖安装**：
   ```bash
   pip install pandas openpyxl
   ```

2. **API路由注册**：
   - 确保导入服务正确注册
   - 验证API接口可访问

### 前端部署

1. **组件注册**：
   - 确保ProjectQuotationItemImport组件正确导入
   - 验证Element Plus组件

2. **权限配置**：
   - 添加批量导入权限控制
   - 配置文件上传限制

## 使用说明

### 注意事项

1. **数据准备**：
   - 确保必填字段完整
   - 检查数据格式正确性
   - 数值字段必须为正整数

2. **文件要求**：
   - 仅支持.xlsx和.xls格式
   - 文件大小不超过10MB
   - 使用提供的模板格式

3. **错误处理**：
   - 仔细查看错误信息
   - 修正错误后重新导入
   - 可以确认导入成功的记录

## 总结

项目报价明细批量导入功能提供了：
- ✅ 完整的批量导入流程
- ✅ 友好的用户界面
- ✅ 详细的错误反馈
- ✅ 数据验证和格式转换
- ✅ 灵活的数据处理

该功能大大提高了项目报价明细数据录入的效率，为用户提供了便捷的批量数据管理能力。
