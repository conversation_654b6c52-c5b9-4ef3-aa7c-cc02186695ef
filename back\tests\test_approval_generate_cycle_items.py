import pytest
import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_admin.entity.vo.user_vo import CurrentUserModel, UserModel
from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel


class TestApprovalGenerateCycleItems:
    """测试审批通过生成检测周期条目功能"""
    
    @pytest.fixture
    async def setup_test_data(self, db_session: AsyncSession):
        """设置测试数据"""
        # 创建测试项目报价
        quotation = ProjectQuotation(
            project_name="测试项目",
            project_code="TEST001",
            status="1",  # 待审核状态
            business_type="lab",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        # 创建测试项目报价明细
        quotation_item1 = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            item_code="ITEM001",
            parameter="测试参数1",
            cycle_count=2,  # 2个周期
            create_by=1,
            create_time=datetime.now()
        )
        
        quotation_item2 = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            item_code="ITEM002",
            parameter="测试参数2",
            cycle_count=1,  # 1个周期
            create_by=1,
            create_time=datetime.now()
        )
        
        db_session.add_all([quotation_item1, quotation_item2])
        
        # 创建审批记录
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=quotation.id,
            approver_type="lab",
            approval_stage=1,
            approval_status="pending",
            is_required="1",
            is_current_round="1",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(approval_record)
        
        await db_session.commit()
        
        return {
            "quotation": quotation,
            "quotation_items": [quotation_item1, quotation_item2],
            "approval_record": approval_record
        }
    
    @pytest.fixture
    def current_user(self):
        """创建当前用户"""
        user = UserModel(
            user_id=1,
            user_name="test_user",
            nick_name="测试用户"
        )
        return CurrentUserModel(
            user=user,
            roles=["admin", "lab-approver"],
            permissions=[]
        )
    
    async def test_generate_cycle_items_on_approval(self, db_session: AsyncSession, setup_test_data, current_user):
        """测试审批通过时生成检测周期条目"""
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        approval_record = test_data["approval_record"]
        
        # 创建审批服务
        approval_service = ProjectQuotationApprovalService(db_session)
        
        # 执行审批操作
        approval_action = ApprovalActionModel(
            projectQuotationId=quotation.id,
            approvalStatus="approved",
            approvalOpinion="审批通过"
        )
        
        await approval_service.perform_approval(quotation.id, approval_action, current_user)
        
        # 验证项目报价状态已更新为"2"
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == quotation.id)
        result = await db_session.execute(stmt)
        updated_quotation = result.scalar_one()
        assert updated_quotation.status == "2"
        
        # 验证检测周期条目已生成
        cycle_stmt = select(DetectionCycleItem).where(DetectionCycleItem.project_quotation_id == quotation.id)
        cycle_result = await db_session.execute(cycle_stmt)
        cycle_items = cycle_result.scalars().all()
        
        # 应该生成3个周期条目（第一个明细2个周期 + 第二个明细1个周期）
        assert len(cycle_items) == 3
        
        # 验证周期条目的详细信息
        item1_cycles = [item for item in cycle_items if item.project_quotation_item_id == test_data["quotation_items"][0].id]
        item2_cycles = [item for item in cycle_items if item.project_quotation_item_id == test_data["quotation_items"][1].id]
        
        assert len(item1_cycles) == 2  # 第一个明细应该有2个周期
        assert len(item2_cycles) == 1  # 第二个明细应该有1个周期
        
        # 验证周期编号
        item1_cycle_numbers = sorted([item.cycle_number for item in item1_cycles])
        assert item1_cycle_numbers == [1, 2]
        
        item2_cycle_numbers = [item.cycle_number for item in item2_cycles]
        assert item2_cycle_numbers == [1]
        
        # 验证状态为未分配
        for item in cycle_items:
            assert item.status == "unassigned"
            assert item.create_by == current_user.user.user_id
    
    async def test_no_duplicate_cycle_items(self, db_session: AsyncSession, setup_test_data, current_user):
        """测试不会重复生成检测周期条目"""
        test_data = await setup_test_data
        quotation = test_data["quotation"]
        
        # 创建审批服务
        approval_service = ProjectQuotationApprovalService(db_session)
        
        # 第一次审批通过
        approval_action = ApprovalActionModel(
            projectQuotationId=quotation.id,
            approvalStatus="approved",
            approvalOpinion="审批通过"
        )
        
        await approval_service.perform_approval(quotation.id, approval_action, current_user)
        
        # 获取第一次生成的周期条目数量
        cycle_stmt = select(DetectionCycleItem).where(DetectionCycleItem.project_quotation_id == quotation.id)
        cycle_result = await db_session.execute(cycle_stmt)
        first_cycle_items = cycle_result.scalars().all()
        first_count = len(first_cycle_items)
        
        # 模拟再次调用生成方法（不应该重复生成）
        await approval_service._generate_detection_cycle_items(quotation.id, current_user.user.user_id)
        
        # 验证周期条目数量没有增加
        cycle_result2 = await db_session.execute(cycle_stmt)
        second_cycle_items = cycle_result2.scalars().all()
        second_count = len(second_cycle_items)
        
        assert first_count == second_count
        assert first_count == 3  # 应该还是3个
    
    async def test_handle_zero_cycle_count(self, db_session: AsyncSession, current_user):
        """测试处理周期数为0或空的情况"""
        # 创建测试项目报价
        quotation = ProjectQuotation(
            project_name="测试项目",
            project_code="TEST002",
            status="1",
            business_type="lab",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        # 创建周期数为0的明细
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            item_code="ITEM003",
            parameter="测试参数3",
            cycle_count=0,  # 周期数为0
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        
        # 创建审批记录
        approval_record = ProjectQuotationApprovalRecord(
            project_quotation_id=quotation.id,
            approver_type="lab",
            approval_stage=1,
            approval_status="pending",
            is_required="1",
            is_current_round="1",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(approval_record)
        await db_session.commit()
        
        # 创建审批服务并执行审批
        approval_service = ProjectQuotationApprovalService(db_session)
        approval_action = ApprovalActionModel(
            projectQuotationId=quotation.id,
            approvalStatus="approved",
            approvalOpinion="审批通过"
        )
        
        await approval_service.perform_approval(quotation.id, approval_action, current_user)
        
        # 验证生成了1个周期条目（默认值）
        cycle_stmt = select(DetectionCycleItem).where(DetectionCycleItem.project_quotation_id == quotation.id)
        cycle_result = await db_session.execute(cycle_stmt)
        cycle_items = cycle_result.scalars().all()
        
        assert len(cycle_items) == 1
        assert cycle_items[0].cycle_number == 1