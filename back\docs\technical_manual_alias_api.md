# 技术手册别名和特殊耗材价格功能API文档

## 概述

本文档描述了技术手册模块新增的别名和特殊耗材价格功能的API接口和使用方法。

## 功能特性

### 1. 别名功能
- **多别名支持**: 每个技术手册可以有多个别名
- **JSON存储**: 别名以JSON数组格式存储在数据库中
- **别名查询**: 支持根据单个别名查询技术手册
- **标签管理**: 前端以标签形式展示和管理别名

### 2. 特殊耗材价格功能
- **价格字段**: 新增特殊耗材价格字段
- **数值验证**: 支持小数点后2位精度
- **前后端支持**: 完整的新增和修改功能

## 数据库变更

### 技术手册表 (technical_manual)

#### 新增字段
```sql
-- 别名列表（JSON格式）
alias_list JSON COMMENT '别名列表（JSON格式）'

-- 特殊耗材价格字段已存在
special_consumables_price DECIMAL(10,2) COMMENT '分析特殊耗材单价'
```

#### 字段说明
- `alias_list`: JSON数组，存储多个别名，如 `["别名1", "别名2", "别名3"]`
- `common_alias`: 保留字段，标记为已废弃
- `special_consumables_price`: 特殊耗材单价，支持小数点后2位

## API接口

### 1. 技术手册查询接口

#### 获取技术手册列表
```http
GET /api/basedata/technical-manual/list
```

**查询参数**:
```json
{
  "classification": "分类（可选）",
  "category": "检测类别（可选）",
  "parameter": "检测参数（可选）",
  "method": "检测方法（可选）",
  "alias": "别名查询（可选）",
  "keyword": "关键词（可选）",
  "qualificationCode": "资质编号（可选）",
  "beginTime": "开始时间（可选）",
  "endTime": "结束时间（可选）"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryCode": "CATE00001",
      "parameter": "pH值",
      "method": "玻璃电极法",
      "aliasList": ["酸碱度", "氢离子浓度", "pH"],
      "specialConsumablesPrice": 15.50,
      "classification": "水",
      "category": "水和废水",
      "qualificationCode": "CMA001",
      "limitationScope": "地表水、地下水",
      "qualificationDate": "2023-01-01",
      "hasQualification": "0",
      "status": "0",
      "createTime": "2024-01-01 10:00:00",
      "remark": "备注信息"
    }
  ]
}
```

#### 分页查询技术手册
```http
GET /api/basedata/technical-manual/page
```

**查询参数**: 同上，额外包含分页参数
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "alias": "酸碱度"
}
```

### 2. 技术手册管理接口

#### 新增技术手册
```http
POST /api/basedata/technical-manual
```

**请求体**:
```json
{
  "categoryCode": "CATE00001",
  "parameter": "pH值",
  "method": "玻璃电极法",
  "aliasList": ["酸碱度", "氢离子浓度"],
  "specialConsumablesPrice": 15.50,
  "qualificationCode": "CMA001",
  "limitationScope": "地表水、地下水",
  "qualificationDate": "2023-01-01",
  "hasQualification": "0",
  "status": "0",
  "remark": "备注信息"
}
```

#### 编辑技术手册
```http
PUT /api/basedata/technical-manual
```

**请求体**: 同新增，需包含id字段
```json
{
  "id": 1,
  "categoryCode": "CATE00001",
  "parameter": "pH值",
  "method": "玻璃电极法",
  "aliasList": ["酸碱度", "氢离子浓度", "pH"],
  "specialConsumablesPrice": 18.00,
  "qualificationCode": "CMA001",
  "limitationScope": "地表水、地下水、废水",
  "qualificationDate": "2023-01-01",
  "hasQualification": "0",
  "status": "0",
  "remark": "更新备注"
}
```

### 3. 批量操作接口

#### 批量录入技术手册
```http
POST /api/basedata/technical-manual/batch-input
```

**请求体**:
```json
{
  "classification": "水",
  "categories": "水和废水,地表水",
  "parameters": "pH值,溶解氧",
  "methods": "玻璃电极法,电化学探头法",
  "aliasList": ["酸碱度,氢离子浓度", "DO,溶氧"],
  "specialConsumablesPrice": 15.50,
  "qualificationCode": "CMA001",
  "limitationScope": "地表水、地下水",
  "qualificationDate": "2023-01-01",
  "hasQualification": "0",
  "status": "0",
  "remark": "批量录入"
}
```

#### 批量更新技术手册
```http
POST /api/basedata/technical-manual/batch-update
```

**请求体**:
```json
{
  "ids": [1, 2, 3],
  "aliasList": ["通用别名1", "通用别名2"],
  "specialConsumablesPrice": 20.00,
  "status": "0",
  "remark": "批量更新"
}
```

## 前端实现指南

### 1. 别名输入组件

#### 组件特性
- 输入框 + 添加按钮
- 标签形式显示已添加的别名
- 支持删除单个别名
- 防重复添加

#### 关键代码
```vue
<template>
  <div class="alias-input-container">
    <!-- 输入框 -->
    <el-input
      v-model="currentAlias"
      placeholder="请输入别名，按回车添加"
      @keyup.enter="addAlias"
      @blur="addAlias"
    />
    <el-button @click="addAlias">添加</el-button>
    
    <!-- 标签显示 -->
    <div class="alias-tags">
      <el-tag
        v-for="(alias, index) in aliasList"
        :key="index"
        closable
        @close="removeAlias(index)"
      >
        {{ alias }}
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentAlias: '',
      aliasList: []
    }
  },
  methods: {
    addAlias() {
      const alias = this.currentAlias.trim()
      if (!alias || this.aliasList.includes(alias)) return
      
      this.aliasList.push(alias)
      this.currentAlias = ''
    },
    removeAlias(index) {
      this.aliasList.splice(index, 1)
    }
  }
}
</script>
```

### 2. 特殊耗材价格输入

```vue
<el-form-item label="特殊耗材价格">
  <el-input-number
    v-model="form.specialConsumablesPrice"
    :precision="2"
    :min="0"
    :max="999999.99"
    placeholder="请输入特殊耗材价格"
  />
  <span>元</span>
</el-form-item>
```

### 3. 别名查询

```vue
<el-form-item label="别名查询">
  <el-input
    v-model="queryForm.alias"
    placeholder="请输入要查询的别名"
    clearable
  />
</el-form-item>
```

## 数据库查询示例

### 1. 别名查询
```sql
-- 查询包含特定别名的技术手册
SELECT * FROM technical_manual 
WHERE JSON_CONTAINS(alias_list, '"酸碱度"');

-- 模糊查询别名
SELECT * FROM technical_manual 
WHERE JSON_SEARCH(alias_list, 'one', '%pH%') IS NOT NULL;

-- 获取别名数量
SELECT id, parameter, JSON_LENGTH(alias_list) as alias_count
FROM technical_manual 
WHERE alias_list IS NOT NULL;

-- 获取第一个别名
SELECT id, parameter, JSON_EXTRACT(alias_list, '$[0]') as first_alias
FROM technical_manual 
WHERE alias_list IS NOT NULL;
```

### 2. 特殊耗材价格查询
```sql
-- 查询有特殊耗材价格的技术手册
SELECT * FROM technical_manual 
WHERE special_consumables_price IS NOT NULL 
AND special_consumables_price > 0;

-- 按价格范围查询
SELECT * FROM technical_manual 
WHERE special_consumables_price BETWEEN 10.00 AND 50.00;
```

## 注意事项

### 1. 数据验证
- 别名不能为空字符串
- 别名不能重复
- 特殊耗材价格必须为非负数
- JSON格式必须正确

### 2. 性能考虑
- JSON查询可能比普通字段查询慢
- 建议为常用查询添加索引
- 大量数据时考虑分页查询

### 3. 兼容性
- 保留了 `common_alias` 字段以确保向后兼容
- 新功能不影响现有数据
- 支持渐进式升级

### 4. 错误处理
- JSON格式错误时的处理
- 别名重复时的提示
- 价格超出范围时的验证

## 测试用例

### 1. 别名功能测试
```javascript
// 添加别名
const aliases = ['别名1', '别名2', '别名3']
// 查询别名
const result = await api.get('/api/basedata/technical-manual/list?alias=别名1')
// 验证结果
expect(result.data.length).toBeGreaterThan(0)
```

### 2. 特殊耗材价格测试
```javascript
// 新增带价格的技术手册
const data = {
  categoryCode: 'CATE00001',
  parameter: '测试参数',
  method: '测试方法',
  specialConsumablesPrice: 25.50
}
const result = await api.post('/api/basedata/technical-manual', data)
expect(result.code).toBe(200)
```

这个功能增强了技术手册的管理能力，提供了更灵活的别名管理和完整的特殊耗材价格支持。
