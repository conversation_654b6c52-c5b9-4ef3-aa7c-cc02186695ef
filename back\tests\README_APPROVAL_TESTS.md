# 项目报价审批流程测试文档

## 概述

本测试套件全面测试了项目报价审批流程的所有相关功能，包括DAO层、服务层、API层和完整的集成测试。

## 测试文件结构

```
tests/
├── test_approval_dao.py              # DAO层测试
├── test_approval_service.py          # 服务层测试  
├── test_approval_api.py              # API接口测试
├── test_approval_integration.py      # 集成测试
├── test_project_quotation_approval.py # 原始审批测试
├── test_data_factory.py              # 测试数据工厂
├── run_approval_tests.py             # 测试运行脚本
├── pytest.ini                        # pytest配置
└── README_APPROVAL_TESTS.md          # 本文档
```

## 测试覆盖范围

### 1. DAO层测试 (`test_approval_dao.py`)

测试数据访问层的所有功能：

- ✅ 根据项目报价ID获取审批记录
- ✅ 根据用户和项目获取审批记录
- ✅ 获取用户的待审批记录
- ✅ 根据审批阶段获取记录
- ✅ 获取必需的审批记录
- ✅ 删除项目的所有审批记录
- ✅ 分页查询审批记录
- ✅ 查询审批记录列表
- ✅ 基本CRUD操作

### 2. 服务层测试 (`test_approval_service.py`)

测试业务逻辑层的核心功能：

- ✅ 一般采样项目的审批记录初始化
- ✅ 送样项目的审批记录初始化
- ✅ 审批阶段控制逻辑
- ✅ 获取待审批项目列表
- ✅ 审批状态计算

### 3. API接口测试 (`test_approval_api.py`)

测试REST API接口：

- ✅ 初始化审批记录API
- ✅ 获取审批状态API
- ✅ 提交审批API
- ✅ 执行审批操作API
- ✅ 获取待审批项目列表API
- ✅ API端点存在性检查
- ✅ API响应格式验证

### 4. 集成测试 (`test_approval_integration.py`)

测试完整的业务流程：

- ✅ 完整的一般采样审批流程
- ✅ 完整的送样审批流程
- ✅ 审批拒绝流程
- ✅ 待审批列表功能

### 5. 原始测试 (`test_project_quotation_approval.py`)

包含原有的审批测试和新增的API测试。

## 审批流程业务规则

### 业务类型

1. **一般采样 (sampling)**
   - 市场审批 → (实验室审批 + 现场审批)
   - 实验室和现场审批可以并行进行
   - 所有审批都是必需的

2. **送样 (sample)**
   - 市场审批 → 实验室审批
   - 现场审批不是必需的

### 审批阶段

- **阶段1**: 市场审批（必须先完成）
- **阶段2**: 技术审批（实验室 + 现场，可并行）

### 审批状态

- `pending`: 待审批
- `approved`: 已通过
- `rejected`: 已拒绝

### 项目状态

- `0`: 草稿
- `1`: 待审核
- `2`: 已审核
- `3`: 已撤回
- `4`: 已拒绝

## 运行测试

### 环境检查

```bash
# 检查测试环境
python tests/run_approval_tests.py check
```

### 运行所有测试

```bash
# 运行所有审批相关测试
python tests/run_approval_tests.py all
```

### 运行特定测试

```bash
# 运行DAO层测试
python tests/run_approval_tests.py dao

# 运行服务层测试
python tests/run_approval_tests.py service

# 运行API测试
python tests/run_approval_tests.py api

# 运行集成测试
python tests/run_approval_tests.py integration

# 运行原始测试
python tests/run_approval_tests.py original
```

### 使用pytest直接运行

```bash
# 运行特定测试文件
pytest tests/test_approval_dao.py -v

# 运行特定测试方法
pytest tests/test_approval_service.py::TestApprovalService::test_init_approval_records_sampling -v

# 运行所有审批相关测试
pytest tests/test_approval_*.py -v
```

## 测试数据

测试使用 `TestDataFactory` 类生成标准化的测试数据：

- **角色**: 市场审批人员、实验室审批人员、现场审批人员
- **用户**: 对应各角色的测试用户
- **项目报价**: 一般采样、送样、复杂项目等不同类型
- **审批记录**: 各种状态的审批记录

## 常见问题

### 1. 测试失败：模块导入错误

确保项目根目录在Python路径中：

```bash
export PYTHONPATH=/path/to/lims2/back:$PYTHONPATH
```

### 2. 数据库连接错误

检查测试数据库配置，确保测试使用独立的数据库。

### 3. 异步测试问题

确保安装了 `pytest-asyncio`：

```bash
pip install pytest-asyncio
```

### 4. 权限相关测试失败

某些API测试可能需要模拟认证，检查测试中的认证逻辑。

## 测试最佳实践

1. **隔离性**: 每个测试都是独立的，不依赖其他测试的结果
2. **可重复性**: 测试可以多次运行，结果一致
3. **清理**: 测试后自动清理数据，不影响其他测试
4. **覆盖性**: 测试覆盖正常流程、异常情况和边界条件

## 扩展测试

如果需要添加新的测试：

1. 在相应的测试文件中添加测试方法
2. 使用 `TestDataFactory` 生成测试数据
3. 遵循现有的测试命名和结构约定
4. 添加适当的断言和错误处理

## 性能测试

对于性能敏感的操作，可以添加性能测试：

```python
import time

def test_approval_performance():
    start_time = time.time()
    # 执行操作
    end_time = time.time()
    assert end_time - start_time < 1.0  # 确保在1秒内完成
```

## 测试报告

运行测试后，可以生成详细的测试报告：

```bash
pytest tests/test_approval_*.py --html=report.html --self-contained-html
```

这将生成一个包含所有测试结果的HTML报告。
