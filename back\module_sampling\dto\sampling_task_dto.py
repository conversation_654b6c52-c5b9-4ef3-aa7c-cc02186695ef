from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime, date
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel
from .detection_cycle_item_dto import DetectionCycleItemDTO


class SamplingTaskItemDTO(BaseModel):
    """采样任务项目DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    item_id: int
    cycles: List[int]

class SamplingTaskCreateDTO(BaseModel):
    """采样任务创建DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    task_name: str
    description: Optional[str] = None
    project_quotation_id: int
    items: List[SamplingTaskItemDTO]


class SamplingTaskUpdateDTO(BaseModel):
    """采样任务更新DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    task_name: Optional[str] = None
    task_description: Optional[str] = None
    planned_start_date: Optional[date] = None
    planned_end_date: Optional[date] = None
    actual_start_date: Optional[date] = None
    actual_end_date: Optional[date] = None
    sampling_location: Optional[str] = None
    sampling_method: Optional[str] = None
    sample_count: Optional[int] = None
    status: Optional[int] = None
    remarks: Optional[str] = None


class SamplingTaskDTO(BaseModel):
    """采样任务DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    id: int
    project_quotation_id: int
    task_name: str
    task_code: str
    task_description: Optional[str] = None
    planned_start_date: Optional[date] = None
    planned_end_date: Optional[date] = None
    actual_start_date: Optional[date] = None
    actual_end_date: Optional[date] = None
    sampling_location: Optional[str] = None
    sampling_method: Optional[str] = None
    sample_count: Optional[int] = None
    status: int
    remarks: Optional[str] = None
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None
    
    # 关联信息
    project_name: Optional[str] = None
    assigned_user_name: Optional[str] = None
    cycle_items: Optional[List[DetectionCycleItemDTO]] = None


class SamplingTaskQueryDTO(BaseModel):
    """采样任务查询DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    project_quotation_id: Optional[int] = None
    task_name: Optional[str] = None
    task_code: Optional[str] = None
    status: Optional[int] = None
    planned_start_date_from: Optional[date] = None
    planned_start_date_to: Optional[date] = None
    planned_end_date_from: Optional[date] = None
    planned_end_date_to: Optional[date] = None
    sampling_location: Optional[str] = None
    assigned_user_id: Optional[int] = None
    page_num: int = 1
    page_size: int = 10


class SamplingTaskAssignmentDTO(BaseModel):
    """采样任务分配DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    task_name: str
    description: Optional[str] = None
    project_quotation_id: int
    cycle_item_ids: List[int]


