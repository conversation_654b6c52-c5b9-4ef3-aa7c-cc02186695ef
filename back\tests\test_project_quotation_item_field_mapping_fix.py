import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from module_sampling.service.detection_cycle_item_service import DetectionCycleItemService
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO
from datetime import datetime


@pytest.mark.asyncio
class TestProjectQuotationItemFieldMappingFix:
    """测试项目报价明细字段映射修复"""

    async def test_project_quotation_item_field_mapping(self, db_session: AsyncSession):
        """测试ProjectQuotationItem字段正确映射到DetectionCycleItemDTO"""
        # 创建测试数据
        quotation = ProjectQuotation(
            project_name="字段映射测试项目",
            project_code="FIELD001",
            customer_name="字段映射测试客户",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            item_code="ITEM001",
            qualification_code="CMA-2023-001",  # 对应 detection_qualification
            classification="环境检测",  # 对应 detection_classification
            category="水质检测",  # 对应 detection_category
            parameter="pH值",  # 对应 detection_parameter
            method="玻璃电极法",  # 对应 detection_method
            sample_source="地表水",
            point_name="采样点A",
            point_count=1,
            cycle_type="月度",
            cycle_count=12,
            frequency=1,
            sample_count=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试服务层方法
        service = DetectionCycleItemService(db_session)
        result = await service.get_cycle_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(result) == 1
        dto = result[0]
        
        # 验证DTO是DetectionCycleItemDTO类型
        assert isinstance(dto, DetectionCycleItemDTO)
        
        # 验证字段映射正确
        assert dto.detection_qualification == "CMA-2023-001"  # qualification_code -> detection_qualification
        assert dto.detection_classification == "环境检测"  # classification -> detection_classification
        assert dto.detection_category == "水质检测"  # category -> detection_category
        assert dto.detection_parameter == "pH值"  # parameter -> detection_parameter
        assert dto.detection_method == "玻璃电极法"  # method -> detection_method
        assert dto.sample_source == "地表水"
        assert dto.point_name == "采样点A"
        
        # 验证项目名称也正确映射
        assert dto.project_name == "字段映射测试项目"

    async def test_convert_to_dto_with_null_values(self, db_session: AsyncSession):
        """测试字段为空值时的转换"""
        # 创建测试数据（某些字段为空）
        quotation = ProjectQuotation(
            project_name="空值测试项目",
            project_code="NULL001",
            customer_name="空值测试客户",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        quotation_item = ProjectQuotationItem(
            project_quotation_id=quotation.id,
            item_code="ITEM002",
            qualification_code=None,  # 空值
            classification=None,  # 空值
            category="大气检测",  # 必填字段
            parameter="PM2.5",  # 必填字段
            method="重量法",  # 必填字段
            sample_source=None,  # 空值
            point_name=None,  # 空值
            point_count=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation_item)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=quotation_item.id,
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试服务层方法
        service = DetectionCycleItemService(db_session)
        result = await service.get_cycle_items_by_project_quotation_id(quotation.id)
        
        # 验证结果
        assert len(result) == 1
        dto = result[0]
        
        # 验证空值字段正确处理
        assert dto.detection_qualification is None
        assert dto.detection_classification is None
        assert dto.sample_source is None
        assert dto.point_name is None
        
        # 验证非空字段正确映射
        assert dto.detection_category == "大气检测"
        assert dto.detection_parameter == "PM2.5"
        assert dto.detection_method == "重量法"

    async def test_convert_to_dto_without_project_quotation_item(self, db_session: AsyncSession):
        """测试没有关联项目报价明细时的转换"""
        # 创建测试数据（不关联项目报价明细）
        quotation = ProjectQuotation(
            project_name="无明细测试项目",
            project_code="NOITEM001",
            customer_name="无明细测试客户",
            status=1,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(quotation)
        await db_session.flush()
        
        cycle_item = DetectionCycleItem(
            project_quotation_id=quotation.id,
            project_quotation_item_id=999999,  # 不存在的ID
            cycle_number=1,
            status=0,
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(cycle_item)
        await db_session.commit()
        
        # 测试服务层的_convert_to_dto方法
        service = DetectionCycleItemService(db_session)
        
        # 直接测试_convert_to_dto方法
        dto = service._convert_to_dto(cycle_item)
        
        # 验证基本字段正确
        assert dto.id == cycle_item.id
        assert dto.project_quotation_id == quotation.id
        assert dto.cycle_number == 1
        assert dto.status == 0
        
        # 验证关联字段为空（因为没有关联数据）
        assert dto.detection_qualification is None
        assert dto.detection_classification is None
        assert dto.detection_category is None
        assert dto.detection_parameter is None
        assert dto.detection_method is None
        assert dto.sample_source is None
        assert dto.point_name is None

    def test_detection_cycle_item_dto_field_types(self):
        """测试DetectionCycleItemDTO字段类型定义"""
        # 验证DTO类字段定义
        dto_fields = DetectionCycleItemDTO.model_fields
        
        # 验证关键字段存在
        required_fields = [
            'detection_qualification',
            'detection_classification', 
            'detection_category',
            'detection_parameter',
            'detection_method',
            'sample_source',
            'point_name',
            'project_name'
        ]
        
        for field_name in required_fields:
            assert field_name in dto_fields, f"字段 {field_name} 在 DetectionCycleItemDTO 中不存在"
        
        # 创建DTO实例测试
        dto = DetectionCycleItemDTO(
            id=1,
            project_quotation_id=1,
            project_quotation_item_id=1,
            cycle_number=1,
            status=0,
            detection_qualification="CMA-TEST",
            detection_classification="测试分类",
            detection_category="测试类别",
            detection_parameter="测试参数",
            detection_method="测试方法",
            sample_source="测试来源",
            point_name="测试点位",
            project_name="测试项目"
        )
        
        # 验证字段可以正确访问和设置
        assert dto.detection_qualification == "CMA-TEST"
        assert dto.detection_classification == "测试分类"
        assert dto.detection_category == "测试类别"
        assert dto.detection_parameter == "测试参数"
        assert dto.detection_method == "测试方法"
        assert dto.sample_source == "测试来源"
        assert dto.point_name == "测试点位"
        assert dto.project_name == "测试项目"