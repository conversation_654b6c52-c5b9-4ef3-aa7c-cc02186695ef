#!/usr/bin/env python3
"""
测试角色用户接口的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.get_db import get_db
from module_admin.dao.role_dao import RoleDao
from module_admin.service.role_service import RoleService


async def test_role_users_api():
    """
    测试根据角色名称获取用户列表的功能
    """
    print("开始测试角色用户接口...")
    
    # 获取数据库连接
    async for db in get_db():
        try:
            # 测试角色名称
            role_name = "审核人"
            print(f"\n测试角色: {role_name}")
            
            # 测试DAO层
            print("\n1. 测试DAO层...")
            dao_result = await RoleDao.get_users_by_role_name_dao(db, role_name)
            print(f"DAO层返回结果数量: {len(dao_result)}")
            
            if dao_result:
                print("DAO层返回的第一条数据字段:")
                first_user = dao_result[0]
                for key, value in first_user.items():
                    print(f"  {key}: {value} (type: {type(value)})")
                print(f"\n尝试直接创建UserInfoModel:")
                try:
                    from module_admin.entity.vo.user_vo import UserInfoModel
                    test_user = UserInfoModel(**first_user)
                    print(f"创建成功: {test_user}")
                    print(f"user_id: {test_user.user_id}")
                    print(f"user_name: {test_user.user_name}")
                except Exception as e:
                    print(f"创建失败: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("DAO层未返回任何数据")
            
            # 测试Service层
            print("\n2. 测试Service层...")
            service_result = await RoleService.get_users_by_role_name_services(db, role_name)
            print(f"Service层返回结果数量: {len(service_result)}")
            
            if service_result:
                print("Service层返回的第一条数据:")
                first_user_info = service_result[0]
                print(f"  用户ID: {getattr(first_user_info, 'user_id', None)}")
                print(f"  用户名: {getattr(first_user_info, 'user_name', None)}")
                print(f"  昵称: {getattr(first_user_info, 'nick_name', None)}")
                print(f"  邮箱: {getattr(first_user_info, 'email', None)}")
                print(f"  手机号: {getattr(first_user_info, 'phonenumber', None)}")
                print(f"  状态: {getattr(first_user_info, 'status', None)}")
                print(f"  部门ID: {getattr(first_user_info, 'dept_id', None)}")
                print(f"  用户类型: {getattr(first_user_info, 'user_type', None)}")
                print(f"  头像: {getattr(first_user_info, 'avatar', None)}")
                print(f"  删除标志: {getattr(first_user_info, 'del_flag', None)}")
                print(f"  登录IP: {getattr(first_user_info, 'login_ip', None)}")
                print(f"  登录时间: {getattr(first_user_info, 'login_date', None)}")
                print(f"  创建人: {getattr(first_user_info, 'create_by', None)}")
                print(f"  创建时间: {getattr(first_user_info, 'create_time', None)}")
                print(f"  更新人: {getattr(first_user_info, 'update_by', None)}")
                print(f"  更新时间: {getattr(first_user_info, 'update_time', None)}")
                print(f"  备注: {getattr(first_user_info, 'remark', None)}")
                print(f"  对象类型: {type(first_user_info)}")
                print(f"  对象属性: {dir(first_user_info)}")
            else:
                print("Service层未返回任何数据")
            
            # 验证数据一致性
            print("\n3. 验证数据一致性...")
            if len(dao_result) == len(service_result):
                print("✓ DAO层和Service层返回的数据数量一致")
                
                if dao_result and service_result:
                    dao_user = dao_result[0]
                    service_user = service_result[0]
                    
                    # 检查关键字段是否一致
                    print(f"\n调试信息:")
                    print(f"DAO user_id: {dao_user['user_id']} (type: {type(dao_user['user_id'])})")
                    print(f"Service user_id: {service_user.user_id} (type: {type(service_user.user_id)})")
                    print(f"Service对象的所有字段值:")
                    for attr in ['user_id', 'user_name', 'nick_name', 'email', 'status']:
                        value = getattr(service_user, attr, 'NOT_FOUND')
                        print(f"  {attr}: {value} (type: {type(value)})")
                    
                    consistency_checks = [
                        ('user_id', dao_user['user_id'], service_user.user_id),
                        ('user_name', dao_user['user_name'], service_user.user_name),
                        ('nick_name', dao_user['nick_name'], service_user.nick_name),
                        ('email', dao_user['email'], service_user.email),
                        ('status', dao_user['status'], service_user.status)
                    ]
                    
                    all_consistent = True
                    for field_name, dao_value, service_value in consistency_checks:
                        if dao_value == service_value:
                            print(f"✓ {field_name} 字段一致: {dao_value}")
                        else:
                            print(f"✗ {field_name} 字段不一致: DAO={dao_value}, Service={service_value}")
                            all_consistent = False
                    
                    if all_consistent:
                        print("\n✓ 所有关键字段数据一致性验证通过")
                    else:
                        print("\n✗ 数据一致性验证失败")
            else:
                print(f"✗ 数据数量不一致: DAO={len(dao_result)}, Service={len(service_result)}")
            
            print("\n测试完成!")
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            break


if __name__ == "__main__":
    asyncio.run(test_role_users_api())