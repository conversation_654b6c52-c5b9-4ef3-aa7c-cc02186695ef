<template>
  <div class="technical-manual-form">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本信息 -->
      <el-form-item label="类目编号" prop="categoryCode">
        <el-select v-model="form.categoryCode" placeholder="请选择类目">
          <el-option
            v-for="item in categoryOptions"
            :key="item.categoryCode"
            :label="`${item.classification} - ${item.category}`"
            :value="item.categoryCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="检测参数" prop="parameter">
        <el-input v-model="form.parameter" placeholder="请输入检测参数" />
      </el-form-item>

      <el-form-item label="检测方法" prop="method">
        <el-input v-model="form.method" placeholder="请输入检测方法" />
      </el-form-item>

      <!-- 别名输入 - 新的实现方式 -->
      <el-form-item label="别名" prop="aliasList">
        <div class="alias-input-container">
          <!-- 别名输入框 -->
          <div class="alias-input-wrapper">
            <el-input
              v-model="currentAlias"
              placeholder="请输入别名，按回车添加"
              @keyup.enter="addAlias"
              @blur="addAlias"
              clearable
              style="width: 300px"
            />
            <el-button 
              type="primary" 
              @click="addAlias" 
              :disabled="!currentAlias.trim()"
              style="margin-left: 10px"
            >
              添加
            </el-button>
          </div>
          
          <!-- 别名标签显示 -->
          <div class="alias-tags-container" v-if="form.aliasList && form.aliasList.length > 0">
            <el-tag
              v-for="(alias, index) in form.aliasList"
              :key="index"
              closable
              @close="removeAlias(index)"
              style="margin: 5px 5px 0 0"
              type="info"
            >
              {{ alias }}
            </el-tag>
          </div>
          
          <!-- 提示信息 -->
          <div class="alias-hint" v-if="!form.aliasList || form.aliasList.length === 0">
            <span style="color: #999; font-size: 12px;">暂无别名，请在上方输入框中添加</span>
          </div>
        </div>
      </el-form-item>

      <!-- 特殊耗材价格 -->
      <el-form-item label="特殊耗材价格" prop="specialConsumablesPrice">
        <el-input-number
          v-model="form.specialConsumablesPrice"
          :precision="2"
          :min="0"
          :max="999999.99"
          placeholder="请输入特殊耗材价格"
          style="width: 200px"
        />
        <span style="margin-left: 10px; color: #999;">元</span>
      </el-form-item>

      <!-- 其他字段... -->
      <el-form-item label="资质编号" prop="qualificationCode">
        <el-input v-model="form.qualificationCode" placeholder="请输入资质编号" />
      </el-form-item>

      <el-form-item label="限制范围" prop="limitationScope">
        <el-input
          v-model="form.limitationScope"
          type="textarea"
          :rows="3"
          placeholder="请输入限制范围"
        />
      </el-form-item>

      <el-form-item label="取得资质时间" prop="qualificationDate">
        <el-date-picker
          v-model="form.qualificationDate"
          type="date"
          placeholder="请选择取得资质时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item label="是否有资质" prop="hasQualification">
        <el-radio-group v-model="form.hasQualification">
          <el-radio label="0">有</el-radio>
          <el-radio label="1">无</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="0">正常</el-radio>
          <el-radio label="1">停用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TechnicalManualForm',
  props: {
    // 编辑时传入的数据
    editData: {
      type: Object,
      default: () => ({})
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 当前输入的别名
      currentAlias: '',
      
      // 表单数据
      form: {
        categoryCode: '',
        parameter: '',
        method: '',
        aliasList: [], // 别名列表
        specialConsumablesPrice: null, // 特殊耗材价格
        qualificationCode: '',
        limitationScope: '',
        qualificationDate: '',
        hasQualification: '0',
        status: '0',
        remark: ''
      },
      
      // 表单验证规则
      rules: {
        categoryCode: [
          { required: true, message: '请选择类目', trigger: 'change' }
        ],
        parameter: [
          { required: true, message: '请输入检测参数', trigger: 'blur' }
        ],
        method: [
          { required: true, message: '请输入检测方法', trigger: 'blur' }
        ],
        specialConsumablesPrice: [
          { type: 'number', message: '特殊耗材价格必须为数字', trigger: 'blur' }
        ]
      },
      
      // 类目选项
      categoryOptions: []
    }
  },
  
  mounted() {
    this.loadCategoryOptions()
    if (this.isEdit && this.editData) {
      this.loadEditData()
    }
  },
  
  methods: {
    // 加载类目选项
    async loadCategoryOptions() {
      try {
        const response = await this.$api.get('/api/basedata/technical-manual-category/select/list')
        if (response.code === 200) {
          this.categoryOptions = response.data
        }
      } catch (error) {
        this.$message.error('加载类目选项失败')
      }
    },
    
    // 加载编辑数据
    loadEditData() {
      Object.keys(this.form).forEach(key => {
        if (this.editData[key] !== undefined) {
          this.form[key] = this.editData[key]
        }
      })
      
      // 特殊处理别名列表
      if (this.editData.aliasList && Array.isArray(this.editData.aliasList)) {
        this.form.aliasList = [...this.editData.aliasList]
      }
    },
    
    // 添加别名
    addAlias() {
      const alias = this.currentAlias.trim()
      if (!alias) return
      
      // 检查是否已存在
      if (this.form.aliasList.includes(alias)) {
        this.$message.warning('该别名已存在')
        return
      }
      
      // 添加到列表
      this.form.aliasList.push(alias)
      this.currentAlias = ''
      
      this.$message.success('别名添加成功')
    },
    
    // 删除别名
    removeAlias(index) {
      this.form.aliasList.splice(index, 1)
      this.$message.success('别名删除成功')
    },
    
    // 提交表单
    async submitForm() {
      try {
        await this.$refs.formRef.validate()
        
        const formData = { ...this.form }
        
        // 如果是编辑模式，添加ID
        if (this.isEdit && this.editData.id) {
          formData.id = this.editData.id
        }
        
        const url = this.isEdit 
          ? '/api/basedata/technical-manual' 
          : '/api/basedata/technical-manual'
        const method = this.isEdit ? 'put' : 'post'
        
        const response = await this.$api[method](url, formData)
        
        if (response.code === 200) {
          this.$message.success(this.isEdit ? '修改成功' : '新增成功')
          this.$emit('success', response.data)
        } else {
          this.$message.error(response.msg || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('操作失败')
      }
    },
    
    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
      this.form.aliasList = []
      this.currentAlias = ''
    }
  }
}
</script>

<style scoped>
.technical-manual-form {
  padding: 20px;
}

.alias-input-container {
  width: 100%;
}

.alias-input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.alias-tags-container {
  min-height: 32px;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

.alias-hint {
  min-height: 32px;
  padding: 8px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  display: flex;
  align-items: center;
}

.el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
