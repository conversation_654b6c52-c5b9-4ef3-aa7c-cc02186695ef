"""
项目报价审批API接口测试
"""

import pytest
import json
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_quotation.entity.do.project_quotation_do import ProjectQuotation


class TestApprovalAPI:
    """
    审批流程API测试类
    """

    @pytest.fixture
    async def setup_test_data(self, db: AsyncSession):
        """
        设置测试数据
        """
        # 创建测试角色
        market_role = SysRole(
            role_id=1,
            role_name="市场审批人员",
            role_key="market-approver",
            role_sort=10,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_role = SysRole(
            role_id=2,
            role_name="实验室审批人员", 
            role_key="lab-approver",
            role_sort=11,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_role = SysRole(
            role_id=3,
            role_name="现场审批人员",
            role_key="field-approver", 
            role_sort=12,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([market_role, lab_role, field_role])
        await db.flush()

        # 创建测试用户
        market_user = SysUser(
            user_id=1,
            user_name="market_user",
            nick_name="市场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_user = SysUser(
            user_id=2,
            user_name="lab_user",
            nick_name="实验室用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_user = SysUser(
            user_id=3,
            user_name="field_user",
            nick_name="现场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([market_user, lab_user, field_user])
        await db.flush()

        # 创建用户角色关联
        user_roles = [
            SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
            SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
            SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
        ]
        db.add_all(user_roles)

        # 创建测试项目报价
        quotation = ProjectQuotation(
            id=1,
            project_name="测试项目",
            project_code="TEST001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        db.add(quotation)
        await db.flush()

        await db.commit()

        return {
            "quotation": quotation,
            "market_user": market_user,
            "lab_user": lab_user,
            "field_user": field_user,
            "market_role": market_role,
            "lab_role": lab_role,
            "field_role": field_role
        }

    @pytest.mark.asyncio
    async def test_init_approval_records_api(self, client: TestClient, setup_test_data):
        """
        测试初始化审批记录API
        """
        test_data = await setup_test_data
        
        # 模拟登录用户
        headers = {"Authorization": "Bearer test_token"}
        
        # 测试初始化审批记录
        response = client.post(
            "/quotation/approval/init/1?business_type=sampling",
            headers=headers
        )
        
        # 由于没有实际的认证系统，这里主要测试接口是否存在
        # 实际的业务逻辑测试在service层进行
        assert response.status_code in [200, 401, 403]

    @pytest.mark.asyncio
    async def test_get_approval_status_api(self, client: TestClient, setup_test_data):
        """
        测试获取审批状态API
        """
        test_data = await setup_test_data
        headers = {"Authorization": "Bearer test_token"}
        
        # 获取审批状态
        response = client.get(
            "/quotation/approval/status/1",
            headers=headers
        )
        
        assert response.status_code in [200, 401, 403]

    @pytest.mark.asyncio
    async def test_submit_for_approval_api(self, client: TestClient, setup_test_data):
        """
        测试提交审批API
        """
        test_data = await setup_test_data
        headers = {"Authorization": "Bearer test_token"}
        
        # 提交审批
        response = client.post(
            "/quotation/approval/submit/1",
            headers=headers
        )
        
        assert response.status_code in [200, 401, 403]

    @pytest.mark.asyncio
    async def test_perform_approval_api(self, client: TestClient, setup_test_data):
        """
        测试执行审批操作API
        """
        test_data = await setup_test_data
        headers = {"Authorization": "Bearer test_token"}
        
        # 执行审批操作
        approval_data = {
            "projectQuotationId": 1,
            "approvalStatus": "approved",
            "approvalOpinion": "测试审批通过"
        }
        
        response = client.post(
            "/quotation/approval/approve/1",
            headers=headers,
            json=approval_data
        )
        
        assert response.status_code in [200, 401, 403]

    @pytest.mark.asyncio
    async def test_get_pending_approvals_api(self, client: TestClient, setup_test_data):
        """
        测试获取待审批项目列表API
        """
        test_data = await setup_test_data
        headers = {"Authorization": "Bearer test_token"}
        
        # 获取待审批列表
        response = client.get(
            "/quotation/approval/pending",
            headers=headers
        )
        
        assert response.status_code in [200, 401, 403]

    @pytest.mark.asyncio
    async def test_api_endpoints_exist(self, client: TestClient):
        """
        测试所有审批相关的API端点是否存在
        """
        # 测试所有审批相关的端点
        endpoints = [
            ("GET", "/quotation/approval/status/1"),
            ("POST", "/quotation/approval/submit/1"),
            ("POST", "/quotation/approval/approve/1"),
            ("GET", "/quotation/approval/pending"),
            ("POST", "/quotation/approval/init/1")
        ]
        
        for method, endpoint in endpoints:
            if method == "GET":
                response = client.get(endpoint)
            else:
                response = client.post(endpoint, json={})
            
            # 确保端点存在（不是404）
            assert response.status_code != 404, f"端点 {method} {endpoint} 不存在"

    def test_api_response_format(self, client: TestClient):
        """
        测试API响应格式
        """
        # 测试一个简单的端点
        response = client.get("/quotation/approval/status/1")
        
        # 即使没有权限，也应该返回JSON格式
        if response.status_code == 200:
            data = response.json()
            assert "code" in data
            assert "message" in data
        elif response.status_code in [401, 403]:
            # 权限错误也应该是JSON格式
            try:
                data = response.json()
                assert isinstance(data, dict)
            except:
                # 如果不是JSON，至少应该有合理的错误响应
                assert response.text is not None
