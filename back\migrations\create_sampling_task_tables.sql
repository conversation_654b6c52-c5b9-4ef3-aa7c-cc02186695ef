-- 采样任务功能相关表创建脚本
-- 创建时间: 2024-01-01
-- 描述: 为采样任务功能创建必要的数据库表结构

-- 1. 创建检测周期条目表
CREATE TABLE IF NOT EXISTS detection_cycle_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    project_quotation_id BIGINT NOT NULL COMMENT '项目报价ID',
    project_quotation_item_id BIGINT NOT NULL COMMENT '项目报价明细ID',
    cycle_number INT NOT NULL COMMENT '周期序号（1,2,3...）',
    status TINYINT DEFAULT 0 COMMENT '状态：0-未分配，1-已分配，2-已完成',
    create_by BIGINT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_quotation_id (project_quotation_id),
    INDEX idx_project_quotation_item_id (project_quotation_item_id),
    INDEX idx_status (status),
    UNIQUE KEY uk_quotation_item_cycle (project_quotation_item_id, cycle_number),
    FOREIGN KEY (project_quotation_id) REFERENCES project_quotation(id) ON DELETE CASCADE,
    FOREIGN KEY (project_quotation_item_id) REFERENCES project_quotation_item(id) ON DELETE CASCADE,
    FOREIGN KEY (create_by) REFERENCES sys_user(user_id),
    FOREIGN KEY (update_by) REFERENCES sys_user(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测周期条目表';

-- 2. 创建采样任务表
CREATE TABLE IF NOT EXISTS sampling_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_code VARCHAR(100) UNIQUE NOT NULL COMMENT '任务编号',
    project_quotation_id BIGINT NOT NULL COMMENT '项目报价ID',
    description TEXT COMMENT '任务描述',
    assigned_user_id BIGINT COMMENT '分配给的用户ID',
    status TINYINT DEFAULT 0 COMMENT '任务状态：0-待执行，1-执行中，2-已完成',
    planned_start_date DATE COMMENT '计划开始日期',
    planned_end_date DATE COMMENT '计划结束日期',
    actual_start_date DATE COMMENT '实际开始日期',
    actual_end_date DATE COMMENT '实际结束日期',
    create_by BIGINT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_quotation_id (project_quotation_id),
    INDEX idx_assigned_user_id (assigned_user_id),
    INDEX idx_status (status),
    INDEX idx_task_code (task_code),
    FOREIGN KEY (project_quotation_id) REFERENCES project_quotation(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_user_id) REFERENCES sys_user(user_id),
    FOREIGN KEY (create_by) REFERENCES sys_user(user_id),
    FOREIGN KEY (update_by) REFERENCES sys_user(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务表';

-- 3. 创建采样任务与检测周期关联表
CREATE TABLE IF NOT EXISTS sampling_task_cycle_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sampling_task_id BIGINT NOT NULL COMMENT '采样任务ID',
    detection_cycle_item_id BIGINT NOT NULL COMMENT '检测周期条目ID',
    create_by BIGINT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_task_cycle (sampling_task_id, detection_cycle_item_id),
    INDEX idx_sampling_task_id (sampling_task_id),
    INDEX idx_detection_cycle_item_id (detection_cycle_item_id),
    FOREIGN KEY (sampling_task_id) REFERENCES sampling_task(id) ON DELETE CASCADE,
    FOREIGN KEY (detection_cycle_item_id) REFERENCES detection_cycle_item(id) ON DELETE CASCADE,
    FOREIGN KEY (create_by) REFERENCES sys_user(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务与检测周期关联表';

-- 4. 创建任务编号序列表（用于生成唯一任务编号）
CREATE TABLE IF NOT EXISTS sampling_task_sequence (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    year_month VARCHAR(6) NOT NULL COMMENT '年月（YYYYMM）',
    sequence_number INT DEFAULT 0 COMMENT '当月序号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_year_month (year_month)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务编号序列表';

-- 插入初始数据
INSERT INTO sampling_task_sequence (year_month, sequence_number) 
VALUES (DATE_FORMAT(NOW(), '%Y%m'), 0) 
ON DUPLICATE KEY UPDATE sequence_number = sequence_number;

-- 添加注释
ALTER TABLE detection_cycle_item COMMENT = '检测周期条目表：存储项目报价明细对应的检测周期信息';
ALTER TABLE sampling_task COMMENT = '采样任务表：存储采样任务的基本信息';
ALTER TABLE sampling_task_cycle_item COMMENT = '采样任务与检测周期关联表：实现采样任务与检测周期的多对多关系';
ALTER TABLE sampling_task_sequence COMMENT = '采样任务编号序列表：用于生成唯一的任务编号';