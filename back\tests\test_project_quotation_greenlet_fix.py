"""测试项目报价服务的 MissingGreenlet 错误修复"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import MissingGreenlet
from sqlalchemy import select

from module_quotation.service.project_quotation_service import ProjectQuotationService
from module_quotation.entity.do.project_quotation_do import ProjectQuotation


class TestProjectQuotationGreenletFix:
    """测试项目报价 MissingGreenlet 错误修复"""
    
    @pytest.mark.asyncio
    async def test_direct_sqlalchemy_query_no_greenlet_error(self, db_session: AsyncSession):
        """测试直接SQLAlchemy查询不会出现 MissingGreenlet 错误"""
        try:
            # 直接使用SQLAlchemy查询，这应该不会抛出 MissingGreenlet 错误
            stmt = select(ProjectQuotation).where(ProjectQuotation.id == 39)
            result = await db_session.execute(stmt)
            quotation = result.scalar_one_or_none()
            
            # 验证查询成功（可能返回 None）
            assert quotation is None or hasattr(quotation, 'id')
            
        except MissingGreenlet as e:
            # 如果出现 MissingGreenlet 错误，测试失败
            pytest.fail(f"MissingGreenlet error occurred: {e}")
        except Exception as e:
            # 其他错误可能是正常的（比如数据不存在等）
            if "MissingGreenlet" in str(e):
                pytest.fail(f"MissingGreenlet error occurred: {e}")
            # 其他错误不影响测试
            pass
    
    @pytest.mark.asyncio
    async def test_project_quotation_service_get_by_id_no_greenlet_error(self, db_session: AsyncSession):
        """测试项目报价服务get_by_id方法不会出现 MissingGreenlet 错误"""
        # 创建项目报价服务
        project_quotation_service = ProjectQuotationService(db_session)
        
        try:
            # 测试get_by_id方法
            quotation = await project_quotation_service.get_by_id(39)
            
            # 验证查询成功（可能返回 None）
            assert quotation is None or hasattr(quotation, 'id')
            
        except MissingGreenlet as e:
            pytest.fail(f"MissingGreenlet error in get_by_id: {e}")
        except Exception as e:
            if "MissingGreenlet" in str(e):
                pytest.fail(f"MissingGreenlet error in get_by_id: {e}")
            # 其他错误不影响测试
            pass