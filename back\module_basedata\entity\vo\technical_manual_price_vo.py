from datetime import datetime
from typing import List, Optional, Literal
from decimal import Decimal

from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank, Size

from module_admin.annotation.pydantic_annotation import as_query


class TechnicalManualPriceModel(BaseModel):
    """
    技术手册价格模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description="主键ID")

    # 唯一标识字段
    method: str = Field(..., description="检测方法", max_length=100)
    category_code: str = Field(..., description="类目编号", max_length=20)
    # 关联查询字段（用于显示）
    category: Optional[str] = Field(default=None, description="检测类别", max_length=50)
    classification: Optional[str] = Field(default=None, description="分类", max_length=50)

    # 检测价格相关字段
    first_item_price: Optional[Decimal] = Field(default=None, description="检测首项单价")
    additional_item_price: Optional[Decimal] = Field(default=None, description="检测增项单价")
    testing_fee_limit: Optional[Decimal] = Field(default=None, description="检测费上限")

    # 采集价格相关字段
    sampling_price: Optional[Decimal] = Field(default=None, description="采集单价")
    pretreatment_price: Optional[Decimal] = Field(default=None, description="前处理单价")

    # 其他价格相关字段
    # special_consumables_price: Optional[Decimal] = Field(default=None, description="分析特殊耗材单价")

    # 系统字段
    status: Optional[Literal["0", "1"]] = Field(default="0", description="状态（0正常 1停用）")
    create_by: Optional[str] = Field(default=None, description="创建者")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新者")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")
    remark: Optional[str] = Field(default=None, description="备注", max_length=500)

    @NotBlank(field_name="method", message="检测方法不能为空")
    def get_method(self):
        return self.method

    @NotBlank(field_name="category_code", message="类目编号不能为空")
    def get_category_code(self):
        return self.category_code

    def validate_fields(self):
        self.get_method()
        self.get_category_code()


class AddTechnicalManualPriceModel(TechnicalManualPriceModel):
    """
    新增技术手册价格模型
    """

    id: Optional[int] = Field(default=None, exclude=True, description="主键ID")


class EditTechnicalManualPriceModel(TechnicalManualPriceModel):
    """
    编辑技术手册价格模型
    """

    id: int = Field(..., description="主键ID")


@as_query
class TechnicalManualPriceQueryModel(BaseModel):
    """
    技术手册价格查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    method: Optional[str] = Field(default=None, description="检测方法")
    category_code: Optional[str] = Field(default=None, description="类目编号")
    # 支持按分类和检测类别查询（会转换为类目编号）
    category: Optional[str] = Field(default=None, description="检测类别")
    classification: Optional[str] = Field(default=None, description="分类")
    keyword: Optional[str] = Field(default=None, description="关键字")
    begin_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")


@as_query
class TechnicalManualPricePageQueryModel(TechnicalManualPriceQueryModel):
    """
    技术手册价格分页查询模型
    """

    page_num: int = Field(default=1, description="页码")
    page_size: int = Field(default=10, description="每页数量")


class MethodCategoryOptionModel(BaseModel):
    """
    检测方法和类别选项模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    method: str = Field(..., description="检测方法")
    categories: List[dict] = Field(..., description="检测类别列表，包含category和classification字段")
